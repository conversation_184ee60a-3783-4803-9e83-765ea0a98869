<template>
  <div>
    <el-upload
      ref="uploadCom"
      :action="idCardCollect"
      list-type="picture-card"
      :on-preview="handlePictureCardPreview"
      :on-success="handleSuccess"
      :headers="uploadHeaders"
      :multiple="false"
      name="image"
      :limit="1"
      :data="{ memberId, type }"
      :file-list="imgArr"
      :before-upload="handleBeforeUpload"
    >
      <i class="el-icon-plus" />
    </el-upload>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="imgServer + picture" alt>
    </el-dialog>
  </div>
</template>

<script>
import { doUpload, deleteByPath } from '@/api/index'
import { getToken } from '@/utils/auth'
import { imgServer } from '@/api/config'
import { idCardCollect } from '@/api/allinPayMember'

export default {
  props: {
    memberData: {
      type: Object,
      default: null
    },
    // 1 营业执照 2组织机构代码 3税务登记证 4银行开户证明 5机构信用代码 6icp备案许可 7行业许可证 8身份证正面 9身份证反面
    type: {
      type: Number,
      default: 1
    },
    img: {
      type: String,
      default: null
    }

  },
  data() {
    return {
      doUpload,
      uploadHeaders: {
        Authorization: getToken()
      },
      picture: null,
      dialogVisible: false,
      imgServer,
      imgArr: [],
      idCardCollect,
      memberId: null

    }
  },
  watch: {
    img(n) {
      console.log(n)
      if (n) {
        console.log(n)
        this.picture = n
        // this.imgArr = [{ url: imgServer + n }]
      } else {
        this.imgArr = []
      }
    },
    memberData(n) {
      if (n) {
        this.memberId = n.id
      }
      console.log(n, '=================')
    }
  },
  mounted() {
    if (this.memberData) {
      this.memberId = this.memberData.id
    }
    console.log('mounted', this.memberData)
    // console.log(this.img)
  },
  methods: {
    initFileList() {
      if (this.img) {
        if (this.img.indexOf('http') === -1) {
          this.picture = imgServer + this.img
          this.imgArr = [{ url: imgServer + this.img }]
        } else {
          this.picture = this.img
          this.imgArr = [{ url: this.img }]
        }
      }
    },
    handlePictureCardPreview() {
      this.dialogVisible = true
    },
    async handleRemove(file) {
      if (file.status === 'success') {
        return this.$confirm(`此操作将永久删除该文件, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(res => {
          const path = this.img
          deleteByPath({ path }).then(res => {
            this.$message({
              message: res.msg,
              type: 'success'
            })
            this.picture = null
            this.$emit('getUrl', this.picture)
          })
        })
      }
    },
    handleSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$eltool.errorMsg(response.msg)
        return
      } else {
        this.$eltool.successMsg(response.msg)
        this.picture = response.data[0].fileUrl
        this.$emit('getUrl', this.picture)
      }
    },
    handleBeforeUpload() {
    }

  }

}
</script>

<style lang="scss" scoped>
</style>

