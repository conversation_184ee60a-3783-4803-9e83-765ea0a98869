/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 健康保护神-葫芦娃配置
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/patronsaintgourdconfig/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/patronsaintgourdconfig/query/page`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/patronsaintgourdconfig/query/list`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/patronsaintgourdconfig/update`, data)
}

// 根据主键单一查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/patronsaintgourdconfig/query/one`, data)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/patronsaintgourdconfig/delete/one/${data.id}`)
}