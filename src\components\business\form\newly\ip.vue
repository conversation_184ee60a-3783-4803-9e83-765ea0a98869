<!-- ip -->
<template>
  <el-form-item
    :label="config.label"
    :label-width="config.width"
    :rules="rules"
    :class="itemClass"
    :prop="config.name">
    <el-input
      :disabled="disabled"
      v-model="form.data.input"
      :class="childClass"
      :placeholder="config.placeholder"
      type="text"
    />
  </el-form-item>
</template>

<script>

var checkNumber = (rule, value, callback) => {
  var obj = checkFn(value)
  if (!obj.flag) {
    callback(new Error(obj.msg))
    return
  } else {
    callback()
  }
}
/**
 * 判断ip是否正确
 * flag true 验证通过 false 验证失败
 */
function checkFn(val) {
  const obj = {
    flag: true,
    msg: ''
  }
  // ip的验证（开头不能为0，共6位）
  if (!isNull(val)) {
    const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
    if (!reg.test(val)) {
      obj.msg = 'ip格式不正确'
      obj.flag = false
    }
  } else {
    obj.msg = 'ip不能为空'
    obj.flag = false
  }
  return obj
}

/**
 * 判断是否为空
 */
function isNull(val) {
  if (val instanceof Array) {
    if (val.length === 0) return true
  } else if (val instanceof Object) {
    if (JSON.stringify(val) === '{}') return true
  } else {
    if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '') return true
    return false
  }
  return false
}

export default {
  name: 'PostCode',
  props: {
    // el-form-item的class值
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    // 是否可编辑
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    // el-input的class值
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    // 组件循环时需要的idx值
    idx: {
      type: Number,
      default: 0
    },
    // 参数配置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '文本输入',
          name: 'text',
          placeholder: '请填写文本输入',
          rules: [
            { required: true, message: '请输入文本输入', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      rules: [],
      form: {
        data: {
          input: ''
        }
      }
    }
  },
  watch: {
    config(val) {
      this.rules = val.rules
    },
    data: {
      handler(val) {
        this.form.data.input = val
      },
      deep: true
    },
    // 监听到form的数据变化则把config.name和form.data.input和idx传到父组件
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.input, this.idx)
      },
      deep: true
    }
  },
  created() {
    this.rules = this.config.rules
    if (this.rules.length > 0 && this.rules[0].required) {
      const rulesObj = { validator: checkNumber, trigger: 'blur' }
      this.rules.push(rulesObj)
    }
  },
  methods: {
    checkNumber() {
      console.log('checkNumber: ', this.form.data.input)
    }
  }
}
</script>
