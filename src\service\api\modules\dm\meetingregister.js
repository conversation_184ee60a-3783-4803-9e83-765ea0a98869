/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 活体检测记录
 */

// 根据ids批量删除
export function deleteBatch (data) {
  return requestV1.deleteForm(`${prefix}/meetingregister/delete/batch/${data.ids}`);
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/meetingregister/insert`, data)
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/meetingregister/query/page`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/meetingregister/update`, data)
}

// 根据主键单一查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/meetingregister/query/one`, data)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/meetingregister/delete/one/${data.id}`)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/meetingregister/query/list`, data)
}

// 批量审核
export function batchAudit(data) {
  return requestV1.postJson(`${prefix}/meetingregister/batch/audit`, data)
}
