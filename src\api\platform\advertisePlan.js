/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'

/**
 *  
 */
// 广告页投放名称
export function listAllAdvertiseList(data = {}) {
  return requestV1.get(`${prefix}/advertisePlan/listAllAdvertise`,data)
}

// 广告页投放展示次数统计
export function advertisePlanDisplayQueryDisplayTotal(data = {}) {
  return requestV1.postJson(`${prefix}/advertisePlanDisplay/queryDisplayTotal`,data)
}

//  广告页投放记录列表 
export function advertisePlanDisplayQueryPage(data = {}) {
  return requestV1.postJson(`${prefix}/advertisePlanDisplay/queryPage`,data)
}

// 导出列表 
export function advertisePlanDisplayAdPlanDisplayExport(data = {},fileName='广告投放记录列表.xlsx') {
  return requestV1.download(`${prefix}/advertisePlanDisplay/adPlanDisplayExport`,data,fileName,'post',true,{},true)
}
