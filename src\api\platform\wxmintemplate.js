// wxmintemplate

/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 * 投放计划管理-订阅消息模板
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/wxmintemplate/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/wxmintemplate/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/wxmintemplate/insert`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/wxmintemplate/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/wxmintemplate/query/page`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/wxmintemplate/update`, data)
}

// 列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/wxmintemplate/query/list`, data);
}

// api/v1/wxmintemplate/template/sync/{authId} 同步消息
export function syncWxmintemplate(data) {
  return requestV1.get(`${prefix}/wxmintemplate/template/sync/${data.authId}`, data);
}

// 查询模板消息推送表格分页数据
export function templatePosterQueryPage(data) {
  return requestV1.postJson(`/manage/api/templatePoster/queryPage`, data);
}

