<template slot-scope="scope">
  <div class="image-main">
    <el-image :src="imgServer+imgName+reduce" :style="{height:'30px'}"
              :preview-src-list="[imgServer+imgName]"
              lazy>
    </el-image>
  </div>
</template>

<script>
  import {imgServer} from '@/api/config'

  export default {
    name: "showImgInList",
    props: {
      imgName: String,
      imgHeight: {
        type: String,
        default: '20px'
      },
      // srcList: Array
    },
    mounted() {
      this.imgServer = imgServer;
    },
    watch: {},
    data() {
      return {
        reduce: '?x-oss-process=image/resize,m_lfit,h_200,w_200', //缩略图拼接
        imgServer: '',
        srcList: []
      }
    }
  }
</script>

<style scoped>
  .image-main {
    width: 35px;
  }
</style>
