/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/auth/api/v1'

//登录
export function login(data) {
    return requestV1.postForm(prefix + '/login', data);
}

//登录
export function logout(data) {
    return requestV1.postForm(prefix + '/logout', data);
}

//踢下线功能
export function kickout(data) {
    return requestV1.postForm(prefix + '/kickout', data);
}

// 手机验证码登录
export function codeLogin(data) {
  return requestV1.postJson(prefix + '/user/code/login', data);
}

// 滑块验证
export function verifyCaptcha(data) {
  return requestV1.postJson(prefix + '/login/verify/captcha', data);
}

//登录
export function captchaLogin(data) {
  return requestV1.postJson(prefix + '/captcha/login', data);
}

//登录
export function captchaCodeLogin(data) {
  return requestV1.postJson(prefix + '/captcha/code/login', data);
}
