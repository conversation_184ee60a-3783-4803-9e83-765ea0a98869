<template>
  <div
    :id="uuid"
    class="report-content-box"
    :class="{
      'user-activity-project-report': inType === 'user-activity-project',
    }"
    v-loading="pageLoading"
  >
    <div class="pageContent">
      <template v-for="(page, index) in pageContent">
        <div
          class="everyPage"
          :key="index"
          :style="{
            width: pageSize.width + 'px',
            height: pageSize.height + 'px',
          }"
          v-if="!page.authHeight"
        >
          <div
            class="everyPageContent"
            :class="{
              'user-activity-project-report':
                inType === 'user-activity-project',
            }"
          >
            <div
              class="settlement-page border-l border-r"
              v-if="page.type === 'allContent'"
            >
              <template v-if="page.once && inType !== 'user-activity-project'">
                <div class="page-title">
                  {{ pageTitle }}
                </div>
                <div class="page-time">执行时间：{{ cutTime }}</div>
                <div class="page-content">
                  为了进一步提升{{ tenantName
                  }}<template v-if="businessType === 16"
                    >旗下小葫芦平台的用户数量、活跃度和互动率，我们组织了线上推广团队，通过精准定位目标用户群体，利用微信生态内的强大社交属性，引导潜在用户高频参与内容点赞、评论等社交互动，构建用户与平台的情感联结，持续提升小葫芦平台的活跃度并增强用户粘性。 </template
                  ><template v-else-if="businessType === 17"
                    >旗下小葫芦平台的用户数量、活跃度和互动率，我们组织了推广团队，在全国范围的医院、药店等人流密集的场景中开展了线下用户活动，通过引导潜在用户高频参与内容点赞、评论等社交互动，构建用户与平台的情感联结，持续提升小葫芦平台的活跃度并增强用户粘性。
                  </template>
                </div>
                <!-- <div class="page-content-tip">
                  {{ tenantName2 }}根据拉新注册用户的数量，给推广团队进行结算。
                </div> -->
                <div
                  class="page-content-table"
                  :style="{
                    width: productTableWidth + 'px',
                    zoom: productTableWidthScale,
                  }"
                >
                  <el-table :data="productTable" border style="width: 100%">
                    <template v-for="cols in productHeaders">
                      <el-table-column
                        :key="cols.prop"
                        :prop="cols.prop"
                        :label="cols.label"
                        :width="cols.width"
                        :isReportTable="true"
                      >
                      </el-table-column>
                    </template>
                  </el-table>
                </div>
              </template>
              <div class="table-box">
                <template v-if="page.once">
                  <template v-if="inType === 'user-activity-project'">
                    <div class="activity-page-h2">
                      <img :src="inViteRegisonUrl" class="title-icon" alt="" />
                      平台邀请注册明细
                      <template v-if="inviteTotalCount >= 500">
                        （仅展示{{ 500 }}条）
                      </template>
                      <template v-else>
                        （共{{ inviteTotalCount }}条）
                      </template>
                    </div>
                  </template>
                  <template v-else>
                    <div class="page-h2">
                      一、小葫芦平台邀请注册明细 （共{{ inviteTotalCount }}条）
                    </div>
                  </template>
                </template>
                <div :class="page.computed ? 'computed' : ''" :id="page.uuid">
                  <div
                    :style="{
                      width: inviteHeaderWidth + 'px',
                      zoom: inviteHeaderScale,
                    }"
                  >
                    <el-table
                      :data="page.children"
                      style="width: 100%"
                      header-row-class-name="header-row-class-name"
                      border
                    >
                      <template v-for="cols in inviteHeaders">
                        <el-table-column
                          :key="cols.prop"
                          :prop="cols.prop"
                          :label="cols.label"
                          :width="cols.width"
                          :isReportTable="true"
                        >
                        </el-table-column>
                      </template>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="settlement-page border-l border-r"
              v-else-if="page.type === 'pinlun'"
            >
              <div class="table-box">
                <template v-if="page.once">
                  <template v-if="inType === 'user-activity-project'">
                    <div class="activity-page-h2">
                      <img :src="inViteRegisonUrl" class="title-icon" alt="" />
                      帖子评论明细
                      <template v-if="page.total >= 500">
                        （仅展示{{ 500 }}条）
                      </template>
                      <template v-else>
                        （共{{ page.total }}条）
                      </template>
                    </div>
                  </template>
                  <template v-else>
                    <div class="page-h3" >
                      三、KPI 帖子评论明细
                      <template v-if="page.total >= 500">
                        （仅展示{{ 500 }}条）
                      </template>
                      <template v-else> （共{{ page.total }}条） </template>
                    </div>
                  </template>
                </template>
                <div :class="page.computed ? 'computed' : ''" :id="page.uuid">
                  <div
                    :style="{
                      width: commonHeadersWidth + 'px',
                      zoom: commonHeadersScale,
                    }"
                  >
                    <el-table
                      :data="page.children"
                      style="width: 100%"
                      header-row-class-name="header-row-class-name"
                      border
                    >
                      <template v-for="cols in commonHeaders">
                        <el-table-column
                          :key="cols.prop"
                          :prop="cols.prop"
                          :label="cols.label"
                          :width="cols.width"
                          :isReportTable="true"
                        >
                        </el-table-column>
                      </template>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="settlement-page border-l border-r"
              v-else-if="page.type === 'dianzan'"
            >
              <div class="table-box">
                <template v-if="page.once">
                  <template v-if="inType === 'user-activity-project'">
                    <div class="activity-page-h2">
                      <img :src="inViteRegisonUrl" class="title-icon" alt="" />
                      帖子点赞明细
                      <template v-if="page.total >= 500">
                        （仅展示{{ 500 }}条）
                      </template>
                      <template v-else>
                        （共{{ page.total }}条）
                      </template>
                    </div>
                  </template>
                  <template v-else>
                    <div class="page-h3">
                      二、KPI 帖子点赞明细
                      <template v-if="page.total >= 500">
                        （仅展示{{ 500 }}条）
                      </template>
                      <template v-else> （共{{ page.total }}条） </template>
                    </div>
                  </template>
                </template>
                <div :class="page.computed ? 'computed' : ''" :id="page.uuid">
                  <div
                    :style="{
                      width: serveHeadersWidth + 'px',
                      zoom: serveHeadersWidthScale,
                    }"
                  >
                    <el-table
                      :data="page.children"
                      style="width: 100%"
                      header-row-class-name="header-row-class-name"
                      border
                    >
                      <template v-for="cols in userVisitCountVosHeader">
                        <el-table-column
                          :key="cols.prop"
                          :prop="cols.prop"
                          :label="cols.label"
                          :width="cols.width"
                          :isReportTable="true"
                        ></el-table-column>
                      </template>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          :key="index"
          v-else-if="page.authHeight"
          :style="{
            width: pageSize.width + 'px',
            height: page.targetHeight ? page.targetHeight + 'px' : 'auto',
          }"
          :id="authHeightResult[index]"
        >
          <div class="everyPageContent">
            <div
              class="settlement-page"
              v-if="page.type === 'executiveTeamPresentation'"
            >
              <div class="page-h3" v-if="!page.noTitleVisible">
                四、执行现场
              </div>
              <div class="activity-image-box">
                <template
                  v-for="item in signInLogListImage.slice(page.start, page.end)"
                >
                  <div :key="item.url" class="activity-flex1">
                    <div class="activity-image-b">
                      <img :src="item.url" class="activity-image" alt="" />
                    </div>
                  </div>
                </template>
                <template v-if="signInLogListImage.length === 0">
                  <div class="nodate">暂无数据</div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { format, formFenToYuan, domainURL } from "@/utils/index";
import { todotasksGetReportPersonal } from "@/api/todotasks.js";
import { useractivityinvitelogQueryPageV2 as useractivityinvitelogQueryPage } from "@/service/api/modules/common-dm/applicationoperatelog.js";
import { queryPage as queryPageLogPage } from "@/service/api/modules/common-dm/operation-user-list.js";
import { commentQueryNotenantPageV2 as queryCommonPage } from "@/api/dmCommunity/comment";
import {
  getDmCommunityCommentLikeFlowType,
  getDmCommunityPostsType,
  getDmCommunityProcessStatus,
  getDmCommunityPutawayStatus,
} from "@/utils/enumeration";
import { queryOne as queryTodoTaskOne } from "@/api/todotasks";

export default {
  props: {
    taskId: {
      type: [Number, String],
      default: null,
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    filename: {
      type: String,
      default: "",
    },
    uuid: {
      type: String,
      default: "user-activity-personal-report",
    },
    businessType: {
      type: Number,
      default: 16,
    },
    inType: {
      type: String,
      default: "default",
    },
    levelParams: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      inViteRegisonUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/7.png",
      inviteTotalCount: 0,
      signInLogListImage: [],
      userActivityItemId: null,
      userActivityUserId: null,
      userActivityId: null,
      oneInit: false,
      totalCount: 0,
      commonHeaders: [
        // { prop: "accountId", label: "唯一用户ID", width: 180 },
        { prop: "nickName", label: "用户昵称", width: 240 },
        { prop: "title", label: "帖子标题", width: 320 },
        { prop: "putawayTimeText", label: "发布时间", width: 180 },
        { prop: "content", label: "评论内容", width: 380 },
        // { prop: "typeText", label: "评论类型", width: 100 },
        // { prop: "processResult", label: "审核结果", width: 100 },
      ],
      commonHeadersWidth: 0,
      commonHeadersScale: 1,
      noNextPage: false,
      inviteHeaders: [
        { prop: "nickName", label: "用户昵称", width: 180 },
        { prop: "inviteUserName", label: "邀请人", width: 100 },
        { prop: "beAccountId", label: "被邀请人用户ID", width: 180 },
        { prop: "inviteUserId", label: "邀请人用户ID", width: 180 },
        { prop: "createTimeText", label: "邀请时间", width: 180 },
        { prop: "realCommentCountText", label: "评论KPI进度", width: 100 },
        { prop: "realLikeCountText", label: "点赞KPI进度", width: 100 },
        { prop: "totalCountText", label: "合计进度", width: 100 },
      ],
      inviteHeaderWidth: 0,
      inviteHeaderScale: 1,
      errorGapCount: 20,
      endPageNum: 100,
      exportParams: {
        isExt: false,
        classifyId: 123,
        classifyCurrentIndex: null,
      },
      allRenderList: [],
      tenantName: "广州绿葆网络发展有限公司",
      tenantName2: null,
      // 点赞明细
      userVisitCountVos: [],
      userVisitCountVosHeader: [
        { prop: "nickName", label: "操作用户昵称", width: 320 },
        // { prop: "inviteUserName", label: "邀请人", width: 100 },
        // { prop: "inviteUserId", label: "邀请人用户ID", width: 180 },
        { prop: "title", label: "帖子标题", width: 620 },
        { prop: "operationTimeText", label: "操作时间", width: 180 },
        // { prop: "accountId", label: "accountId", width: 180 },
        // { prop: "typeText", label: "操作类型", width: 100 },
      ],
      serveHeadersWidth: 0,
      serveHeadersWidthScale: 1,
      productTableWidth: 0,
      productTableWidthScale: 1,
      productTable: [],
      productHeaders: [
        {
          label: "姓名",
          prop: "userName",
          width: 150,
        },
        {
          label: "结算数量",
          prop: "settlementNum",
          width: 150,
        },
        {
          label: "单价",
          prop: "unitPrice",
          width: 150,
        },
        {
          label: "奖金",
          prop: "bonus",
          width: 150,
        },
        {
          label: "金额",
          prop: "totalAmount",
          width: 150,
        },
      ],
      uuidCount: 0,
      // 渲染指针
      waitIndex: 0,
      historyIndex: 0,
      pageSize: {
        width: 794,
        height: 1123,
      },
      opageContent: [
        {
          text: "所有内容",
          type: "allContent",
          once: true,
          uuid: "0_report",
          children: [],
          total: 0,
        },
      ],
      // 导出数据的标题
      pageContent: [],
      reportEnd: {
        text: "报告内容",
        type: "reportContentBooks",
        authHeight: true,
        pageContent: {},
      },

      pageLoading: true,
      authHeightResult: [],
      initsuccesscount: 0,
      targetCount: 3,
      size: 500,
      pageCurrent: 1,
      renderObject: "log", // 渲染对象 dianzan/common/log
    };
  },
  watch: {
    updatecount(n) {
      this.pageLoading = true;
      this.initsuccesscount = 0;
      this.pageContent = [...this.opageContent];
      this.initpageData();
    },
  },
  methods: {
    sceentInit() {
      let spaceCount = 1;
      if (this.signInLogListImage.length > spaceCount) {
        let count = this.signInLogListImage.length - spaceCount;
        let idx = this.pageContent.findIndex(
          (item) => item.type === "executiveTeamPresentation"
        );
        let tidx = 0;
        while (count > 0) {
          count -= spaceCount;
          tidx += 1;
          idx += 1;
          this.pageContent.splice(idx, 0, {
            text: "执行现场",
            type: "executiveTeamPresentation",
            authHeight: true,
            end: spaceCount * (tidx + 1),
            start: spaceCount * tidx,
            noTitleVisible: true,
            pageContent: {
              signInLogListImage: [],
            },
          });
        }
      }
    },
    initEndContent() {
      this.pageContent.push({
        text: "执行现场",
        type: "executiveTeamPresentation",
        authHeight: true,
        start: 0,
        end: 1,
        pageContent: {
          signInLogListImage: [],
        },
      });
      this.sceentInit();
      this.$nextTick(() => {
        this.updateSuccess();
      });
    },
    initViteMain() {
      if (this.pageCurrent !== 1) {
        return;
      }
      let scale = 1;
      let width = 0;
      this.inviteHeaders.forEach((item) => {
        width += item.width;
      });
      let boxWidth = this.pageSize.width - 40;
      if (width < boxWidth) {
        this.inviteHeaderScale = scale;
        this.inviteHeaderWidth = width;
        this.inviteHeaders[this.inviteHeaders.length - 1].width = null;
      } else {
        width += this.inviteHeaders.length + 10;
        this.inviteHeaders[this.inviteHeaders.length - 1].width = null;
        scale = (boxWidth / width).toFixed(2);
        this.inviteHeaderScale = scale;
        this.inviteHeaderWidth = width;
      }
    },
    async initPinLun() {
      if (this.pageCurrent !== 1) {
        return;
      }
      let scale = 1;
      let width = 0;
      this.commonHeaders.forEach((item) => {
        width += item.width;
      });
      let boxWidth = this.pageSize.width - 40;
      if (width < boxWidth) {
        this.commonHeadersScale = scale;
        this.commonHeadersWidth = width;
        this.commonHeaders[this.commonHeaders.length - 1].width = null;
      } else {
        width += this.commonHeaders.length + 10;
        this.commonHeaders[this.commonHeaders.length - 1].width = null;
        scale = (boxWidth / width).toFixed(2);
        this.commonHeadersScale = scale;
        this.commonHeadersWidth = width;
      }
    },
    initProductTable() {
      let scale = 1;
      let width = 0;
      this.productHeaders.forEach((item) => {
        width += item.width;
      });
      let boxWidth = this.pageSize.width - 40;
      if (width < boxWidth) {
        this.productTableWidthScale = scale;
        this.productTableWidth = width;
        this.productHeaders[this.productHeaders.length - 1].width = null;
      } else {
        width += this.productHeaders.length + 10;
        this.productHeaders[this.productHeaders.length - 1].width = null;
        scale = (boxWidth / width).toFixed(2);
        this.productTableWidthScale = scale;
        this.productTableWidth = width;
      }
    },
    getRenderHeight(uuid) {
      let dom = document.getElementById(uuid);
      let offsetHeight = dom.clientHeight;
      let offsetTop = dom.offsetTop;
      return {
        offsetHeight,
        offsetTop,
      };
    },
    async initRender(item, typeName = "allContent") {
      return new Promise((resolve, reject) => {
        let idx = this.waitIndex;
        if (!this.pageContent[idx]) {
          if (
            this.exportParams.classifyId &&
            this.historyIndex === this.endPageNum
          ) {
            return resolve(2);
          }
          this.pageContent.push({
            type: typeName,
            uuid: this.getUuid(),
            once: this.oneInit,
            pageNum: this.waitIndex + 1,
            children: [],
            total: this.totalCount,
          });
          this.oneInit = false;
        } else if (this.pageCurrent === 1) {
          this.pageContent[idx].total = this.totalCount;
        }
        let uuid = this.pageContent[idx].uuid;
        if (this.renderObject === "log") {
          let num1 = Number(item.commentKpi) + Number(item.likeKpi);
          let num2 = Number(item.realCommentCount) + Number(item.realLikeCount);
          let totalCount = formFenToYuan((num2 / num1) * 100) + "%";
          item.realCommentCountText =
            item.realCommentCount + "/" + item.commentKpi;
          item.realLikeCountText = item.realLikeCount + "/" + item.likeKpi;
          item.totalCountText = totalCount;
          item.createTimeText = format(item.createTime, "YYYY-MM-DD HH:mm:ss");
        } else if (this.renderObject === "dianzan") {
          item.typeText = this.getEnumText(
            item.type,
            getDmCommunityCommentLikeFlowType()
          );
          item.operationTimeText = format(
            item.operationTime,
            "YYYY-MM-DD HH:mm:ss"
          );
        } else if (this.renderObject === "common") {
          item.typeText = this.getEnumText(
            item.type,
            getDmCommunityPostsType()
          );
          item.putawayStatusText = this.getEnumText(
            item.putawayStatus,
            getDmCommunityPutawayStatus()
          );
          item.processStatusText = this.getEnumText(
            item.processStatus,
            getDmCommunityProcessStatus()
          );
          item.processResult = this.getProcessResultText(item);
          item.putawayTimeText = format(
            item.putawayTime,
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        // 获取前
        this.pageContent[idx].children.push(item);
        this.$nextTick(async () => {
          let { offsetHeight, offsetTop } = this.getRenderHeight(uuid);
          if (
            offsetHeight + offsetTop + this.errorGapCount >
            this.pageSize.height
          ) {
            let len = this.pageContent[idx].children.length;
            this.pageContent[idx].children.splice(len - 1, 1);
            this.updateWaiting();
            let iv = await this.initRender(item, typeName);
            return resolve(iv);
          }
          resolve(true);
        });
      });
    },
    getProcessResultText(item) {
      if (item.processStatus !== 2) {
        return "";
      } else {
        return item.putawayStatus === 1 ? "审核通过" : "审核下架";
      }
    },
    sheep() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(true);
        }, 5);
      });
    },
    updateWaiting() {
      this.waitIndex += 1;
      this.historyIndex += 1;
    },
    // 点赞
    initDianzan() {
      if (this.pageCurrent !== 1) {
        return;
      }
      let width = 0;
      let scale = 1;
      this.userVisitCountVosHeader.forEach((item) => {
        width += item.width;
      });
      let boxWidth = this.pageSize.width - 40;
      if (width < boxWidth) {
        this.serveHeadersWidthScale = scale;
        this.serveHeadersWidth = boxWidth;
        this.userVisitCountVosHeader[
          this.userVisitCountVosHeader.length - 1
        ].width = null;
      } else {
        width += this.productHeaders.length + 10;
        this.userVisitCountVosHeader[
          this.userVisitCountVosHeader.length - 1
        ].width = null;
        scale = (boxWidth / width).toFixed(2);
        this.serveHeadersWidthScale = scale;
        this.serveHeadersWidth = width;
      }
    },
    async waitRendering() {
      let result = this.allRenderList;
      let typeName = "";
      if (this.renderObject === "log") {
        typeName = "allContent";
      } else if (this.renderObject === "dianzan") {
        typeName = "dianzan";
      } else if (this.renderObject === "common") {
        typeName = "pinlun";
      }
      for (let i = 0; i < result.length; i++) {
        let item = result[i];
        let isResult = await this.initRender(item, typeName);
        if (isResult === 2) {
          break;
        }
      }
      console.log("this.pageContent", this.pageContent);
      if (this.exportParams.classifyId) {
        if (
          !this.requestEnd &&
          this.historyIndex !== this.endPageNum &&
          !this.noNextPage
        ) {
          this.pageCurrent += 1;
          this.requestPage();
          return;
        } else if (this.noNextPage && this.historyIndex !== this.endPageNum) {
          this.pageCurrent = 1;
          if (this.renderObject === "log") {
            if (
              this.pageContent[this.waitIndex] &&
              this.pageContent[this.waitIndex].type === "allContent"
            ) {
              this.updateWaiting();
            }
            this.renderObject = "dianzan";
          } else {
            if (
              this.pageContent[this.waitIndex] &&
              this.pageContent[this.waitIndex].type === "dianzan"
            ) {
              this.updateWaiting();
            } else if (
              !this.pageContent[this.waitIndex] &&
              this.pageContent[this.waitIndex - 1].type === "allContent"
            ) {
              this.pageContent.push({
                type: "dianzan",
                uuid: this.getUuid(),
                once: true,
                pageNum: this.waitIndex + 1,
                children: [],
                total: this.totalCount,
              });
              this.updateWaiting();
            }
            this.renderObject = "common";
          }
          this.requestPage();
          return;
        }
      }
      if (
        this.allRenderList.length === 0 &&
        this.pageCurrent === 1 &&
        !this.pageContent[this.waitIndex] &&
        this.pageContent[this.waitIndex - 1] &&
        this.pageContent[this.waitIndex - 1].type === "dianzan"
      ) {
        this.pageContent.push({
          type: "pinlun",
          uuid: this.getUuid(),
          once: true,
          pageNum: this.waitIndex + 1,
          children: [],
          total: this.totalCount,
        });
      }
      this.updateSuccess();
      if(this.inType === 'user-activity-project') {
        this.updateSuccess()
      } else {
        this.initEndContent();
      }
    },
    getUuid() {
      this.uuidCount += 1;
      return this.uuidCount + "_report";
    },
    getPrice(price) {
      price = formFenToYuan((price - 0) / 100);
      return price;
    },
    // 获取数据
    async exportProjectAccurateId() {
      const res = await todotasksGetReportPersonal(
        {
          id: this.taskId,
        },
        {
          "no-time-manage": 1,
        }
      );
      const taskRes = await queryTodoTaskOne(
        {
          id: this.taskId,
        },
        {
          "no-time-manage": 1,
        }
      );
      const taskData = taskRes.data;
      const data = res.data;
      this.cutTime =
        this.getTargetTime(format(data.startTime, "YYYY-MM-DD")) +
        " 至 " +
        this.getTargetTime(format(data.endTime, "YYYY-MM-DD"));
      this.tenantName2 = data.tenantName;
      if (data.todoTaskExecutorReportVos instanceof Object) {
        this.todoTaskExecutorReportVos = data.todoTaskExecutorReportVos;
        this.productTable = data.todoTaskExecutorReportVos.map((item) => {
          item.unitPrice = this.getPrice(item.unitPrice) + "元";
          item.bonus = this.getPrice(item.bonus) + "元";
          item.totalAmount = this.getPrice(taskData.incentiveFeePrice) + "元";
          item.settlementNum = taskData.performanceCount;
          return item;
        });
      }
      this.userActivityId = taskData.businessId;
      if (
        Array.isArray(taskData.todoTasksItems) &&
        taskData.todoTasksItems.length !== 0
      ) {
        this.userActivityUserId = taskData.todoTasksItems[0].disposeUserId;
        this.userActivityItemId = taskData.todoTasksItems[0].id;
      }
      let imageArr = [];
      if (data.todoTaskExecutorLocationReportVos instanceof Object) {
        oneRoot: for (
          let i = 0;
          i < data.todoTaskExecutorLocationReportVos.length;
          i++
        ) {
          let imagePath = data.todoTaskExecutorLocationReportVos[i].imageIds;
          if (imagePath !== "" && imagePath) {
            let arr = imagePath.split(",");
            for (let k = 0; k < arr.length; k++) {
              if (imageArr.length === 27) {
                break oneRoot;
              }
              if (arr[k] === "") {
                continue;
              }
              imageArr.push({
                url: domainURL(arr[k]) + "?x-oss-process=image/auto-orient,1",
              });
            }
          }
        }
      }
      this.signInLogListImage = imageArr;
    },
    isTaskList() {
      if(this.levelParams instanceof Object && this.levelParams.taskIdList) {
        return true;
      }
    },
    async requestPage() {
      let res = null;
      let levelParams = this.levelParams || {}
      if (this.renderObject === "log") {
        let condition = {
          userActivityId: this.userActivityId,
          taskId: this.userActivityItemId,
          inviteUserId: this.userActivityUserId,
          ...levelParams,
        }
        if(this.isTaskList()) {
          delete condition.taskId
        }
        res = await queryPageLogPage(
          {
            size: this.size,
            current: this.pageCurrent,
            condition: condition,
          },
          {
            "no-time-manage": 1,
          }
        );
        this.inviteTotalCount = res.data.total;
        this.initViteMain();
      } else if (this.renderObject === "dianzan") {
        let condition = {
          taskId: this.userActivityItemId,
          businessType: 5,
          ...levelParams
        }
        if(this.isTaskList()) {
          delete condition.taskId
        }
        res = await useractivityinvitelogQueryPage(
          {
            size: this.size,
            current: this.pageCurrent,
            condition: condition,
          },
          {
            "no-time-manage": 1,
          }
        );
        this.initDianzan();
      } else if (this.renderObject === "common") {
        let condition = {
          businessType: 5,
          taskId: this.userActivityItemId,
          ...levelParams
        }
        if(this.isTaskList()) {
          delete condition.taskId
        }
        res = await queryCommonPage(
          {
            size: this.size,
            current: this.pageCurrent,
            condition: condition,
          },
          {
            "no-time-manage": 1,
            "gb-part-tenant-id": 1,
          }
        );
        this.initPinLun();
      }
      if (res.data.records.length !== this.size || this.inType === 'user-activity-project') {
        this.requestEnd = true;
        this.noNextPage = true;
      }
      if (
        this.pageCurrent === 1 &&
        ["dianzan", "common"].includes(this.renderObject)
      ) {
        this.oneInit = true;
        if (this.renderObject === "common") {
          this.noNextPage = false;
        }
      }
      this.totalCount = Number(res.data.total);
      this.allRenderList = res.data.records;
      console.log("this.allRenderList", this.allRenderList, this.pageContent);
      this.waitRendering();
    },
    getTargetTime(str) {
      let a1 = str.split("-");
      return a1[0] + "年" + a1[1] + "月" + a1[2] + "日";
    },
    addComputeTag() {
      // class=“pdf_finish”
      const dom = document.createElement("div");
      dom.classList = "pdf_finish";
      document.body.append(dom);
    },
    async updateSuccess() {
      this.initsuccesscount += 1;
      console.log(
        "this.initsuccesscount",
        this.initsuccesscount,
        this.targetCount
      );

      if (this.initsuccesscount === this.targetCount) {
        this.pageLoading = false;
        this.$nextTick(() => {
          this.initpage();
          if (this.inType === "user-activity-project") {
            this.$emit("compute", {});
            return;
          }
          let aDom = document.querySelector("a");
          if (aDom && Array.isArray(aDom)) {
            aDom[0] && aDom[0].remove();
          }
          setTimeout(() => {
            this.$emit("compute", {});
            this.addComputeTag();
          }, 300);
        });
      }
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    initpageData() {
      if (this.inType === "user-activity-project") {
        this.requestPage();
        this.initProductTable();
        this.$nextTick(() => {
          this.updateSuccess();
        });
      } else {
        this.exportProjectAccurateId().then((res) => {
          this.requestPage();
          this.initProductTable();
          this.$nextTick(() => {
            this.updateSuccess();
          });
        });
      }
    },
    initpage() {
      this.authHeightResult = {};
      for (let i = 0; i < this.pageContent.length; i++) {
        if (this.pageContent[i].authHeight) {
          let id = "authHeight" + i;
          this.authHeightResult[i] = id;
        }
      }

      this.$nextTick(() => {
        this.save();
      });
    },
    // 页面的倍数
    initHeight(height) {
      let pagecount = 1;
      while (height > this.pageSize.height) {
        pagecount += 1;
        height -= this.pageSize.height;
      }

      return pagecount * this.pageSize.height;
    },
    // 保存前初始化页面
    save() {
      for (let key in this.authHeightResult) {
        let id = this.authHeightResult[key];
        let dom = document.getElementById(id);
        // console.log(dom.clientHeight, this.pageContent[key]);
        this.pageContent[key].targetHeight = this.initHeight(dom.clientHeight);
      }
      this.$forceUpdate();
    },
  },
  computed: {
    pageTitle() {
      let n = "";
      if (this.businessType === 16) {
        n = "关于小葫芦平台的线上用户活动项目执行明细";
      } else if (this.businessType === 17) {
        n = "关于小葫芦平台的线下用户活动项目执行明细";
      }
      return n;
    },
  },
};
</script>




<style lang="scss" scoped>
$ColorMain: #4787ff;
.activity-page-h2 {
  padding-bottom: 15px;
  font-weight: bold;
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1f6ee1;
}
.title-icon {
  width: 55px;
  height: 55px;
  margin-right: 5px;
}
.activity-flex1 {
  // flex: 1;
  min-width: 33.333%;
  display: flex;
  margin-bottom: 2px;
}
.page-h2 {
  line-height: 2;
  margin-top: 50px;
  margin-bottom: 20px;
  font-weight: bold;
  font-size: 20px;
}
.page-h3 {
  line-height: 2;
  margin-bottom: 20px;
  font-weight: bold;
  font-size: 20px;
}
.ps50 {
  margin-top: 50px;
  margin-bottom: 50px;
}
.report-content-box {
}
.report-content-box.user-activity-project-report {
  background: #f2f9ff;
}
.mg50 {
  margin-bottom: 50px;
  margin-top: 50px;
}

.mgt30 {
  margin-top: 30px;
}
.everyPage {
  overflow: hidden;
  // padding: 0 0px 5px;
  box-sizing: border-box;
}
.everyPageContent {
  width: 100%;
  height: 100%;
  background: #fff;
}
.everyPageContent.user-activity-project-report {
  background: #f2f9ff;
}
// 问卷活动调研
.settlement-page {
  padding: 20px;
  position: relative;
  .page-title {
    font-size: 24px;
    font-weight: 550;
    display: flex;
    justify-content: center;
    line-height: 2;
    margin-bottom: 5px;
  }
  .page-time {
    font-size: 16px;
    font-weight: 550;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
  }
  .page-content {
    line-height: 1.5;
    font-size: 14px;
    text-indent: 30px;
    margin-bottom: 30px;
  }
  .page-content-tip {
    margin-bottom: 30px;
    text-indent: 30px;
    font-size: 14px;
  }
  .page-h3 {
    padding-bottom: 15px;
    font-weight: bold;
    font-size: 20px;
  }
  .activity-image-box {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
    .activity-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .activity-image-b {
      width: 550px;
      height: 977.7777px;
    }
  }
  .nodate {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    font-size: 12px;
    color: #909399;
  }
}
</style>
