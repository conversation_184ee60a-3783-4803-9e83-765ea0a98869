import { queryList } from "@/api/dm/visiting/visitingplanobjectlist.js";
import { format } from "@/utils/index";
import { getdrugstoreFeedbackList } from "@/utils/enumeration.js";
import {
  initColumnar,
  initColumnarTwoChart,
  initColumnarFourChart,
} from "../columnar.js";

const color = ["#005aff", "#f8b551"];
import { seximg } from "../images";
import { loadScript, getPosition } from "@/utils/index";
import {
  todotasksGetReportPrecise as exportProjectAccurateId,
  todotasksGetReportPreciseDetailSubmitLog,
  todotasksGetReportPreciseDetailFrom,
} from "@/api/dmDemand";
import { getQueryStr, domainURL } from "@/utils/index";
import { filterReportImageList,scaleReportImageObject, maxSizeReportImageList } from '@/utils/report-error-image.js'
import dynamicsTable from '../components/dynamics-table.vue'

export default {
  components: {
    dynamicsTable
  },
  props: {
    taskId: {
      type: [Number, String],
      default: null,
    },
    preview: {
      type: Boolean,
      default: true,
    },
    // 拼接域名 lvbao不支持本地调式图片
    domainUrl: {
      type: String,
      default: "https://lvbao-saas.oss-cn-shenzhen.aliyuncs.com/",
    },
    domainResult: {
      type: Array,
      default: function () {
        return ["https://file.greenboniot.cn/"];
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    filename: {
      type: String,
      default: "",
    },
    uuid: {
      type: String,
      default: "export-visiting-analysis-activity-report",
    },
    taskMonth: {
      type: [Number, String],
      default: "",
    },
  },
  data() {
    return {
      isInType: 'default',
      maxImageCount: 27,
      userImageCount: 9,
      userImageBoolean: false,
      tableHeader: [
        {
          prop: "recordName",
          label: "执行团队代表人姓名",
          width: 220,
        },
        {
          prop: "genderText",
          label: "性别",
          width: 220,
        },
        {
          prop: 'userPhone',
          label: '手机号',
          width: 220
        },{
          prop: 'submitNum',
          label: '拉新数量',
          width: 220
        }
      ],
      taskUserVoList: [],
      taskUserVoListUpdateCount: 0,
      imageStyleOne: false,
      bottomStr2:"",
      bottomStr: "",
      bottomIconUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/9.png",
      productImgUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/1.png",
      productBgUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/2.png",
      productResultUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/3.png",
      productResultAnalysisUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/4.png",
      onSiteSectionUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/5.png",
      recruitNewUserInformationUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/6.png",
      laNewUserUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/7.png",
      executiveSummaryUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/8.png",

      bgImg:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/bg.jpg",
      taskUserRegion: [],
      taskUserRegionSpaceTop: 0,
      // 地推位置分布
      locationTableData: [],
      // 小葫芦拉新注册明细
      userVisitCountVos: [],
      // 小葫芦拉新注册表头
      userVisitCountVosHeader: [],
      // 拉新标题所在索引
      tCount: 3,
      domainUrl2: "https://file.greenboniot.cn/",
      saTime: 600,

      collectionType: null,
      pageSize: {
        height: 1123,
        width: 794,
      },
      themeImg:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/dirBg.jpg",
      research: {
        // title: "关于高血压患者家属需求在全国地区的调研项目",
        // monthText: "2023-11",
      },
      topResult: [
        {
          label: "问卷名称",
          id: "title",
          value: "",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/1.png",
        },
        {
          label: "项目方",
          value: "江西施美药业股份有限公司",
          id: "projectName",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/2.png",
        },
        {
          label: "服务商",
          value: "广州兰图科技有限公司",
          id: "serviceName",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/3.png",
        },
        {
          label: "任务月度",
          value: "",
          id: "monthText",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/4.png",
        },
        {
          label: "问卷数量",
          id: "taskNumAllCount",
          value: "0",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/5.png",
        },
        {
          label: "项目执行人数",
          id: "taskUserNum",
          value: "0",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/6.png",
        },
        {
          label: "报告生成时间",
          id: "createTimeText",
          value: "",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/7.png",
        },
      ],
      taskNumAllCount: 0,
      createTimeText: "",
      monthText: "",

      chartCount: 0,
      chartCountLength: 0,
      projectExecutorInformation: {
        age_0_17: "0.00%",
        age_18_30: "0.00%",
        age_31_40: "0.00%",
        age_41_50: "0.00%",
        age_51_70: "0.00%",
        age_71_100: "0.00%",
        sex_1: "0.00%", // 男性
        sex_2: "0.00%", //女性
        sex_0: "0.00%", // 未知
      },
      seximg,
      opageContent: [
        // 问卷活动调研
        {
          text: "所有内容",
          type: "allContent",
          authHeight: true,
          pageContent: {
            contentHtml: "",
          },
        },
        // 推广明细
        {
          text: "推广明细",
          type: "promotionDetails",
          authHeight: true,
          pageContent: {
            contentHtml: "",
          },
        },
        // 地推团队位置分析
        {
          text: "地推团队位置分析",
          type: "locationDistributionOfTheLocal",
          authHeight: true,
          pageContent: {
            staffAreaObject: {},
            taskUserRegion: [],
          },
        },
        {
          text: "地推现场照片展示",
          type: "executiveTeamPresentation",
          authHeight: true,
          end: 9,
          start: 0,
          pageContent: {
            signInLogListImage: [],
          },
        },
        // 地推活动结果分析
        {
          text: "地推活动结果分析",
          type: "activityResult",
          authHeight: true,
          hidden: false,
          pageContent: {
            contentHtml: "",
            serveHeadersWidth: 754,
            serveHeadersWidthScale: 1,
            staffSexObject: {},
          },
        },
      ],
      // 导出数据的标题
      pageContent: [],
      reportEnd: {
        text: "执行总结",
        type: "reportContentBooks",
        authHeight: true,
        pageContent: {},
      },
      // 题目
      topicObject: {
        text: "题目内容",
        type: "topicContent",
        authHeight: true,
        pageContent: {
          uuid: "my",
          hearders: [],
          tableData: [],
          isOnly: false,
          echartHeight: 400,
          echartMarginTop: -443,
        },
      },

      pageLoading: false,
      authHeightResult: [],
      initsuccesscount: 0,
      targetCount: 1,
      // 导出类型
      exportVisitType: 3,

      // 年龄段数据
      ageResult: [
        // {
        //   value: 0,
        //   name: "0-17周岁",
        //   oName: "0-17周岁",
        //   itemStyle: {
        //     normal: { color: "#6a9955" },
        //   },
        //   uuid: "age_0_17",
        // },
        {
          value: 0,
          name: "18-30周岁",
          oName: "18-30周岁",
          itemStyle: {
            normal: { color: "#2721ff" },
          },
          uuid: "age_18_30",
        },
        {
          value: 0,
          name: "31-40周岁",
          oName: "31-40周岁",
          itemStyle: {
            normal: { color: "#6763fe" },
          },
          uuid: "age_31_40",
        },
        {
          value: 0,
          name: "41-50周岁",
          oName: "41-50周岁",
          itemStyle: {
            normal: { color: "#cfcdfe" },
          },
          uuid: "age_41_50",
        },
        {
          value: 0,
          name: "51-70周岁",
          oName: "51-70周岁",
          itemStyle: {
            normal: { color: "#5dc0f9" },
          },
          uuid: "age_51_70",
        },
        // {
        //   value: 0,
        //   name: "70周岁以上",
        //   oName: "70周岁以上",
        //   itemStyle: {
        //     normal: { color: "#eab81a" },
        //   },
        //   uuid: "age_71_100",
        // },
      ],
      ageResultCount: 0,
      sexResultCount: 0,
      sexResult: [
        // itemStyle.normal.color
        {
          name: "女性",
          cName: "女性",
          value: 0,
          uuid: 2,
          itemStyle: {
            normal: {
              color: "#e2b62d",
            },
          },
        },
        {
          name: "男性",
          cName: "男性",
          value: 0,
          uuid: 1,
          itemStyle: {
            normal: {
              color: "#264894",
            },
          },
        },
        {
          name: "未知",
          cName: "未知",
          value: 0,
          uuid: 0,
          itemStyle: {
            normal: {
              color: "#ff002d",
            },
          },
        },
      ],
      auditResult: [
        {
          value: 0,
          name: "待处理",
          cName: "待处理",
          uuid: 1,
          itemStyle: {
            color: "#2822ff",
            // normal: { color: "#2822ff" },
          },
        },
        {
          value: 0,
          name: "处理中",
          cName: "处理中",
          uuid: 2,
          itemStyle: {
            color: "#fdc71c",
            // normal: { color: "#fdc71c" },
          },
        },
        {
          value: 0,
          name: "已处理",
          cName: "已处理",
          uuid: 3,
          itemStyle: {
            color: "#42d885",
            // normal: { color: "#2822ff" },
          },
        },
        // {
        //   value: 0,
        //   name: "已关闭",
        //   cName: "已关闭",
        //   uuid: 5,
        //   itemStyle: {
        //     color: "#f56c6c",
        //     // normal: { color: "#fdc71c" },
        //   },
        // },
        // {
        //   value: 0,
        //   name: "已撤销",
        //   cName: "已撤销",
        //   uuid: 6,
        //   itemStyle: {
        //     color: "#ffba00",
        //     // normal: { color: "#2822ff" },
        //   },
        // },
      ],
      auditObject: {
        audit_1_text: "待审核",
        audit_2_text: "审核通过",
        audit_3_text: "审核未通过",

        audit_1: "0.00%",
        audit_2: "0.00%",
        audit_3: "0.00%",
        // audit_5: "0.00%",
        // audit_6: "0.00%",
        audit_count_1: 0,
        audit_count_2: 0,
        audit_count_3: 0,
      },
      auditResultTotal: 0,

      tableData: [
        {
          title: "药店",
          title2: 2,
          title3: 3,
        },
        {
          title: "任务数（单）",
          title2: 2,
          title3: 3,
        },
        {
          title: "百分比（%）",
          title2: 2,
          title3: 3,
        },
      ],
      hearders: [
        {
          key: "title",
          prop: "title",
          title: "001",
        },
        {
          key: "title2",
          prop: "title2",
          title: "001",
        },
        {
          key: "title3",
          prop: "title3",
          title: "001",
        },
      ],
      // 执行人信息
      userReportVo: {},

      // 任务信息
      // taskReportVo: {},

      // 女性占比
      womanText: "0.00%",
      // 男性占比
      manText: "0.00%",

      // 项目信息
      demandInfo: {},

      visitingPlan: {},
      taskAllocationReportVoList: [],

      productAnalysisInfo: {
        num_0_18: "0.00",
        num_18_36: "0.00",
        num_36_54: "0.00",
        num_54_72: "0.00",
        num_72_90: "0.00",
        num_72_: "0.00",

        num_0_18_count: 0,
        num_18_36_count: 0,
        num_36_54_count: 0,
        num_54_72_count: 0,
        num_72_90_count: 0,
        num_72__count: 0,
      },

      // 题目数据
      answerReportVoList: [],
      // 地推活动结果分析
      activityResultList: [],
      // 用户基本信息
      basicInfo: {},
      // 用户健康信息
      healthInfo: {},
      // 地推结果分析
      activityResultObject: {
        isSex: false, // 性别控件
        isAge: false, // 年龄
        isDisease: false, // 疾病
        isProvince: false, // 省市
        isDepartment: false, // 科室
        areaStr: "", // 地区值字符串
        allWriteOptionNum: 0, // 新注册人数
        diseaseStr: "", // 疾病字符串
      },
      // 活动场景展示
      eventSceneDisplayImage: [],
      // 签到图片
      signInLogListImage: [],
      // 固定字段
      fixedFieldObject: {
        projectName: "关于小葫芦平台在全国区域的精准地推活动", // 项目名称
        serviceProvider: "", // 服务方
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己
        startTime: "", // 项目执行开始时间
        endTime: "", // 项目执行结束时间
        productTaskNumber: 0, // 地推团队数量
      },
    };
  },
  mounted() {},
  watch: {
    updatecount(n) {
      this.pageLoading = true;
      this.initsuccesscount = 0;
      this.pageContent = [...this.opageContent];
      this.answerReportVoList = [];
      this.activityResultList = [];
      this.chartCountLength = 0;
      this.chartCount = 0;

      const saToken = getQueryStr("satoken");
      if (saToken) {
        this.saTime = 300;
      } else {
        // 客户端预览
        this.saTime = 2000;
      }

      this.initEchart();
      // this.exportProjectAccurateId()
    },
  },
  methods: {
    successDynamics() {
      this.updateSuccess();
    },
    // 初始化拉新注册明细表头
    initUserVisitCountVos(data = []) {
      let idx = this.pageContent.findIndex(
        (item) => item.type === "newRegistrationDetailsForXiaohuluLa"
      );

      let sw = 714;
      let serveHeadersWidthScale = 1;
      let serveHeadersWidth = 0;
      let userVisitCountVos = [];
      let headerObj = {};
      if (this.userVisitCountVos.length !== 0) {
        // this.userVisitCountVos[0];
        headerObj = this.userVisitCountVos[0];
      } else {
        return;
      }
      let indexObject = {
        微信昵称: 1,
        性别: 2,
        年龄: 3,
        疾病: 4,
        科室: 5,
        省市: 6,
        请求ip: 7,
        提交时间: 8,
      };
      let indexCount = 9;

      let headerArr = [];
      for (let key in headerObj) {
        // for (let i = 0; i < headerResult.length; i++) {
        serveHeadersWidth += 150;
        indexCount += 1;
        headerArr.push({
          prop: key,
          label: key,
          width: 150,
          indexCount: indexObject[key] ? indexObject[key] : indexCount,
        });
      }
      headerArr.sort((a, b) => a.indexCount - b.indexCount);
      console.log("headerArr", headerArr);

      let idx3 = headerArr.findIndex((item) => item.prop === "疾病");
      if (idx3 !== -1) {
        headerArr.splice(idx3, 1);
        serveHeadersWidth -= 150;
      }
      idx3 = headerArr.findIndex((item) => item.prop === "省市");
      if (idx3 !== -1) {
        headerArr.splice(idx3, 1);
        serveHeadersWidth -= 150;
      }
      if (serveHeadersWidth < sw) {
        serveHeadersWidth = sw;
        serveHeadersWidthScale = 1;
        headerArr[headerArr.length - 1].width = null;
      } else {
        serveHeadersWidth += headerArr.length + 10;
        headerArr[headerArr.length - 1].width = null;
        serveHeadersWidthScale = (sw / serveHeadersWidth).toFixed(2);
      }
      this.pageContent[idx].pageContent.serveHeadersWidth = serveHeadersWidth;
      this.pageContent[idx].pageContent.serveHeadersWidthScale =
        serveHeadersWidthScale;

      this.userVisitCountVosHeader = headerArr;
    },
    initNumFn() {
      let count = 2;
      if (this.activityResultObject.isDisease) {
        count += 1;
      }
      this.tCount = count;
    },
    getDaysInCurrentMonth() {
      let str = this.taskMonth;
      let arr = str.split("-");
      let year = arr[0];
      let month = arr[1];
      // 设置日期为下个月的第0天，这样它就会回滚到当前月的最后一天
      var nextMonthFirstDay = new Date(year, month, 1);
      var daysInMonth = new Date(nextMonthFirstDay - 1).getDate();
      return daysInMonth;
    },
    // 初始化执行时间
    initTime() {
      let daysInMonth = this.getDaysInCurrentMonth();
      let startTime = this.taskMonth + "-" + "01";
      let endTime = this.taskMonth + "-" + daysInMonth;
      this.fixedFieldObject.startTime = startTime;
      this.fixedFieldObject.endTime = endTime;
    },
    isOtherChartFn(type) {
      let is = true;
      // if ([15, 16, 17, 18, 19, 20].includes(type)) {
      if ([18,15].includes(type)) {
        is = false;
      }
      return is;
    },
    // 赋值对应
    updateActivityItem(type, answerOptionVoList) {
      if (type === 15) {
        // 性别
        this.activityResultObject.isSex = true;
        let arr = [...answerOptionVoList];
        arr.sort((a, b) => b.selectOptionNum - a.selectOptionNum);
        this.bottomStr2 = arr[0] ? arr[0].optionValue : '';
      } else if (type === 16) {
        // 年龄
        this.activityResultObject.isAge = true;
      } else if (type === 17) {
        // 科室标签
        this.activityResultObject.isDepartment = true;
        let arr = [...answerOptionVoList];
        arr.sort((a, b) => b.selectOptionNum - a.selectOptionNum);
        this.bottomStr = arr[0] ? arr[0].optionValue : '';
      } else if (type === 18) {
        // 省市
        this.activityResultObject.isProvince = true;
        let areaStr = "";
        for (let i = 0; i < answerOptionVoList.length; i++) {
          if (areaStr === "") {
            areaStr +=
              answerOptionVoList[i].optionValue +
              "地区用户占比" +
              answerOptionVoList[i].selectOptionProportion +
              "%";
          } else {
            areaStr +=
              "，" +
              answerOptionVoList[i].optionValue +
              "地区用户占比" +
              answerOptionVoList[i].selectOptionProportion +
              "%";
          }
        }
        this.activityResultObject.areaStr = areaStr;
      } else if ([19, 23].includes(type)) {
        // 疾病
        this.activityResultObject.isDisease = true;
        let diseaseStr = "";
        answerOptionVoList.sort(
          (a, b) => b.selectOptionNum - a.selectOptionNum
        );
        // this.bottomStr = answerOptionVoList[0].optionValue
        console.log("疾病", answerOptionVoList);
        for (let i = 0; i < answerOptionVoList.length; i++) {
          if (i === 5) {
            break;
          }
          if (diseaseStr === "") {
            diseaseStr +=
              answerOptionVoList[i].optionValue +
              "用户占比" +
              answerOptionVoList[i].selectOptionProportion +
              "%";
          } else {
            diseaseStr +=
              "，" +
              answerOptionVoList[i].optionValue +
              "用户占比" +
              answerOptionVoList[i].selectOptionProportion +
              "%";
          }
        }
        if (answerOptionVoList.length > 0) {
          diseaseStr += "(文字过多，仅展示占比最高前5条)";
        }
        this.activityResultObject.diseaseStr = diseaseStr;
        this.bottomStr = answerOptionVoList[0] ? answerOptionVoList[0].optionValue : '';
      } else if (type === 20) {
        // 姓名
        this.activityResultObject.isName = true;
      }
    },
    sceentInit() {
      let imageCount = 9;
      if(this.imageStyleOne) {
        imageCount = 1;
      } else if (this.userImageBoolean) {
        imageCount = this.userImageCount
      }
      if (this.signInLogListImage.length > imageCount) {
        let count = this.signInLogListImage.length - imageCount;
        let idx = this.pageContent.findIndex(
          (item) => item.type === "executiveTeamPresentation"
        );
        let tidx = 0;
        while (count > 0) {
          count -= imageCount;
          tidx += 1;
          idx += 1;
          this.pageContent.splice(idx, 0, {
            text: "地推现场照片展示",
            type: "executiveTeamPresentation",
            authHeight: true,
            end: imageCount * (tidx + 1),
            start: imageCount * tidx,
            pageContent: {
              signInLogListImage: [],
            },
          });
        }
      }
    },
    // 初始化活动场景
    initActivityPage() {
      // this.pageContent.push({
      //   text: "活动场景展示",
      //   type: "eventSceneDisplay",
      //   authHeight: true,
      //   pageContent: {
      //     eventSceneDisplayImage:this.eventSceneDisplayImage
      //   },
      // })
      this.pageContent.push({
        text: "拉新明细",
        type: "newRegistrationDetailsForXiaohuluLa",
        authHeight: true,
        pageContent: {
          // signInLogListImage: this.signInLogListImage,
        },
      });

      // 初始化明细
      this.initUserVisitCountVos();
    },
    addComputeTag() {
      // class=“pdf_finish”
      const dom = document.createElement("div");
      dom.classList = "pdf_finish";
      document.body.append(dom);
    },
    // 生成模拟 3D 饼图的配置项
    getPie3DTwo(pieData, internalDiameterRatio) {
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let legendData = [];
      let k =
        typeof internalDiameterRatio !== "undefined"
          ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
          : 1 / 3;

      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;

        let seriesItem = {
          name:
            typeof pieData[i].name === "undefined"
              ? `series${i}`
              : pieData[i].name,
          type: "surface",
          parametric: true,
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k,
          },
        };

        if (typeof pieData[i].itemStyle != "undefined") {
          let itemStyle = {};

          typeof pieData[i].itemStyle.color != "undefined"
            ? (itemStyle.color = pieData[i].itemStyle.color)
            : null;
          typeof pieData[i].itemStyle.opacity != "undefined"
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null;

          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;

        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = this.getParametricEquationTwo(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          true,
          false,
          1
        );

        startValue = endValue;

        legendData.push(series[i].name);
      }
      console.log("legendData", legendData);

      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      let option = {
        //animation: false,
        legend: {
          show: true,
          data: legendData,
          type: "plain",
          // orient: "vertical",
          left: 0,
          top: 265,
          // bottom: 6,
          textStyle: {
            // color: "#CAEFFF",
            color: "#000",
          },
          itemGap: 10,
        },

        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1.3,
          max: 1.3,
        },

        grid3D: {
          show: false,
          height: 350,
          width: 350,
          boxHeight: 20, // 饼图厚度
          // left: "5%",
          // top: "0%",
          top: "-20%",
          left: "-3%",
          viewControl: {
            // 3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 25,
            distance: 300, //调整视角到主体的距离，类似调整zoom
            rotateSensitivity: 0,
            zoomSensitivity: 0,
            panSensitivity: 0,
            autoRotate: false, // 控制是否自动旋转
            //   autoRotateSpeed: 5,
            //   autoRotateAfterStill: 10
          },
        },
        series: series,
      };
      return option;
    },
    // 同步渲染
    syncStatisisItem(fn) {
      let timer = this.saTime;
      return new Promise((resolve, reject) => {
        fn();
        setTimeout(() => {
          resolve(true);
        }, timer);
      });
    },
    initEchart() {
      // this.$nextTick(() => {
      console.log("initEchart");

      return new Promise((resolve, reject) => {
        loadScript(`${this.domainUrl2}cdnjs/echarts.js`)
          .then(() => {
            loadScript(`${this.domainUrl2}cdnjs/echarts-gl.js`)
              .then(() => {
                console.log("window", window);

                this.exportProjectAccurateId().then(async (res) => {
                  if (this.collectionType != 4) {
                    let itx = this.topResult.findIndex(
                      (item) => item.id === "taskUserNum"
                    );
                    this.topResult[itx].hidden = false;
                  } else {
                    let itx = this.topResult.findIndex(
                      (item) => item.id === "taskUserNum"
                    );
                    this.topResult[itx].hidden = true;
                  }

                  this.$nextTick(() => {
                    setTimeout(() => {
                      this.updateSuccess(); // 1
                      resolve(true);
                    }, 100);
                  });
                  this.initProblemData();
                });
              })
              .catch((err) => {
                // console.log(err);
              });
          })
          .catch((err) => {
            console.log(err);
          });
      });

      // });
    },
    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquationTwo(startRatio, endRatio, isSelected, isHovered, k) {
      // 计算
      let midRatio = (startRatio + endRatio) / 2;

      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;

      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }

      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== "undefined" ? k : 1 / 3;

      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1;

      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },

        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },

        x: function (u, v) {
          if (u < startRadian) {
            return (
              offsetX +
              Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        y: function (u, v) {
          if (u < startRadian) {
            return (
              offsetY +
              Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u);
          }
          return Math.sin(v) > 0 ? 1 : -1;
        },
      };
    },
    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    // 初始化题目分析报告
    initProblemData() {
      // 后端返回数据
      // const problemData = this.answerReportVoList;
      const problemData = this.activityResultList;
      let str = "mm";
      let allWriteOptionNum = 0;
      let typeCount = 1;
      // 分类最大数量
      let typeMaxCount = 4;
      let cnum = 1;
      for (let i = 0; i < problemData.length; i++) {
        this.updateActivityItem(
          problemData[i].formType,
          problemData[i].answerOptionVoList
        );
        if (allWriteOptionNum < problemData[i].allWriteOptionNum) {
          allWriteOptionNum = problemData[i].allWriteOptionNum;
        }

        problemData[i].uuid = str + "_" + i;
        this.topicObject.pageContent.title = problemData[i].formTemplateTitle;

        this.topicObject.pageContent.uuid = problemData[i].uuid;
        this.topicObject.pageContent.showBottom = [1].indexOf(typeCount) != -1;
        this.topicObject.pageContent.belongType = typeCount;

        if (
          [19, 23, 17].includes(problemData[i].formType) ||
          !this.topicObject.pageContent.showBottom
        ) {
          this.topicObject.pageContent.echartHeight = 550;
          this.topicObject.pageContent.echartMarginTop = -625;
        } else {
          this.topicObject.pageContent.echartHeight = 400;
          this.topicObject.pageContent.echartMarginTop = -443;
        }
        problemData[i].belongType = typeCount;
        // if (problemData[i].formType === 17) {
        //   this.topicObject.pageContent.belongType = 1;
        //   problemData[i].belongType = 1;
        //   this.topicObject.pageContent.showBottom = true;
        // } else 
        if (problemData[i].formType === 15) {
          this.topicObject.pageContent.belongType = 3;
          problemData[i].belongType = 3;
          this.topicObject.pageContent.showBottom = false;
        } else if(problemData[i].formType === 16) {
          // 年龄
          this.topicObject.pageContent.belongType = 2;
          problemData[i].belongType = 2;
          this.topicObject.pageContent.showBottom = false;
        } 
        else if ([19, 23, 17].includes(problemData[i].formType)) {
          // 科室/疾病
          this.topicObject.pageContent.belongType = 4;
          problemData[i].belongType = 4;
          this.topicObject.pageContent.showBottom = false;
        }
        if (typeCount !== typeMaxCount) {
          typeCount += 1;
        } else {
          typeCount = 1;
        }
        // let topTitle = problemData[i].formTemplateTitle
        this.topicObject.pageContent.hearders = [
          {
            key: "optionValue",
            prop: "optionValue",
            title: "选项",
            width: 180,
          },
          {
            key: "selectOptionNum",
            prop: "selectOptionNum",
            title: "答案人数（人）",
            width: 180,
          },
          {
            key: "selectOptionProportionText",
            prop: "selectOptionProportionText",
            title: "百分比（%）",
            // width:180,
          },
        ];
        let totalTargetNumber = 0;
        // 是否是年龄
        let isage = false;
        for (let k = 0; k < problemData[i].answerOptionVoList.length; k++) {
          let val = problemData[i].answerOptionVoList[k].optionValue;
          if (val.indexOf("age_") !== -1) {
            val = val.split("age_")[1];
            val = val.split("_").join("-") + "岁";
            isage = true;
            problemData[i].answerOptionVoList[k].sortVal = val.split("-")[0];
            // problemData[i].answerOptionVoList[k].sortVal = problemData[i].answerOptionVoList[k].sortVal.split('_').join('-')
          }
          problemData[i].answerOptionVoList[k].optionValue = val;
          problemData[i].answerOptionVoList[k].selectOptionProportionText = (
            problemData[i].answerOptionVoList[k].selectOptionProportion - 0
          ).toFixed(2);

          // totalTargetNumber +=
        }
        if (isage) {
          console.log(
            "problemData[i].answerOptionVoList",
            problemData[i].answerOptionVoList
          );
          problemData[i].answerOptionVoList.sort(
            (a, b) => a.sortVal - b.sortVal
          );
          let tix = problemData[i].answerOptionVoList.findIndex(
            (item) => item.optionValue === "0-17岁"
          );
          problemData[i].answerOptionVoList.splice(tix, 1);
          tix = problemData[i].answerOptionVoList.findIndex(
            (item) => item.optionValue === "71-100岁"
          );
          problemData[i].answerOptionVoList.splice(tix, 1);
        }

        this.topicObject.pageContent.tableData =
          problemData[i].answerOptionVoList;
        this.topicObject.pageContent.isOnly = i === 0;

        // totalTargetNumber
        this.topicObject.pageContent.totalTargetNumber =
          problemData[i].allWriteOptionNum - 0;

        if (this.isOtherChartFn(problemData[i].formType)) {
          cnum += 1;
          this.topicObject.pageContent.index = "（" + cnum + "）";
          this.pageContent.push(JSON.parse(JSON.stringify(this.topicObject)));
        } else {
          problemData[i].topicObject = JSON.parse(
            JSON.stringify(this.topicObject)
          );
        }
      }
      this.taskNumAllCount = allWriteOptionNum;

      let idx3 = this.topResult.findIndex(
        (item) => item.id === "taskNumAllCount"
      );
      this.topResult[idx3].value = allWriteOptionNum;

      // console.log("this.pageContent", this.pageContent);
      // 初始化
      if(this.isInType !== 'user-activity-project') {
        this.initActivityPage();
        this.pageContent.push(this.reportEnd);
      } else if(this.isInType === 'user-activity-project'){
        let hidx = this.pageContent.findIndex(item => item.type === 'activityResult');
        if(hidx !== -1) {
          this.pageContent[hidx].hidden = true;
        }
      }
      // 初始化现场图片
      this.sceentInit();
      // 初始化小葫芦序号
      this.initNumFn();

      this.$nextTick(async () => {
        for (let i = 0; i < problemData.length; i++) {
          let item = problemData[i];

          let seriesData = [];
          let textData = [];

          for (let k = 0; k < item.answerOptionVoList.length; k++) {
            let val = item.answerOptionVoList[k].optionValue;
            textData.push(val);
            seriesData.push({
              value: item.answerOptionVoList[k].selectOptionNum,
              // name: this.getTargetText(item.answerOptionVoList[k].optionValue),
              name: val,
            });
          }

          item.seriesData = seriesData;
          item.textData = textData;
          if (this.isOtherChartFn(problemData[i].formType)) {
            await this.initProbleDataChart(item, i);
          } else {
            await this.initPointChart(item, i);
          }
        }
      });
    },
    initSector(strUUID, seriesData) {
      const str = this.uuid;
      const dom = document.getElementById(str);
      let myCharts4 = window.echarts.init(dom.querySelector(strUUID));
      console.log("item.seriesData", seriesData);
      let idx = seriesData.findIndex((item) => item.name === "女");
      let color = [];
      if (idx === 0) {
        color = ["#f4908f", "#1f97d5"];
      } else {
        color = ["#1f97d5", "#f4908f"];
      }
      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "left",
          show: false,
        },
        series: [
          {
            name: "性别",
            type: "pie",
            radius: "80%",
            data: seriesData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
            color: color,
          },
        ],
      };
      let timer = this.saTime;

      // 使用刚指定的配置项和数据显示图表。
      myCharts4.setOption(option);
      setTimeout(() => {
        this.chartCount += 1;
        this.updateSuccess('题目'); // 2
      }, timer);
    },
    initPointChart(citem, index) {
      if (citem.formType === 18) {
        let idx = this.pageContent.findIndex(
          (item) => item.type === "locationDistributionOfTheLocal"
        );
        this.pageContent[idx].pageContent.staffAreaIndex = "（" + 1 + "）";
        this.pageContent[idx].pageContent.staffAreaTitle =
          citem.formTemplateTitle;
        this.pageContent[idx].pageContent.staffAreaObject =
          citem.topicObject.pageContent;
        let chartUuid = "#" + this.uuid + "_locationArea";
        // citem.jj = this.taskUserRegion
        let seriesData = [];
        for (let i = 0; i < this.taskUserRegion.length; i++) {
          seriesData.push({
            value: this.taskUserRegion[i].selectOptionNum,
            name: this.taskUserRegion[i].optionValue,
          });
        }
        citem.belongType = 4;
        this.pageContent[idx].pageContent.showBottom = false;

        citem.seriesData = seriesData;
        this.initProbleDataChart(citem, index, chartUuid);
      } else if (citem.formType === 15) {
        let idx = this.pageContent.findIndex(
          (item) => item.type === "activityResult"
        );
        this.pageContent[idx].pageContent.staffAreaIndex = "（1）";
        this.pageContent[idx].pageContent.staffAreaTitle =
          citem.formTemplateTitle;
        this.pageContent[idx].pageContent.staffSexObject =
          citem.topicObject.pageContent;
        let chartUuid = "#" + this.uuid + "_sexArea";
        // let seriesData = [];
        // for (let i = 0; i < item.se.length; i++) {
        //   seriesData.push({
        //     value: this.taskUserRegion[i].selectOptionNum,
        //     name: this.taskUserRegion[i].optionValue,
        //   });
        // }

        // seriesData = seriesData;
        this.initSector(chartUuid, citem.seriesData);

        // this.initProbleDataChart(citem, index, chartUuid);
      }
    },
    getTargetText(text = '', start = 6) {
      // let start = 3;
      let str = text.substr(0, start);
      let temp = text.substr(start);
      while (temp.length != 0) {
        let t = temp.substr(0, start);
        temp = temp.substr(start);
        str += "\n" + t;
      }
      return str;
    },
    initProbleDataChart(item, idx, pointChartUuid) {
      console.log(idx, "idx=========",item,this.chartCountLength);
      return new Promise((resolve, reject) => {
        let that = this;
        const str = this.uuid;
        let strUUID = "#" + this.uuid + "_" + item.uuid;
        if (pointChartUuid) {
          strUUID = pointChartUuid;
        }
        console.log("strUUID", strUUID);
        const dom = document.getElementById(str);
        //1 实例化对象
        let myCharts4 = window.echarts.init(dom.querySelector(strUUID));
        const colorResult = [
          "rgba(127, 181, 255,0.6)",
          "rgba(166, 228, 239,0.6)",
          "rgba(188, 161, 254,0.6)",
          "rgba(103, 96, 254,0.6)",

          "rgba(232, 177, 0,0.6)",
          "rgba(2, 170, 254,0.6)",
          "rgba(129, 78, 204,0.6)",
          "rgba(203, 130, 81,0.6)",
          "rgba(163, 200, 78,0.6)",
          "rgba(23, 179, 103,0.6)",
        ];
        // let tidx = parseInt(Math.random(10));
        let tidx = idx;
        for (let i = 0; i < item.seriesData.length; i++) {
          let color = "";
          if (!colorResult[tidx]) {
            tidx = 0;
          }
          color = colorResult[tidx];
          tidx += 1;
          item.seriesData[i].itemStyle = {
            color: color,
            opacity: 0.6,
          };
        }
        let timer = this.saTime;
        let belongType = item.belongType;
        //----------------------------------------------

        if (belongType === 4) {
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name, 3);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          console.log("4", item);
          let max = Math.max.apply(null, optionData);
          let min = Math.min.apply(null, optionData);
          let eNum = 100;
          console.log(max, "max");
          if (max < 100) {
            eNum = max + min;
          }
          initColumnarFourChart(myCharts4, optionData, yAxisData, eNum, {
            backgroundColor: "#fff",
          });

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess('题目');
          }, 1000);
        } else if (belongType === 2) {
          // this.initChart(item.seriesData, myCharts4);
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name, 10);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          console.log("2", item);
          initColumnar(myCharts4, optionData, yAxisData);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess('题目');
          }, timer);
        } else if (belongType === 3) {
          // initFixColumnarChart
          // this.initChart(item.seriesData, myCharts4);
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name, 3);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          initColumnarTwoChart(myCharts4, optionData, yAxisData);
          console.log("3", item);
          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess('题目');
          }, timer);
        } else if (belongType === 1) {
          // 传入数据生成 option
          let option = this.getPie3DTwo(item.seriesData, 0.59);
          // 绘制图表
          myCharts4.setOption(option);
          console.log("1", item);
          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess('题目');
          }, timer);
        }
      });
    },
    // 是否特殊租户
    getSpecialItem(data) {
      let isOpenTenantName = data.tenantName;
      let taskMonth = this.taskMonth;
      if(scaleReportImageObject[isOpenTenantName] && scaleReportImageObject[isOpenTenantName] instanceof Object) {
        if(scaleReportImageObject[isOpenTenantName][taskMonth]) {
          this.imageStyleOne = true;
        }
      } 
    },
    // 获取分析报告
    async exportProjectAccurateId() {
      const res = await exportProjectAccurateId(
        {
          taskMonth: this.taskMonth,
          type: this.requestType,
        },
        {
          "no-time-manage": 1,
        }
      );
      const cRes = await todotasksGetReportPreciseDetailFrom(
        {
          taskMonth: this.taskMonth,
          type: this.requestType,
        },
        {
          "no-time-manage": 1,
        }
      );

      const cData = cRes.data;

      const data = res.data;
      this.getSpecialItem(data);
      // 执行人信息
      if (data.userReportVo instanceof Object) {
        this.userReportVo = data.userReportVo || {};

        // 性别信息
        if (this.userReportVo.genderReportVoList) {
          let genderReportVoList = this.userReportVo.genderReportVoList;

          for (let i = 0; i < genderReportVoList.length; i++) {
            for (let j = 0; j < this.sexResult.length; j++) {
              this.sexResultCount = genderReportVoList[i].allNum;
              if (this.sexResult[j].uuid === genderReportVoList[i].gender) {
                let percent =
                  genderReportVoList[i].num !== 0
                    ? (
                        (genderReportVoList[i].num /
                          genderReportVoList[i].allNum) *
                        100
                      ).toFixed(2)
                    : "0.00";
                if (this.sexResult[j].uuid === 2) {
                  this.womanText = percent + "%";
                } else if (this.sexResult[j].uuid === 1) {
                  this.manText = percent + "%";
                }
                this.sexResult[j].value = genderReportVoList[i].num;
                this.sexResult[j].name =
                  this.sexResult[j].cName + percent + "%";
                this.sexResult[j].num = this.sexResult[j].value;
                let str = "sex_" + genderReportVoList[i].gender;
                this.projectExecutorInformation[str] = percent + "%";
                break;
              }
            }
          }
        }
      }

      // 任务信息

      if (data.authStatusGroupVoList instanceof Object) {
        let taskStatusReportVoList = data.authStatusGroupVoList;

        if (taskStatusReportVoList) {
          console.log("taskStatusReportVoList", taskStatusReportVoList);
          let auditResultTotal = 0;
          for (let i = 0; i < taskStatusReportVoList.length; i++) {
            for (let j = 0; j < this.auditResult.length; j++) {
              if (
                this.auditResult[j].uuid ===
                taskStatusReportVoList[i].authStatus
              ) {
                this.auditResult[j].value =
                  taskStatusReportVoList[i].num &&
                  taskStatusReportVoList[i].num !== ""
                    ? taskStatusReportVoList[i].num
                    : 0;
                auditResultTotal += this.auditResult[j].value;

                let percent = taskStatusReportVoList[i].proportion;
                this.auditResult[j].name =
                  this.auditResult[j].cName + percent + "%";
                // this.auditResultTotal = taskStatusReportVoList[i].allNum;

                let str = "audit_" + this.auditResult[j].uuid;
                this.auditObject[str] = percent + "%";
                let str2 = "audit_count_" + this.auditResult[j].uuid;
                this.auditObject[str2] = taskStatusReportVoList[i].num;
                let str3 = "audit_" + this.auditResult[j].uuid + "_text";
                this.auditObject[str3] = taskStatusReportVoList[i].authDesc;

                break;
              }
            }
          }
          this.auditResultTotal = auditResultTotal;
        }
      }
      if (data.taskReportVo instanceof Object) {
        if (data.taskReportVo.taskAllocationReportVoList instanceof Object) {
          this.taskAllocationReportVoList =
            data.taskReportVo.taskAllocationReportVoList;
        }
      } else {
      }

      if (data.demand instanceof Object) {
        if (data.todoTasksList instanceof Object) {
          data.demand.taskNumber = data.todoTasksList.length;
        } else {
          data.demand.taskNumber = 0;
        }
        if (data.taskUserVoList instanceof Object) {
          data.demand.productTaskNumber = data.taskUserVoList.length;
        } else {
          data.demand.productTaskNumber = 0;
        }
        data.demand.createTimeText = format(
          data.demand.createTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        let date = new Date(data.demand.createTime);
        // data.demand.monthText = date.getMonth() + 1;
        data.demand.monthText =
          date.getFullYear() + "-" + (date.getMonth() + 1);
        data.demand.yearText = date.getFullYear();
        data.demand.startTime = format(data.demand.startTime, "YYYY-MM-DD");
        data.demand.endTime = format(data.demand.endTime, "YYYY-MM-DD");

        let idx = this.topResult.findIndex((item) => item.id === "taskUserNum");
        this.topResult[idx].value = data.demand.taskUserNum;

        this.demandInfo = data.demand;
      }

      console.log("data.answerReportVoList", cData.dynamicFormList);

      if (data.answerReportVoList instanceof Object) {
        const answerReportVoList = data.answerReportVoList;
        this.answerReportVoList = answerReportVoList;
        // chartCount:0,
        this.chartCountLength = data.answerReportVoList.length;
        this.chartCount = 0;
      }

      if (data.research instanceof Object) {
        this.research.title = data.research.title;
        let idx = this.topResult.findIndex((item) => item.id === "title");
        this.topResult[idx].value = data.research.title;

        let allContentIdx = this.pageContent.findIndex(
          (item) => item.type === "allContent"
        );

        this.pageContent[allContentIdx].pageContent.backgroundContentHtml =
          data.research.background || "";
        this.pageContent[allContentIdx].pageContent.methodSampleContentHtml =
          data.research.methodSample || "";
        this.pageContent[allContentIdx].pageContent.resultAnalysisContentHtml =
          data.research.resultAnalysis || "";
        this.pageContent[
          allContentIdx
        ].pageContent.resultConclusionContentHtml =
          data.research.resultConclusion || "";

        let idx3 = this.topResult.findIndex((item) => item.id === "monthText");
        this.topResult[idx3].value = data.research.taskMonthly;
        this.monthText = data.research.taskMonthly;

        let idx4 = this.topResult.findIndex(
          (item) => item.id === "createTimeText"
        );
        this.topResult[idx4].value = data.research.reportTimeStr;
        this.collectionType = data.research.collectionType;
        let hidden = this.collectionType == 4;
        let iidx = this.pageContent.findIndex(
          (item) => item.type === "projectExecutorInformation"
        );
        if (iidx !== -1) {
          this.pageContent[iidx].hidden = hidden;
        }
      }
      // 任务主体
      if (data.todoTasksList instanceof Object) {
        this.fixedFieldObject.productTaskNumber = data.todoTasksList.length;
      }
      if (data.operatingList instanceof Object) {
        let todoTasksList = data.operatingList;
        let sceneImages = [];
        oneRoot: for (let i = 0; i < todoTasksList.length; i++) {
          let imageIds = todoTasksList[i].imageIds;
          if (imageIds !== "") {
            let arr = imageIds.split(",");
            for (let k = 0; k < arr.length; k++) {
              if (sceneImages.length < 9) {
                sceneImages.push({
                  url: domainURL(arr[k]) + "?x-oss-process=image/auto-orient,1",
                });
              } else {
                break oneRoot;
              }
            }
          }
        }
        this.eventSceneDisplayImage = sceneImages;
      }
      if (data.operatingImageList instanceof Object) {
        let cidx = this.pageContent.findIndex(
          (item) => item.type === "executiveTeamPresentation"
        );
        if(this.imageStyleOne) {
          this.pageContent[cidx].start = 0
          this.pageContent[cidx].end = 1
        } else if(this.userImageBoolean) {
          this.pageContent[cidx].start = 0;
          this.pageContent[cidx].end = this.userImageCount
        }
        let imagePathArr = [];
        for(let i=0;i<data.operatingImageList.length;i++) {
          let url = data.operatingImageList[i];
          if (imagePathArr.length === this.maxImageCount) {
            break;
          }
          if(url === '') {
            continue
          }
          if(maxSizeReportImageList.includes(url)) {
            imagePathArr.push({
              url: domainURL(url)
            })
          } else {
            imagePathArr.push({
              url: domainURL(url) + "?x-oss-process=image/auto-orient,1"
            })
          }
        }
        this.pageContent[cidx].pageContent.signInLogListImage = imagePathArr;
        this.signInLogListImage = imagePathArr;
      }
      this.userVisitCountVos = [];
      this.activityResultList = [];
      if(Array.isArray(cData)) {
        for(let u=0;u<cData.length;u++) {
          let cItem = cData[u];
          if (Array.isArray(cItem.dynamicFormList)) {
            let dynamicFormList = cItem.dynamicFormList.map(item => {
              if(item['科室/疾病'] && item['科室/疾病'] != '') {
                item['疾病'] = item['科室/疾病'];
              }
              if(item['疾病'] && item['疾病'] != '') {
                item['科室/疾病'] = item['疾病'];
              }
              for(let vkey in item) {
                if((vkey.includes('科室') || vkey.includes('疾病')) && vkey.includes('/') && item[vkey] && item[vkey] !== '') {
                  item['科室/疾病'] = item[vkey];
                  item['疾病'] = item[vkey];
                }
              }

              return item;
            });
            this.userVisitCountVos = this.userVisitCountVos.concat(dynamicFormList);
          } else {
            // this.userVisitCountVos = [];
          }
          if (cItem.answerPreciseReportVoList instanceof Object) {
            const answerPreciseReportVoList = cItem.answerPreciseReportVoList.sort(
              (a, b) => a.formType - b.formType
            );
            let allWriteOptionNum = 0;
            for (let i = 0; i < answerPreciseReportVoList.length; i++) {
              allWriteOptionNum = answerPreciseReportVoList[i].allWriteOptionNum;
              // healthInfo
              // basicInfo
              if (answerPreciseReportVoList[i].formType == 16) {
                for (
                  let j = 0;
                  j < answerPreciseReportVoList[i].answerOptionVoList.length;
                  j++
                ) {
                  let optionValue =
                    answerPreciseReportVoList[i].answerOptionVoList[j].optionValue;
                  if (optionValue == "age_18_30") {
                    this.basicInfo.age_18_30 =
                      answerPreciseReportVoList[i].answerOptionVoList[
                        j
                      ].selectOptionProportion;
                  } else if (optionValue == "age_31_40") {
                    this.basicInfo.age_31_40 =
                      answerPreciseReportVoList[i].answerOptionVoList[
                        j
                      ].selectOptionProportion;
                  } else if (optionValue == "age_41_50") {
                    this.basicInfo.age_41_50 =
                      answerPreciseReportVoList[i].answerOptionVoList[
                        j
                      ].selectOptionProportion;
                  } else if (optionValue == "age_51_70") {
                    this.basicInfo.age_51_70 =
                      answerPreciseReportVoList[i].answerOptionVoList[
                        j
                      ].selectOptionProportion;
                  } else if (optionValue == "age_71_100") {
                    this.basicInfo.age_71_100 =
                      answerPreciseReportVoList[i].answerOptionVoList[
                        j
                      ].selectOptionProportion;
                  }
                }
              }
              if (answerPreciseReportVoList[i].formType == 15) {
                for (
                  let j = 0;
                  j < answerPreciseReportVoList[i].answerOptionVoList.length;
                  j++
                ) {
                  let optionValue =
                    answerPreciseReportVoList[i].answerOptionVoList[j].optionValue;
                  if (optionValue == "男") {
                    this.basicInfo.man =
                      answerPreciseReportVoList[i].answerOptionVoList[
                        j
                      ].selectOptionProportion;
                  } else if (optionValue == "女") {
                    this.basicInfo.girl =
                      answerPreciseReportVoList[i].answerOptionVoList[
                        j
                      ].selectOptionProportion;
                  }
                }
              }
            }
            this.activityResultList = this.activityResultList.concat(answerPreciseReportVoList);
            this.activityResultObject.allWriteOptionNum += allWriteOptionNum;
            // this.chartCountLength = data.answerPreciseReportVoList.length;
            this.chartCount = 0;
          }
          this.filterData();
          this.chartCountLength = this.activityResultList.length
        }
      }
      if (data.taskUserRegion instanceof Object) {
        let taskUserRegion = data.taskUserRegion;
        let tdx = this.pageContent.findIndex(
          (item) => item.type === "locationDistributionOfTheLocal"
        );
        this.pageContent[tdx].pageContent.taskUserRegion = taskUserRegion.map(
          (item) => {
            item.selectOptionProportion += "%";
            return item;
          }
        );
        this.taskUserRegion = taskUserRegion;

        if (this.taskUserRegion.length !== 0) {
          let idx = this.activityResultList.findIndex(
            (item) => item.formType === 18
          );
          if (idx === -1) {
            this.activityResultList.unshift({
              flowConfigId: "",
              flowConfigOrder: 1,
              formTemplateId: "",
              formTemplateOrder: 4,
              formTemplateTitle: "省市",
              formType: 18,
              mandatoryStatus: 1,
              submitId: "",
              answerOptionVoList: [],
              allWriteOptionNum: 0,
            });
            this.chartCountLength = this.activityResultList.length
          }
        }
        // this.$nextTick(() => {
        //   this.setTaskUserRegion();
        // });
      }

      // 精准报告
      if([3,6].includes(this.requestType) && data.taskUserVoList instanceof Object) {
        this.taskUserVoList = data.taskUserVoList;
        this.$nextTick(() => {
          this.taskUserVoListUpdateCount += 1;
        })
      } else if([3,6].includes(this.requestType)) {
        this.updateSuccess();
      }

      // 服务方
      this.fixedFieldObject.serviceProvider = data.tenantName;

      // 初始化执行时间
      this.initTime();
    },
    filterData() {
      let logCount = 0;
      this.activityResultList = this.activityResultList.filter((item) => {
        if([19,23].includes(item.formType)) {
          logCount += 1;
          if(item.answerOptionVoList.length === 0 && logCount !== 1) {
            return false;
          }
        }
        return true;
      })
    },
    // 计算距离顶部高度
    setTaskUserRegion() {
      let dom = document.getElementById("taskUserRegionSpace");
      let cdom = document.getElementById("locationDistributionOfTheLocal");
      let cOffsetTop = cdom.offsetTop;
      let offsetTop = dom.offsetTop;
      let ch = offsetTop - cOffsetTop;
      this.taskUserRegionSpaceTop = 0;
      if (this.pageSize.height - ch < 450) {
        this.taskUserRegionSpaceTop = this.pageSize.height - ch - 10;
      }
      console.log("this.taskUser", this.taskUserRegionSpaceTop, offsetTop, ch);
    },
    // 项目执行人任务数量分析
    initProductAnalysis() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);

      // taskAllocationReportVoList
      let cData = [];
      var obj = {
        num_0_18: 1,
        num_18_36: 2,
        num_36_54: 3,
        num_54_72: 4,
        num_72_90: 5,
        num_72_: 6,
      };
      for (let key in obj) {
        cData.push(0);
      }
      for (let i = 0; i < this.taskAllocationReportVoList.length; i++) {
        let idxx = obj[this.taskAllocationReportVoList[i].descValue];
        if (idxx) {
          cData[idxx - 1] = this.taskAllocationReportVoList[i].num;

          let percent = (
            (this.taskAllocationReportVoList[i].num /
              this.taskAllocationReportVoList[i].allNum) *
            100
          ).toFixed(2);
          this.productAnalysisInfo[
            this.taskAllocationReportVoList[i].descValue
          ] = percent + "%";
          // this.taskAllocationReportVoList[i]
          let str2 = this.taskAllocationReportVoList[i].descValue + "_count";
          this.productAnalysisInfo[str2] =
            this.taskAllocationReportVoList[i].num;
          // this.productAnalysisInfo[this.taskAllocationReportVoList[i].descValue] = this.taskAllocationReportVoList[i].num;
        }
      }
      console.log("cData", cData, this.taskAllocationReportVoList);

      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".barTarget .chart")
      );

      let option4 = {
        grid3D: {},
        xAxis3D: {
          type: "value",
        },
        yAxis3D: {
          type: "category",
          data: ["0-18", "18-36", "36-54", "54-72", "72-90", "90单以上"],
          axisLabel: {
            formatter: "{value}",
          },
        },
        zAxis3D: {},
        // xAxis: {
        //   type: "value",
        // },
        // yAxis: {
        //   type: "category",
        //   data: ["0-18", "18-36", "36-54", "54-72", "72-90", "90单以上"],
        //   axisLabel: {
        //     formatter: "{value}",
        //   },
        //   // data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        // },
        series: [
          {
            data: cData,
            // type: "bar",
            type: "bar3D",
          },
        ],
      };

      //3 把配置给实例对象
      initColumnar(myCharts4, cData);
      // myCharts4.setOption(option4);
      //4 让图标自适应
      window.addEventListener("resize", function () {
        myCharts4.resize();
      });
    },
    async loadChart() {},
    async updateSuccess(type) {
      console.log('入口',type)
      // if (!is) {
      this.initsuccesscount += 1;
      // }

      console.log(
        "this.initsuccesscount",
        this.initsuccesscount,
        this.initsuccesscount ===
        this.targetCount + this.chartCountLength,
        '题目数量',
        this.chartCountLength,
        this.targetCount + this.chartCountLength,

      );

      if (
        this.initsuccesscount ===
        this.targetCount + this.chartCountLength
      ) {
        await this.loadChart();
        this.pageLoading = false;
        // let timer = this.timer;
        let timer = 2000;

        this.$nextTick(() => {
          this.initpage();
          setTimeout(() => {
            this.$emit("compute", {});
            this.addComputeTag();
          }, timer);
        });
      }
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    initpage() {
      this.authHeightResult = {};
      for (let i = 0; i < this.pageContent.length; i++) {
        if (this.pageContent[i].authHeight) {
          let id = "authHeight" + i;
          this.authHeightResult[i] = id;
        }
      }

      this.$nextTick(() => {
        this.save();
      });
    },
    // 页面的倍数
    initHeight(height) {
      let pagecount = 1;
      while (height > this.pageSize.height) {
        pagecount += 1;
        height -= this.pageSize.height;
      }

      return pagecount * this.pageSize.height;
    },
    // 保存前初始化页面
    save() {
      for (let key in this.authHeightResult) {
        let id = this.authHeightResult[key];
        let dom = document.getElementById(id);
        if(!this.pageContent[key].hidden) {
          console.log(dom.clientHeight, this.pageContent[key]);
          this.pageContent[key].targetHeight = this.initHeight(dom.clientHeight);
        }
      }
      this.$forceUpdate();
    },
  },
};