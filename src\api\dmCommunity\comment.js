/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 评论
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/comment/delete/batch/${data.ids}`)
}
// 根据id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/comment/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/comment/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/comment/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/comment/query/one`, data)
}

// 评论审核分页
export function queryPage(data,expandHeaders = {}) {
    return requestV1.postJson(`${prefix}/comment/query/page`, data, null, expandHeaders);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/comment/update`, data)
}

// 新增点赞数
export function increateLikenumber (data) {
    return requestV1.postForm(`${prefix}/comment/increate/likenumber`, data)
}

// 获取评论层级-分页
export function queryLevelPage (data) {
    return requestV1.postJson(`${prefix}/comment/query/level/page`, data)
    // return requestV1.postJson(`${prefix}/comment/query/page`, data)
}

// 保存通用评论数据
export function postMessageComment (data) {
    return requestV1.postJson(`${prefix}/comment/post/message/comment`, data)
}

// 新增点赞数
export function increaseLikenumber (data) {
    return requestV1.postForm(`${prefix}/comment/increase/likenumber`, data)
}

// 取消点赞
export function reduceLikenumber (data) {
    return requestV1.postForm(`${prefix}/comment/reduce/likenumber`, data)
}

// 修改审核状态
export function updateProcessStatus (data) {
    return requestV1.postForm(`${prefix}/comment/update/process/status`, data)
}

// 修改上下架状态
export function updatePutawayStatus (data) {
    return requestV1.postForm(`${prefix}/comment/update/putaway/status`, data)
}

// 置顶评论
export function commentUpdateToppedStateBatch (data) {
  return requestV1.postJson(`${prefix}/comment/update/topped/state/batch`, data)
}

// 批量删除评论
export function commentBatchDelete (data) {
  return requestV1.deleteForm(`${prefix}/comment/delete/batch/${data.ids}`, )
}

export function commentQueryNotenantPage(data,expandHeaders = {}) {
  return requestV1.postJson(`${prefix}/comment/query/notenant/page`, data, null, expandHeaders)
}

export function commentQueryNotenantPageV2(data,expandHeaders = {}) {
  return requestV1.postJson(`/dm/api/v2/comment/query/notenant/page`, data, null, expandHeaders)
}