<template>
  <el-pagination
    v-if="page.total!=0"
    :current-page="page.current"
    :page-sizes="[10, 20, 30, 40,50]"
    :page-size="page.size"
    :total="page.total"
    class="gb-pagination"
    background
    layout="total, sizes, prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"/>
</template>

<script>
import { scrollTo } from '@/utils/scroll-to'
export default {
  name: 'Pagination',
  props: {
    page: {
      type: Object,
      required: true
    }
  },
  data() {
    return {

    }
  },
  methods: {
    handleSizeChange(val) {
      // this.page.size = val
      this.scroll()
      this.$emit('updatePage', 'size', val)
    },
    handleCurrentChange(val) {
      // this.page.number = val
      this.scroll()
      this.$emit('updatePage', 'current', val)
    },
    scroll() {
      if (this.autoScroll) {
        scrollTo(0, 800)
      }
    }
  }
}
</script>

<style scoped>
  .gb-pagination{
    margin-top: 20px;
  }
</style>
