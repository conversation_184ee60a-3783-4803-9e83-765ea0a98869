<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">
    <el-color-picker
      :class="childClass"
      v-model="form.data.color"/>
  </el-form-item>
</template>

<script>
export default {
  name: 'Color',
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '颜色选择',
          name: 'color',
          rules: [
            { required: true, message: '颜色选择', trigger: 'blur' }
          ]
        }
      }
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    data: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          color: ''
        }
      }
    }
  },

  watch: {
    data: {
      handler(val) {
        this.form.data.color = val
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.color)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">

</style>
