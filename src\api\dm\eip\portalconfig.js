/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 企业门户配置
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/portalconfig/insert`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/portalconfig/update`, data)
}

// 获取倒序第一条数据
export function queryOnlyOne (data) {
    return requestV1.get(`${prefix}/portalconfig/query/only/one`, data)
}
