import requestV1 from '@/common/utils/modules/request'

const prefix = '/sop/api/v1'

/**
 * sop 商品管理
 */
// 批量删除
export function deleteBatch (data) {
  return requestV1.deleteForm(`${prefix}/goods/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
  return requestV1.deleteForm(`${prefix}/goods/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/goods/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/goods/query/page`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/goods/query/list`, data);
}

// 根据id查询
export function queryOne (data) {
  return requestV1.get(`${prefix}/goods/query/one`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/goods/update`, data)
}

