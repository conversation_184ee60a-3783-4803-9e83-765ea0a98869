<!--form表单的单选框组件-->
<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">

    <el-radio-group
      :class="childClass"
      v-model="form.data.radio">
      <el-radio
        v-for="(item) in array"
        :key="item.key"
        :label="item.key">{{ item.value }}</el-radio>
    </el-radio-group>

  </el-form-item>

</template>

<script>
// import dic from '@/service/api/dic'
import common from '@/common/utils'
export default {
  name: 'Radio',
  props: {
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '默认',
          name: 'radio',
          rules: [
            { required: true, message: '请选择单选框', trigger: 'blur' }
          ],
          array: [],
          dicKey: ''
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: ''
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          radio: ''
        }
      },
      array: []
    }
  },
  watch: {
    data: {
      handler(val) {
        val = val.trim()
        if (this.$validate.isNull(val)) {
          this.form.data.radio = this.array[0].key + ''
        } else {
          this.form.data.radio = '' + val
        }
      }
    },
    config: {
      handler(val) {
        if (val.array.length > 0) {
          this.array = val.array
        }
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.radio)
      },
      deep: true
    }
  },
  mounted() {
    const that = this
    that.form.data.radio = this.data
    this.getDic(() => {
      if (this.form.data.radio === '') {
        that.form.data.radio = that.array[0].key + ''
        this.$emit('updateForm', '' + this.config.name, this.form.data.radio)
      }
    })
  },
  methods: {
    /**
     * 当传入的参数配置array为''且dicKey的值不为空，请求获取dicKey的数据字典
     * @param fn (处理数据函数)
     */
    getDic(fn) {
      const ar = this.config.array
      if (ar.length > 0) {
        this.array = ar
        return
      }
      if (!this.$validate.isNull(this.config.dicKey)) {
          this.$api.dic.getDicInfo({ 'dictType': this.config.dicKey }, (res) => {
          this.array = res
          fn()
        })
      }
    }
  }
}
</script>

<style lang="scss">

</style>
