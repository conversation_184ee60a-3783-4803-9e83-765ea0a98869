<template>
  <div class="pageContent" v-loading="pageLoading">
    <template v-for="(page, index) in pageContent">
      <div
        class="everyPage"
        :key="index"
        :style="{
          width: pageSize.width + 'px',
          height: pageSize.height + 'px',
        }"
        v-if="!page.authHeight"
      >
        <div class="everyPageContent">
          <!-- 封面 -->
        </div>
      </div>
      <div
        :key="index"
        v-else-if="page.authHeight"
        :style="{
          width: pageSize.width + 'px',
          height: page.targetHeight ? page.targetHeight + 'px' : 'auto',
        }"
        :id="authHeightResult[index]"
      >
        <div class="everyPageContent">
          <!-- 结算报告 -->
          <div
            class="settlement-page"
            v-if="page.type === 'surveyOfQuestionnaireActivities'"
          >
            <template
              v-for="(cItem, index) in page.pageContent.surveyDataResult"
            >
              <div class="templateBox" :key="index">
                <div class="table-header-title">
                  {{ cItem.filename }}数据报告（时间{{ cItem.planTimef }}）
                </div>

                <template
                  v-if="
                    cItem.tableData.length !== 0 || cItem.hearders.length !== 0
                  "
                >
                  <el-table
                    :data="cItem.tableData"
                    header-row-class-name="header-row-class-name"
                    border
                    style="width: 100%"
                    :style="{
                      'min-width': serveHeadersWidth + 'px',
                      zoom: serveHeadersWidthScale,
                    }"
                  >
                    <el-table-column type="index" width="50px" label="序号">
                    </el-table-column>
                    <template v-for="item in cItem.hearders">
                      <el-table-column
                        :key="item.key"
                        :prop="item.key"
                        :label="item.title"
                        :width="item.width"
                      >
                        <template slot-scope="scope">
                          <div
                            class="exporttxt"
                            :style="scope.row[item.key + 'Style'] || ''"
                          >
                            {{ scope.row[item.key] }}
                          </div>
                        </template>
                      </el-table-column>
                    </template>
                  </el-table>
                </template>
                <div class="no-data" v-else>暂无数据</div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { getVisitingplan as queryOne } from "@/api/dm/visiting/visitingplan";

import { queryList } from "@/api/dm/visiting/visitingplanobjectlist.js";

import { format } from "@/utils/index";

import { getdrugstoreFeedbackList } from "@/utils/enumeration.js";

import { researchGetReport } from "@/api/research.js";

export default {
  // name: "retailPharmacyVisitDataReport",
  props: {
    taskId: {
      type: [Number, String],
      default: null,
    },
    preview: {
      type: Boolean,
      default: true,
    },
    // 拼接域名 lvbao不支持本地调式图片
    domainUrl: {
      type: String,
      default: "https://lvbao-saas.oss-cn-shenzhen.aliyuncs.com/",
    },
    domainResult: {
      type: Array,
      default: function () {
        return ["https://file.greenboniot.cn/"];
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    filename: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      serveHeadersWidth: 1123,
      serveHeadersWidthScale: 0,
      // 横向就变成 宽 1123 高 794
      pageSize: {
        width: 1123,
        height: 794,
      },
      // 导出数据的标题
      pageContent: [
        // 问卷活动调研
        {
          text: "问卷活动调研",
          type: "surveyOfQuestionnaireActivities",
          authHeight: true,
          pageContent: {
            // 调研数据报告
            surveyDataResult: [],
          },
        },
      ],
      pageLoading: false,
      authHeightResult: [],
      initsuccesscount: 0,
      targetCount: 1,
      // 导出类型
      exportVisitType: 3,
    };
  },
  mounted() {},
  watch: {
    // heardersWidth(n) {
    //   this.$emit("initTableWidth", n + 50);
    // },
    updatecount(n) {
      this.pageLoading = true;
      this.initsuccesscount = 0;
      // this.targetCount = 10000;
      // this.initpage();
      this.initpageData();
    },
  },
  methods: {
    addComputeTag() {
      // class=“pdf_finish”
      const dom = document.createElement("div");
      dom.classList = "pdf_finish";
      document.body.append(dom);
    },
    // 获取任务节点
    async getNodeData() {
      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );
      this.pageContent[idx].pageContent.surveyDataResult = [];

      const res = await researchGetReport({
        id: this.taskId,
      });

      const data = res.data;
      console.log("data--", data);
      const cTableData = data.dynamicDataList;
      const targetHeaders = [];
      let clientWidth = 0;

      if (cTableData.length > 0) {
        const headers = cTableData[0];

        for (let key in headers) {
          // 企业推广
          if(data.collectionType === 4 && (key === '提交人' || key === '手机号码')){
            continue
          }
          targetHeaders.push({
            key: key,
            title: key,
            width: 100,
          });
          clientWidth += 100;
        }
      } else if (cTableData.length === 0) {
        let headers = data.templateList;

        for (let i = 0; i < headers.length; i++) {
          targetHeaders.push({
            key: headers[i].title,
            title: headers[i].title,
            width: 100,
          });
          clientWidth += 100;
        }
      }

      if (clientWidth > this.pageSize.width) {
        this.serveHeadersWidth = clientWidth + 100 + (targetHeaders.length * 2);
        targetHeaders[targetHeaders.length - 1].width = null;
        this.serveHeadersWidthScale = (
          this.pageSize.width / this.serveHeadersWidth
        ).toFixed(2);
      } else if(targetHeaders[targetHeaders.length - 1]){
        targetHeaders[targetHeaders.length - 1].width = null;
      }
      console.log("targetHeaders", targetHeaders);

      this.pageContent[idx].pageContent.surveyDataResult.push({
        tableData: cTableData,
        hearders: targetHeaders,
        filename: data.title,
        planTimef:
          data.startTimeStr +
          "至" +
          data.endTimeStr,
      });

      this.updateSuccess();

      console.log();

      // this.pageContent[idx].
      // let targetHeaders = [];

      this.initCountHeader();
    },
    initCountHeader() {
      let number = 0;
      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      for (
        let i = 0;
        i < this.pageContent[idx].pageContent.surveyDataResult.length;
        i++
      ) {
        let hearders =
          this.pageContent[idx].pageContent.surveyDataResult[i].hearders;
        let cCount = 0;

        for (let j = 0; j < hearders.length; j++) {
          cCount += hearders[j].width;
        }

        if (cCount > number) {
          number = cCount;
        }
      }

      console.log("number", number);

      this.hearderWidth = number + 50;

      if (this.pageSize.width < this.hearderWidth) {
        this.$emit("updateWidth", this.hearderWidth);
      }
    },
    // 获取拜访详情
    async queryOne() {
      const res = await queryOne({ id: this.taskId, isCurrentUser: 2 });

      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (idx !== -1) {
        console.log("kkk");
        this.pageContent[idx].pageContent.articleContent = res.data.content;
        this.exportVisitType = res.data.type - 0;

        //  item.value = [res.data.startTime, res.data.endTime];
        this.pageContent[idx].pageContent.planTime =
          res.data.startTime + " 至 " + res.data.endTime;

        this.$forceUpdate();
      }

      this.$nextTick(() => {
        setTimeout(() => {
          this.updateSuccess();
        }, 2000);
      });

      // this.updateSuccess();

      console.log("res", res);
    },

    // 获取拜访列表
    async getVisitCordList(type) {
      const res = await queryList({
        type: type,
        planId: this.taskId,
      },{
        "no-time-manage": 1
      });

      const data = res.data;
      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (type === 1) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.defaultTableData = data;
        }
      } else if (type === 2) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.characterTableData = data;
        }
      } else if (type === 3) {
        if (idx !== -1) {
          const drugstoreFeedbackArr = getdrugstoreFeedbackList();

          this.pageContent[idx].pageContent.pharmacyTableData = data.map(
            (item) => {
              item.writeTimeText = format(item.writeTime,'YYYY-MM-DD HH:mm:ss');
              item.drugstoreFeedbackText = this.getEnumText(
                item.drugstoreFeedback,
                drugstoreFeedbackArr
              );
              return item;
            }
          );
        }
      }

      this.$nextTick(() => {
        this.updateSuccess();
      });

      // return data;
    },
    updateSuccess() {
      this.initsuccesscount += 1;

      // console.log(
      //   "this.initsuccesscount",
      //   this.initsuccesscount,
      //   this.targetCount
      // );

      if (this.initsuccesscount === this.targetCount) {
        this.pageLoading = false;
        this.$nextTick(() => {
          // this.initpage();

          // this.$nextTick(() => {
          //   this.$emit("compute", {});
          // });
          setTimeout(() => {
            this.addComputeTag();
            this.$emit("compute", {});
          }, 2000);
        });
      }
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    initpageData() {
      // 获取详情
      this.getNodeData();
    },

    initpage() {
      this.authHeightResult = {};
      for (let i = 0; i < this.pageContent.length; i++) {
        if (this.pageContent[i].authHeight) {
          let id = "authHeight" + i;
          this.authHeightResult[i] = id;
        }
      }

      this.$nextTick(() => {
        this.save();
      });
    },
    // 页面的倍数
    initHeight(height) {
      let pagecount = 1;
      while (height > this.pageSize.height) {
        pagecount += 1;
        height -= this.pageSize.height;
      }

      return pagecount * this.pageSize.height;
    },
    // 保存前初始化页面
    save() {
      for (let key in this.authHeightResult) {
        let id = this.authHeightResult[key];

        let dom = document.getElementById(id);
        console.log(dom.clientHeight, this.pageContent[key]);
        this.pageContent[key].targetHeight = this.initHeight(dom.clientHeight);
      }
      this.$forceUpdate();
    },
  },
};
</script>




<style lang="scss" scoped>

.no-data {
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}
.templateBox {
  width: 100%;
  margin-bottom: 50px;

  .table-header-title {
    height: 40px;
    display: flex;
    align-items: center;
    font-weight: 550;
    justify-content: center;
    line-height: 1.5;
    font-size: 14px;
    background: #dfe6ec;
    border-bottom: 1px solid #dbdbdb;
  }
}
::v-deep .header-row-class-name th {
  background: #dfe6ec;
}

.mgt30 {
  margin-top: 30px;
}
.pageContent {
  // width: 100%;
  margin: 0 auto;
  background: #fff;
}
.everyPage {
  overflow: hidden;
  padding: 0 0px 5px;
  box-sizing: border-box;
}
.everyPageContent {
  width: 100%;
  height: 100%;
  background: #fff;
}
// 封面
.cover-page {
  padding-top: 100px;
  padding-left: 50px;
  height: 100%;
  overflow: hidden;
  background: #fff;
  box-sizing: border-box;

  .cover-page-bottom-txt {
    // display:flex;
    margin-top: 50px;
    line-height: 2;
    font-size: 18px;
    text-align: center;
  }
  .cover-text-box-info {
    font-size: 16px;
    color: #7c7c7c;
  }

  .cover-text-box-bottom {
    position: absolute;
    bottom: 100px;
    left: 50px;
    color: #fff;
  }

  .cover-text-box-title {
    font-size: 40px;
    color: #fff;
  }

  .cover-page-icon {
    margin-bottom: 30px;
  }

  .cover-page-img-box {
    position: relative;
  }

  .cover-text-box {
    position: absolute;
    top: 100px;
    left: 30px;
  }

  .cover-page-img {
    width: 100%;
  }

  .cover-text-box-info {
    color: #fff;
    font-size: 24px;
  }
}
// 目录
.directory-page {
  padding: 20px;
  .directory-title {
    font-size: 28px;
    line-height: 2;
    font-weight: 550;
    margin-bottom: 30px;
  }
}

// 问卷活动调研
.serve-page {
  padding: 20px;

  .serve-page-title {
    height: 50px;
    // background: #3a78f1;
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 550;
    // color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0e0800;
  }

  .serve-page-execute-time {
    text-align: center;
    font-weight: 550;
    margin-bottom: 10px;
  }

  .serve-page-execute-sub1 {
    font-weight: 550;
    margin-bottom: 20px;
  }

  .serve-page-execute-center-t1 {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
    font-weight: 550;
  }

  .serve-page-execute-introduction {
    text-indent: 2em;
    color: #7c7c7c;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 40px;
  }

  .serve-cols {
    display: flex;
    align-items: center;
    height: 40px;
    // border-bottom: 1px solid #000;
    border-top: none;
  }
  .serve-col {
  }
  .serve-cols-title {
    padding: 20px;
    background: #ffefef;
    // border: 2px solid #de4040;
    line-height: 1.5;
    color: #de4040;
  }
  .border-bottom {
    border-bottom: 1px solid #000;
  }
  .serve-col-span {
    width: 130px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .serve-col-label {
    padding-left: 30px;
  }
  .serve-cols-info {
    line-height: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #edf3ff;
    color: blue;
  }
}

.person-serve-detail {
  padding: 20px;

  .person-serve-detail-title {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 550;
    font-size: 18px;
  }
  .person-serve-detail-until {
    display: flex;
    justify-content: flex-end;
  }
}

.one {
  background: yellow;
}
.two {
  background: blue;
}
</style>
