/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/auth/api/v1'


//查询所有系统
export function queryList(data) {
    return requestV1.get(prefix + '/systeminfo/query/list', data);
}

//根据主键集合字符串批量删除数据
export function deleteBatch(data) {
    return requestV1.deleteForm(`${prefix}/systeminfo/delete/batch/${data}`);
}

//根据主键id指定删除
export function deleteOne(data) {
    return requestV1.deleteForm(`${prefix}/systeminfo/delete/one/${data}`);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/systeminfo/insert', data);
}

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/systeminfo/query/one', data);
}

//分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/systeminfo/query/page', data);
}

//根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(prefix + '/systeminfo/query/param', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/systeminfo/update', data);
}

//更新角色和资源缓存
export function cleanCache(data) {
    return requestV1.get(prefix + '/systeminfo/clean/role/permission/cache', data);
}