/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

const prefix = '/dm/api/v1'

/**
 * 积分商品分页
 */


// 积分商品分页 分页
export function pointgiftQueryPage (data) {
    return requestV1.postJson(`${prefix}/pointgift/query/page`, data)
}
// 新增积分商品
export function pointgiftInsert(data){
    return requestV1.postJson(`${prefix}/pointgift/insert`, data)
}
// 更新积分商品
export function pointgiftUpdate(data){
    return requestV1.putJson(`${prefix}/pointgift/update`, data)
}
// 积分商品 批量删除
export function pointgiftDeleteBatch (ids) {
    return requestV1.deleteJson(`${prefix}/pointgift/delete/batch/${ids}`)
}
// 积分商品 指定删除
export function pointgiftDeleteOne (id) {
    return requestV1.deleteJson(`${prefix}/pointgift/delete/one/${id}`)
}
// 批量上下架
export function pointgiftUpdateStateBatch(data){
    return requestV1.postJson(`${prefix}/pointgift/updateStateBatch`, data)
}
// 查询默认每月限兑配置
export function pointgiftexchangeconfigQueryDefault(data){
    return requestV1.get(`${prefix}/pointgiftexchangeconfig/query/default`, data)
}
// 更新积分礼物每月兑换数量
export function pointgiftexchangeconfigUpdate(data){
    return requestV1.putJson(`${prefix}/pointgiftexchangeconfig/update`, data)
}

