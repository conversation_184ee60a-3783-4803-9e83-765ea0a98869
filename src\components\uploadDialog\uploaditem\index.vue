<template>
  <!-- v-if="seekCountList !== 0" -->

  <div class="nestPicture">
    <!-- 已上传文件 -->
    <div
      v-for="(item, index) in picarr"
      :key="item.index"
      class="print printup"
    >
      <img :src="domainURL(item.url)" alt="" />
      <div class="mask">
        <!-- <div class="masktools" @click="remove(index)">删除</div> -->
        <div class="masktools">
          <i class="closeico el-icon-zoom-in" @click="zoom(item.url)"></i>
          <!-- 禁用状态无法移除 -->
          <i class="el-icon-delete closeico" v-if="!disabled" @click="remove(index)"></i>
        </div>
      </div>
      <template v-if="(ischecked || onlyshow) && update">
        <div class="checkbox" @click="showDialog">
          <el-checkbox v-model="item.checked"></el-checkbox>
        </div>
      </template>
      <!-- <i class="isShow hx-icon el-icon-circle-close" @click="cannelUpload(index)"></i> -->
    </div>
    <!-- <template > -->
    <div
      v-for="(item, index) in picture"
      :key="item.index"
      class="print"
      :class="item.success ? '' : 'printup beforeUpload'"
    >
      <!-- 关闭 -->
      <!-- <div class="closeico el-icon-circle-close"></div> -->
      <img :src="item.graph" class="img" alt="" />
      <!-- <el-image
        :src="item.graph"
        class="img"
        :preview-src-list="[item.graph]"
        :ref="'image' + index"
      >
      </el-image> -->
      <!-- -->
      <el-progress
        v-show="!item.success && item.start"
        :percentage="item.percent"
        class="progress2"
        :text-inside="true"
        :stroke-width="15"
      ></el-progress>

      <div class="mask">
        <div class="masktools">
          <i class="closeico el-icon-zoom-in" @click="zoom(item.graph)"></i>
          <i class="el-icon-delete closeico" @click="cannelUpload(index)"></i>
        </div>
      </div>

      <template v-if="(ischecked || onlyshow) && update">
        <div class="checkbox" @click="showDialog">
          <el-checkbox v-model="item.checked"></el-checkbox>
        </div>
      </template>

      <!-- <i
        class="isShow closeico el-icon-circle-close"
        @click="cannelUpload(index)"
      ></i> -->
    </div>
    <!-- </template> -->
    <!-- :on-preview="handlePictureCardPreview" -->
    <template v-if="!onlyshow">
      <el-upload
        :file-list="fileList"
        action=""
        list-type="picture-card"
        :class="[disabled ? 'disabled' : '']"
        :on-remove="handleRemove"
        :http-request="handHttpUpload"
        :ref="'uploadRef' + idx"
        :on-change="handleChange"
        :disabled="disabled"
        :multiple="true"
        :auto-upload="false"
        :show-file-list="false"
      >
        <i class="el-icon-plus"></i>
      </el-upload>
    </template>

    <template v-else>
      <div class="el-upload--picture-card" @click="showClick()" :class="[disabled ? 'disabled' : '']">
        <i class="el-icon-plus"></i>
      </div>
    </template>

    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="srcList"
      :zIndex='zIndex'
    ></el-image-viewer>
  </div>
</template>


<script>
import { uploadFileAxios } from "@/api/index.js";

import LoadingExit from "@/components/LoadingExit/index.vue";
// 导入组件
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { nextTick } from "vue";

import { domainURL } from "@/utils/index.js";
import { PopupManager } from 'element-ui/src/utils/popup';

export default {
  name: "uploadItem",
  components: {
    ElImageViewer,
  },
  props: {
    // 禁用状态
    disabled:{
      type:Boolean,
      default:false
    },
    // 文件大小上传限制
    filesize: {
      type: Number,
      required: false,
      default: 10, // 10M
    },
    maxcount: {
      type: Number,
      default: 100,
    },
    // 上传附带其他参数
    params: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 已存在文件
    picarr: {
      type: Array,
      default: () => {
        return [];
      },
    },

    groupId: {
      type: Number,
      default: 4001,
    },
    relId: {
      type: String,
      default: function () {
        return "";
      },
    },

    // 头像模式--- 只能选一张
    isavator: {
      type: Boolean,
    },
    ischecked: {
      type: Boolean,
      default: false,
    },

    // 仅展示
    onlyshow: {
      type: Boolean,
      default: false,
    },
    // 接受文件后缀
    accept: {
      type: Array,
      default: function () {
        return [
          'png',
          'jpg',
          'mp3',
          'mp4',
          'gif',
          'BMP',
          'DIB',
          'PCP',
          'DIF',
          'WMF',
          'GIF',
          'JPG',
          'TIF',
          'EPS',
          'PSD',
          'CDR',
          'IFF',
          'TGA',
          'PCD',
          'MPT',
          'PNG'
        ]
        // return ['.png','.jpg','.mp3','.mp4','.BMP','.DIB','.PCP','.DIF','.WMF','.GIF','.JPG','.TIF','.EPS','.PSD','.CDR','.IFF','.TGA','.PCD','.MPT','.PNG']
      }
    },
  },
  data() {
    return {
      zIndex:2000,
      srcList: [],
      showViewer: false, // 显示查看器

      clickcount: false,
      //   list: [
      //     {
      //       url: "",
      //     },
      //   ],
      srcList: [],
      fileList: [],
      // 成功上传
      successCount: 0,
      seekCountList: 0,
      picture: [],
      // 要上传图片URL Index
      pictureIndex: {},
      idx: Math.random(),
      update: true,
    };
  },
  methods: {
    clearfiles() {
      this.fileList = [];
      this.$refs["uploadRef" + this.idx].clearFiles();
      // let result = this.picarr;
      // this.$emit("query", result);
      this.$nextTick(() => {
        this.picture = [];
      });
    },
    showDialog() {
      if (this.onlyshow) {
        this.$emit("showclick");
      }
    },
    domainURL,
    // 配合弹框uploa
    showClick() {
      if(this.disabled){
        return
      }
      this.$emit("showclick");
    },
    // 更新文件上传对应索引
    updateUpdateIndex() {
      for (let i = 0; i < this.picture.length; i++) {
        this.pictureIndex[this.picture[i].uid] = i;
      }
    },
    // 使用批量删除，父级必须要接受移除传进来picarr 移除方法remove
    async moredelete() {
      if (!this.ischecked) {
        return;
      }

      for (let i = 0; i < this.picture.length; i++) {
        if (this.picture[i].checked && this.picture.length != 0) {
          this.picture.splice(i, 1);
          if (i == 0) {
            i = -1;
          } else {
            i = i - 1;
          }
        }
      }

      for (let i = 0; i < this.picarr.length; i++) {
        if (this.picarr[i].checked) {
          this.$emit("remove", i);
          if (i == 0 && this.picarr.length != 0) {
            i = -1;
          } else {
            i = i - 1;
          }
        }
      }

      console.log(this.picture);

      this.updateUpdateIndex();

      this.update = false;
      await nextTick();
      this.update = true;
      // this.$forceUpdate()
    },
    // toggle(index,key){
    //   this.$emit('change',{
    //     index,
    //     key
    //   })
    // },
    // 查看图片
    async showImage(path) {
      this.srcList = path;
      await this.$nextTick();
      const nextZIndex = PopupManager.nextZIndex();
      console.log('this.nextZIndex',nextZIndex)
      this.zIndex = this.zIndex > nextZIndex ? this.zIndex : nextZIndex;
      this.showViewer = true;
    },
    // 关闭查看器
    closeViewer() {
      this.showViewer = false;
    },
    async zoom(path) {
      this.srcList = [domainURL(path)];
      await this.$nextTick();
      const nextZIndex = PopupManager.nextZIndex();
      console.log('this.nextZIndex',nextZIndex)
      this.zIndex = this.zIndex > nextZIndex ? this.zIndex : nextZIndex;
      this.showViewer = true;
      // let ref = "image" + index;
      // console.log(this.$refs[ref][0]);
      // this.$refs[ref][0].$el.click();
    },
    cannelUpload(idx) {
      console.log("this.fileList", this.fileList);
      console.log("this.picture", this.picture);
      if (this.clickcount) {
        return;
      }
      this.clickcount = true;

      setTimeout(() => {
        this.clickcount = false;
      }, 500);

      if (!this.picture[idx].success) {
        this.seekCountList -= 1;
      }
      // console.log(idx)
      this.fileList.splice(idx, 1);
      this.picture.splice(idx, 1);

      this.updateUpdateIndex();
    },
    remove(index) {
      

      this.$emit("remove", index);
    },
    findImgIndex(name = '') {
      let valid = false
      const temp = name.split('.')
      const suffix = temp[temp.length - 1]
      if (Array.isArray(this.accept)) {
        const idx = this.accept.findIndex(item => item === suffix)
        if (idx === -1) {
          this.$message.error('只支持上传' + this.accept.join(',') + '后缀的文件');
          valid = true
        }
      }
      return valid
    },
    // 图片文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
    handleChange(file, fileList) {
      // 添加文件的时候进来
      // console.log('fileList',fileList)
      // console.log('file',file)
      // return;
      const isLt = file.size / 1024 / 1024 < this.filesize;

      const v1 = this.findImgIndex(file.name)
      if (v1) {
        fileList.pop()
        this.fileList = fileList
        return
      }

      if (
        !isLt ||
        fileList.length > this.maxcount ||
        fileList.length + this.picarr.length > this.maxcount
      ) {
        // errorInfo("文件大小超过限制" + this.filesize + "M");
        if (
          fileList.length > this.maxcount ||
          fileList.length + this.picarr.length > this.maxcount
        ) {
          this.$message.error("最多只能上传" + this.maxcount + "张");
        } else {
          this.$message.error("文件大小超过限制" + this.filesize + "M");
        }
        fileList.pop();
        this.fileList = fileList;

        return;
      } else {
        this.seekCountList += 1;
      }
      if (file.status === "ready") {
        // 头像模式
        if (this.isavator) {
          this.seekCountList = 1;

          if (this.picarr.length != 0) {
            this.$emit("clear", "picarr");
            // this.picarr = [];
          }
          this.picture = [
            {
              graph: URL.createObjectURL(file.raw),
              uid: file.uid,
              isSeek: !isLt,
              percent: 0,
              success: false,
            },
          ];
          this.pictureIndex[file.uid] = this.picture.length - 1;
          this.fileList = [file];
          // this.fileList = fileList
        } else {
          this.picture.push({
            graph: URL.createObjectURL(file.raw),
            uid: file.uid,
            isSeek: !isLt,
            percent: 0,
            success: false,
          });
          this.pictureIndex[file.uid] = this.picture.length - 1;
          this.fileList = fileList;
        }
      }

      console.log(this.picture);
      // console.log(that.picture);
    },
    // 文件上传
    handHttpUpload(val) {
      console.log("jjjjj");
      var formData = new FormData();
      console.log(val);

      formData.append("files", val.file);
      // groupId, relId
      let groupId = this.groupId;
      let relId = this.relId;
      formData.append("groupId", groupId);
      formData.append("relId", relId);
      for (var key in this.params) {
        formData.append(key, this.params[key]);
      }
      console.log("8888");
      this.picture[this.pictureIndex[val.file.uid]].start = true;
      // console.log(formData);
      console.log(uploadFileAxios);
      uploadFileAxios(formData, {}, (progressEvent) => {
        console.log(progressEvent);
        // 原生获取上传进度的事件
        if (progressEvent.lengthComputable) {
          // if (progressEvent.total) {
          //   // 属性lengthComputable主要表明总共需要完成的工作量和已经完成的工作是否可以被测量
          //   // 如果lengthComputable为false，就获取不到progressEvent.total和progressEvent.loaded

          var percent = (progressEvent.loaded / progressEvent.total) * 100;
          percent = percent.toFixed(2);
          this.picture[this.pictureIndex[val.file.uid]].percent = percent - 0;
          this.picture[this.pictureIndex[val.file.uid]].start = true;
          console.log(this.picture);
          console.log(this.picture[this.pictureIndex[val.file.uid]]);
          console.log("-------", percent);
          //
          if (percent == 100) {
            this.picture[this.pictureIndex[val.file.uid]].percent = 100;
            this.picture[this.pictureIndex[val.file.uid]].success = true;
          } else {
            this.picture[this.pictureIndex[val.file.uid]].percent = percent - 0;
            this.picture[this.pictureIndex[val.file.uid]].success = false;
          }
        } else {
          // var percent = (progressEvent.loaded / progressEvent.total) * 100;
          // this.picture[this.pictureIndex[val.file.uid]].percent = percent;
          // this.picture[this.pictureIndex[val.file.uid]].start = true;
        }
      })
        .then((ret) => {
          // if (ret.return_code == "SUCCESS") {
          // console.log(ret);
          this.successCount += 1;
          this.picture[this.pictureIndex[val.file.uid]].url = ret.data[0].dir;

          console.log("this.seekCountList", this.seekCountList);
          console.log("this.successCount", this.successCount);
          // 成功个数等于要上传列表条数
          if (this.successCount == this.seekCountList) {
            // this.$message.success("上传成功");
            this.fileList = [];

            this.$refs["uploadRef" + this.idx].clearFiles();

            let result = this.picarr.concat(this.picture);
            this.$emit("query", result);

            this.$nextTick(() => {
              this.picture = [];
            });
            // emit('success', {
            //   arr: that.picture
            // })
          }
          // } else {
          //   this.$eltool.errorMsg(ret.msg)
          //   // errorInfo(ret);
          // }
        })
        .catch((e) => {
          this.successCount += 1;
          if (this.successCount == this.seekCountList) {
            this.fileList = [];
            this.$refs["uploadRef" + this.idx].clearFiles();
            let result = this.picarr;
            this.$emit("query", result);
            this.$nextTick(() => {
              this.picture = [];
            });
          }
        });
    },
    handleRemove(uploadFile, uploadFiles) {
      // console.log(uploadFile, uploadFiles);
      for (var i = 0; i < this.picture.length; i++) {
        if (uploadFile.uid === this.picture[i].uid) {
          // that.picture.isSeek = false; // 是否可见
          this.picture.splice(i, 1);
          break;
        }
      }
      console.log(this.picture);
    },
    submit() {
      console.log("submit");
      // console.log(this.$refs.uploadRef.submit)
      if (this.fileList.length == 0) {
        let result = this.picarr;
        this.$emit("query", result);
      } else {
        this.$refs["uploadRef" + this.idx].submit();
      }
    },
    submitUpload() {
      // this.successCount = 0;
    },
  },
};
</script>



<style lang="scss" scope>
.el-upload--picture-card {
  text-align: center;
  // display: flex;
  // align-items: center;
  // justify-content: center;
}
.disabled.el-upload--picture-card{
  cursor: not-allowed;
}
.disabled .el-upload--picture-card{
  cursor: not-allowed !important;
}
.closeico {
  // position: absolute;
  // right: 3px;
  // top: 3px;
  // color: #000;
  // z-index: 9;
  font-size: 28px;
  cursor: pointer;
}
// 嵌套弹出层图片框
.nestPicture {
  display: flex;
  flex-wrap: wrap;

  .printup {
    position: relative;

    .mask {
      background: rgba(0, 0, 0, 0.3);
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: none;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 16px;
    }
    .checkbox {
      position: absolute;
      right: 5px;
      top: 5px;
    }

    .masktools {
      // cursor: pointer;
      // display: flex;
      display: flex;
      position: absolute;
      /* top: 50%; */
      color: #fff;
      /* left: 50%; */
      width: 100%;
      height: 30px;
      top: 50%;
      display: flex;
      justify-content: space-around;
      padding: 0 30px;
      box-sizing: border-box;
      transform: translateY(-50%);
    }
  }

  .printup:hover .mask {
    display: flex;
  }

  .print {
    overflow: hidden;
    background-color: var(--el-fill-color-blank);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    box-sizing: border-box;
    width: 148px;
    height: 148px;
    margin: 0 8px 8px 0;
    padding: 0;
    display: inline-flex;
    position: relative;
  }

  .beforeUpload::before {
    position: absolute;
    bottom: 0;
    left: 0;
    content: "待上传";
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0.7;
    z-index: 1;
    zoom: 1;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .print:hover::before {
    display: none;
  }
  .beforeUpload {
    .closeico {
      // color: #fff;
    }
  }

  .scheme {
    position: absolute;
    color: #a69f9f;
    top: -7px;
    right: 2px;
    z-index: 2;
    font-size: 19px;
  }

  .scheme:hover {
    color: #3a8ee6;
    cursor: pointer;
  }

  .img {
    width: 100%;
    height: 100%;
  }
}

.progress2 {
  width: 4.6rem;
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 999;
  transform: translate(-50%, -50%);
}
</style>
