/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
const prefix = '/dm/api/v1'

/**
 * 合规字段
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/todotaskscompliancefield/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/todotaskscompliancefield/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/todotaskscompliancefield/insert`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/todotaskscompliancefield/update`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/todotaskscompliancefield/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/todotaskscompliancefield/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/todotaskscompliancefield/query/page`, data);
}

// 根据场景码获取对应配置 
// 
export function todotaskscompliancefieldQueryOneScenario(data) {
  return requestV1.get(`${prefix}/todotaskscompliancefield/query/one/scenario`, data);
}