/**
 * 获取请求地址头部类型
 * @type {string}
 */
const header =
  window.location.href.indexOf("https") === -1 ? "http://" : "https://";
/**
 * 调试环境地址选择
 * @type {number}
 */
const useManberId = 16;
const devManber = [
  { id: 1, url: "192.168.3.10:7000", desc: "锋" },
  { id: 2, url: "panjinfa.vicp.io", desc: "潘" },
  { id: 3, url: "192.168.3.110:7000", desc: "测试服务器" },
  { id: 4, url: "192.168.3.52:7000", desc: "力军" },
  { id: 5, url: "192.168.3.10:7000", desc: "杰鑫" },
  { id: 6, url: "saas.ngrok.greenboniot.cn", desc: "测试" },
  { id: 7, url: "saas.greenboniot.cn", desc: "正式" },
  { id: 8, url: "127.0.0.1:7004", desc: "新服务调试" },
  { id: 9, url: "192.168.3.15:7000", desc: "潘内网" },
  { id: 10, url: "api.greenboniot.cn", desc: "生产" },
  { id: 11, url: "t.api.ngrok.greenboniot.cn", desc: "合并测试" },
  { id: 12, url: "h.api.ngrok.greenboniot.cn", desc: "力军" },
  { id: 13, url: "wxapi.greenboniot.cn", desc: "新生产" },
  { id: 14, url: "192.168.3.8:7000", desc: "育润" },
  { id: 15, url: "192.168.3.9:7000", desc: "林衢" },
  { id: 16, url: "192.168.3.53:7000", desc: "黄桂春" },
];

/**
 * 根据id获取请求地址
 * @returns {string}
 */
function getManber() {
  for (const i in devManber) {
    if (devManber[i].id === useManberId) {
      return header + devManber[i].url;
    }
  }
}

/**
 * 全局环境变量
 */
const environmentConfiguration = {
  development: {
    ctx: getManber(),
    ws_ctx: "",
    file_ctx: header + "test-file.greenboniot.cn/",
    log_ctx: header + "log.greenboniot.cn/",
    domain_ctx: window.location.origin,
    isDebug: true,
    version: "V0.0.1",
    code_ctx:
      header + "gift.ngrok.greenboniot.cn/packet/js/ads-m.js?businessInfoId",
    isOpenPermission: false,
  },
  staging: {
    // ctx: header + '192.168.3.110:7000',
    ctx: header + "t.api.ngrok.greenboniot.cn",
    ws_ctx: "",
    file_ctx: header + "test-file.greenboniot.cn/",
    log_ctx: header + "log.greenboniot.cn/",
    domain_ctx: header + "t.manage.ngrok.greenboniot.cn",
    isDebug: true,
    version: "V0.0.1",
    code_ctx:
      header + "gift.ngrok.greenboniot.cn/packet/js/ads-m.js?businessInfoId",
    isOpenPermission: true,
  },
  production: {
    ctx: header + "api.greenboniot.cn",
    ws_ctx: "",
    file_ctx: header + "file.greenboniot.cn/",
    log_ctx: header + "log.greenboniot.cn/",
    domain_ctx: header + "saas.greenboniot.cn",
    isDebug: false,
    version: "V0.0.1",
    code_ctx:
      header + "file.greenboniot.cn/h5/static/js/ads-m.min.js?businessInfoId",
    isOpenPermission: true,
  },
};
const NODE_ENV = process.env.NODE_ENV;
export default Object.freeze(environmentConfiguration[NODE_ENV]);
