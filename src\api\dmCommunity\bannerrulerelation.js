/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 马甲管理-banner曝光量马甲规则
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/bannerrulerelation/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/bannerrulerelation/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/bannerrulerelation/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/bannerrulerelation/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/bannerrulerelation/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/bannerrulerelation/update`, data)
}

// 修改启动状态
export function switchOpenstatus (data) {
    return requestV1.postForm(`${prefix}/bannerrulerelation/update/open/status`, data)
}
