<!-- 电话 -->
<template>
  <el-form-item
    :label="config.label"
    :label-width="config.width"
    :rules="rules"
    :class="itemClass"
    :prop="config.name">
    <el-input
      :disabled="disabled"
      v-model="form.data.input"
      :class="childClass"
      :placeholder="config.placeholder"
      type="text"
    />
  </el-form-item>
</template>

<script>
var checkPhone = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('电话号不能为空'))
  }
  var obj = isMobile(value)
  if (!obj.flag) {
    callback(new Error(obj.msg))
    return
  } else {
    callback()
  }
}
/**
 * 判断电话号码是否正确
 * flag true 验证通过 false 验证失败
 */
function isMobile(phone) {
  const obj = {
    flag: true,
    msg: ''
  }
  if (!isNull(phone)) {
    if (phone.length === 11) {
      const isPhone = /^\d{3,4}-\d{7,8}$/
      if (!isPhone.test(phone)) {
        obj.msg = '电话号码格式不正确'
        obj.flag = false
      }
    } else {
      obj.msg = '电话号码长度为10位'
      obj.flag = false
    }
  } else {
    obj.msg = '电话号码不能为空'
    obj.flag = false
  }
  return obj
}

/**
 * 判断是否为空
 */
function isNull(val) {
  if (val instanceof Array) {
    if (val.length === 0) return true
  } else if (val instanceof Object) {
    if (JSON.stringify(val) === '{}') return true
  } else {
    if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '') return true
    return false
  }
  return false
}

export default {
  name: 'Phone',
  props: {
    // el-form-item的class值
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    // 是否可编辑
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    // el-input的class值
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    // 组件循环时需要的idx值
    idx: {
      type: Number,
      default: 0
    },
    // 参数配置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '文本输入',
          name: 'text',
          placeholder: '请填写文本输入',
          rules: [
            { required: true, message: '请输入文本输入', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      rules: [],
      form: {
        data: {
          input: ''
        }
      }
    }
  },
  watch: {
    config(val) {
      this.rules = val.rules
    },
    data: {
      handler(val) {
        this.form.data.input = val
      },
      deep: true
    },
    // 监听到form的数据变化则把config.name和form.data.input和idx传到父组件
    form: {
      handler(val) {
        let num = this.form.data.input
        if (num) {
          const numLen = num.length
          if (numLen >= 3 && num.indexOf('-') === -1) {
            const str1 = num.substr(0, 3)
            const str2 = '-' + num.substr(3, numLen - 1)
            num = str1 + str2
          } else if (numLen <= 3 && num.indexOf('-') > -1) {
            // debugger
            num = num.replace('-', '')
          }
          this.$emit('updateForm', '' + this.config.name, num, this.idx)
        }
      },
      deep: true
    }
  },
  created() {
    this.rules = this.config.rules
    if (this.rules.length > 0 && this.rules[0].required) {
      const rulesObj = { validator: checkPhone, trigger: 'blur' }
      this.rules.push(rulesObj)
    }
  },
  methods: {
    checkPhone() {
      console.log('checkPhone: ', this.form.data.input)
    }
  }
}
</script>
