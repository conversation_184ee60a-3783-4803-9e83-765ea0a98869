<template>
  <div :id="uuid" class="report-content-box" v-loading="pageLoading">
    <!-- 封面 -->
    <div
      class="settlement-page"
      :style="{
        height: pageSize.height + 'px',
        width: pageSize.width + 'px',
      }"
    >
      <div class="theme-img">
        <img :src="bgImg" class="" alt="" />
      </div>
      <div class="analysis-report-box">
        <div class="title">关于小葫芦平台</div>
        <div class="activity">在全国区域的地推活动</div>
        <div class="project-report">
          项&nbsp;目&nbsp;服&nbsp;务&nbsp;报&nbsp;告
        </div>
        <div class="execution-time">
          执行时间：{{ fixedFieldObject.startTime }} 至
          {{ fixedFieldObject.endTime }}
        </div>
        <div class="server">服务方：{{ fixedFieldObject.serviceProvider }}</div>
        <div class="project-leader">
          项目方：{{ fixedFieldObject.projectParty }}
        </div>
      </div>
    </div>
    <!-- 目录 -->
    <div
      class="settlement-page"
      :style="{
        height: pageSize.height + 'px',
        width: pageSize.width + 'px',
      }"
    >
      <div class="theme-img">
        <img :src="themeImg" class="" alt="" />
      </div>
    </div>
    <div class="pageContent">
      <template v-for="(page, index) in pageContent">
        <div
          :key="index"
          v-if="page.authHeight"
          :style="{
            width: pageSize.width + 'px',
            height: page.targetHeight ? page.targetHeight + 'px' : 'auto',
          }"
          :id="authHeightResult[index]"
        >
          <div class="everyPageContent">
            <!-- 基础信息、北京介绍、验收结果 -->
            <div class="settlement-page" v-if="page.type === 'allContent'">
              <div class="user-content-box">
                <!-- 信息 -->
                <div class="project-info">
                  <div class="title color1">
                    <img :src="productImgUrl" class="title-icon" alt="" />
                    项目基础信息
                  </div>
                  <div class="title-table">
                    <div class="name">
                      项目名称：<span class="general">{{
                        fixedFieldObject.projectName
                      }}</span>
                    </div>
                    <div class="execution">
                      项目执行周期：<span class="general"
                        >{{ fixedFieldObject.startTime }} 至
                        {{ fixedFieldObject.endTime }}</span
                      >
                    </div>
                    <!-- <div class="name">
                      支付渠道：<span class="general">{{
                        fixedFieldObject.paymentChannels
                      }}</span>
                    </div> -->
                    <div class="project-bott">
                      <div class="project-bott-l">
                        项目方：
                        <span class="general">{{
                          fixedFieldObject.projectParty
                        }}</span>
                      </div>
                      <div class="project-bott-r">
                        <span class="white-space">服务商：</span>
                        <span class="general">{{
                          fixedFieldObject.serviceProvider
                        }}</span>
                      </div>
                    </div>
                    
                  </div>
                </div>
                <!-- 介绍 -->
                <div class="project-introduce">
                  <div class="title color1">
                    <img :src="productBgUrl" class="title-icon" alt="" />
                    项目背景介绍
                  </div>
                  <div class="info">
                    <div class="info-t">
                      小葫芦平台，是{{
                        fixedFieldObject.projectParty
                      }}，潜心打造的医患社交平台。旨在打造一个医药大健康信息平台，为用户提供真实可靠的病友互助沟通渠道。该平台日常发布权威的健康科普资讯，为用户提供专业的健康咨询服务。同时，患者也可以在平台上发布健康求助贴子，与其他病友分享治疗经验，共同探索健康的捷径。
                    </div>
                    <br />
                    <div class="info-b">
                      为了进一步提升{{
                        fixedFieldObject.projectParty
                      }}旗下小葫芦平台的用户数量、活跃度和互动率，我们组织了地推团队，在全国范围的医院、药店、商超等人流密集的场景中开展了地推活动。活动采用了包括但不限于赠送小礼品、赠送服务等方式，引导用户扫码进入小葫芦平台，进行文章的阅读、评论、点赞等互动操作，并鼓励用户继续使用平台。
                    </div>
                  </div>
                </div>
                <!-- 结果 -->
                <div class="project-result" style="margin-top: 500px">
                  <div class="title color1">
                    <img :src="productResultUrl" class="title-icon" alt="" />
                    项目验收结果
                  </div>
                  <div class="info">
                    项目“关于小葫芦平台在全国区域的地推活动”由{{
                      fixedFieldObject.serviceProvider
                    }}
                    于 {{ fixedFieldObject.startTime }} 至
                    {{ fixedFieldObject.endTime }} 承接执行。{{
                      fixedFieldObject.projectParty
                    }}于 当月最后一天验收项目，本项目由
                    {{ fixedFieldObject.productTaskNumber }}
                    个地推团队执行，共完成访问量（pv）{{
                      userVisitCountVosInfo.pvCount
                    }}，访客数（uv）{{
                      userVisitCountVosInfo.uvCount
                    }}，验收结果如下。
                  </div>
                  <div class="title-table">
                    <div class="serve">
                      <div class="serve-l">项目方</div>
                      <div class="serve-r">
                        {{ fixedFieldObject.projectParty }}
                      </div>
                    </div>
                    <div class="serve">
                      <div class="serve-l">服务方</div>
                      <div class="serve-r">
                        {{ fixedFieldObject.serviceProvider }}
                      </div>
                    </div>
                    <div class="executions">
                      <div class="executions-l">项目执行时间</div>
                      <div class="executions-r">
                        {{ fixedFieldObject.startTime }} 至
                        {{ fixedFieldObject.endTime }}
                      </div>
                    </div>
                    <div class="team">
                      <div class="team-l">地推团队数量（人次）</div>
                      <div class="team-r">
                        {{ fixedFieldObject.productTaskNumber }}
                      </div>
                    </div>
                    <div class="team">
                      <div class="team-l">完成平台访问量（PV）</div>
                      <div class="team-r">
                        {{ userVisitCountVosInfo.pvCount }}
                      </div>
                    </div>
                    <div class="team">
                      <div class="team-l">完成平台访客量（UV）</div>
                      <div class="team-r">
                        {{ userVisitCountVosInfo.uvCount }}
                      </div>
                    </div>
                    <!-- <div class="complete">
                      <div class="complete-l">完成新用户注册数量</div>
                      <div class="complete-r">
                        {{ activityResultObject.allWriteOptionNum }}
                      </div>
                    </div> -->
                  </div>
                  <div style="margin-top: 50px">
                    <div class="title color1 mgb40">
                      <img
                        :src="productResultAnalysisUrl"
                        class="title-icon"
                        alt=""
                      />
                      推广明细
                    </div>
                    <el-table
                      :data="userVisitCountVos"
                      border
                      style="width: 100%"
                    >
                      <el-table-column
                        prop="userName"
                        label="执行团队代表人姓名"
                        width="180"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="userPhone"
                        label="手机号"
                        width="180"
                      >
                      </el-table-column>
                      <el-table-column prop="pvCount" label="完成平台访问量">
                      </el-table-column>
                      <el-table-column prop="uvCount" label="完成平台访客量">
                      </el-table-column>
                    </el-table>
                    <div class="spaceItem" :style="{
                        height:page.userVisitCountVosspaceHeight + 'px',
                      }"></div>
                  </div>
                  <div class="data-form">
                    <!-- <div class="c-table-t">数据附表</div> -->
                    <div class="title color1">
                      <img
                        :src="productResultAnalysisUrl"
                        class="title-icon"
                        alt=""
                      />
                      数据附表
                      <template v-if="dataAppendixResult.length >= 500">
                        （仅展示前{{ 500 }}条）
                      </template>
                      <template v-else>
                        （共{{ dataAppendixResult.length }}条）
                      </template>
                    </div>
                    <div class="btw pd30">
                      <el-table
                        :data="dataAppendixResult.slice(0, 500)"
                        border
                        style="width: 100%; margin-top: 20px"
                      >
                        <el-table-column
                          prop="visitTime"
                          label="访问时间"
                          width="180"
                        >
                        </el-table-column>
                        <el-table-column
                          prop="visitTypeText"
                          label="访问类型"
                          width="100"
                        >
                        </el-table-column>
                        <!-- <el-table-column
                          prop="terminalTypeText"
                          label="终端类型"
                          width="100"
                        >
                        </el-table-column> -->
                        <el-table-column
                          prop="writeIp"
                          label="访问Ip"
                          width="150"
                        >
                        </el-table-column>
                        <el-table-column
                          prop="nickNameText"
                          label="用户昵称"
                          width="100"
                        >
                        </el-table-column>
                        <el-table-column prop="accountId" label="唯一用户ID">
                        </el-table-column>
                      </el-table>
                       <div class="spaceItem" :style="{
                        height:page.dataAppendixResultspaceHeight + 'px',
                      }"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="settlement-page" v-if="page.type === 'topicContent'">
              <div class="reportContent-box">
                <div class="chartItem">
                  <div
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                    "
                  >
                    <div class="reportContent-l">
                      <div class="panel barTarget">
                        <div
                          class="barChart"
                          :id="uuid + '_' + page.pageContent.uuid"
                        ></div>
                        <div
                          class="buttomCharts"
                          v-if="page.pageContent.showBottom"
                        ></div>
                      </div>
                    </div>
                    <div class="gender-content">
                      <el-table
                        :data="page.pageContent.tableData"
                        style="
                          width: 100%;
                          border-left: none;
                          border-right: none;
                        "
                        border
                      >
                        <template v-for="item in page.pageContent.hearders">
                          <el-table-column
                            :key="item.key"
                            :prop="item.key"
                            :label="item.title"
                            :width="item.width"
                          >
                            <template slot-scope="scope">
                              <div
                                class="exporttxt"
                                :style="scope.row[item.key + 'Style'] || ''"
                              >
                                {{ scope.row[item.key] }}
                              </div>
                            </template>
                          </el-table-column>
                        </template>
                      </el-table>
                      <!-- <div class="reportContent-tip">
                        实际提交：{{ page.pageContent.totalTargetNumber || 0 }}
                        份问卷
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="settlement-page"
              v-if="page.type === 'reportContentBooks'"
            >
              <div class="user-execute-box">
                <div class="title color1">
                  <img :src="executiveSummaryUrl" class="title-icon" alt="" />
                  执行总结
                </div>
                <div class="btw" style="margin-top: 30px">
                  <div class="basic-info">
                    <div class="basic-text1">
                      本次项目共组织了{{
                        fixedFieldObject.productTaskNumber
                      }}个地推团队，深入医院、药店、商超等人流密集的场景，开展了一系列地推活动来推广小葫芦平台。活动采用了包括但不限于赠送小礼品、赠送服务等方式，引导用户扫码进入小葫芦平台，进行文章的阅读、评论、点赞等互动操作，并鼓励用户继续使用平台。
                    </div>
                  </div>
                  <br />
                  <div class="basic-info">
                    <div class="basic-text1">
                      在本次活动中，共完成访问量（pv）{{
                        userVisitCountVosInfo.pvCount
                      }}，访客数（uv）{{
                        userVisitCountVosInfo.uvCount
                      }}。这不仅增加了小葫芦平台的用户数量，还提升了平台整体的活跃度和互动率。通过地推活动的执行，小葫芦平台在全国范围内的知名度和用户数量得到了显著提升，有效地将平台推广给潜在用户，进一步提高了平台在该领域的影响力，为小葫芦平台的发展奠定了坚实的基础。通过增加用户数量和提高活跃度，平台能够更好地满足用户需求，进一步加强与用户的互动和沟通，这将为平台的未来发展和推广提供有力支持。
                    </div>
                  </div>
                  <br />
                  
                  <div class="basic-info">
                    <div class="basic-text1">
                      小葫芦平台致力于打造一个患者交流圈，为患者提供一个交流互动的平台。在平台上，患者可以通过发帖、评论等方式与其他患者进行交流，分享自己的经验和感受。小葫芦平台设有健康科普专区，为用户提供丰富的健康知识和科普信息。在该专区，用户可以浏览各类健康科普文章、提高自身的健康意识和健康素养，以预防疾病并更好地管理自己的健康。
                    </div>
                  </div>
                  <br />
                  <div class="basic-info">
                    <div class="basic-text1">
                      小葫芦平台还提供在线咨询服务，用户可以通过平台与医生进行实时的在线咨询。用户可以通过文字、图片、语音等方式向医生描述自己的病情，医生会根据用户信息提供相关建议。这种在线咨询服务方便快捷，为用户提供了便利的医疗服务，尤其对于一些日常症状和健康问题，用户可以及时获得专业的医生建议。
                    </div>
                  </div>
                  <br />
                  <div class="basic-info">
                    <div class="basic-text1">
                      总之，小葫芦平台通过患者交流圈、健康科普专区和在线咨询服务等功能，为用户提供了一个全面的健康管理平台。通过地推活动的推广，小葫芦平台在全国范围内得到了更多用户的关注和参与，未来，小葫芦平台将继续致力于为用户提供更好的健康服务和互动体验，助力用户更好地管理自己的健康。
                    </div>
                  </div>
                  <div class="imgBox">
                    <img :src="bottomIconUrl" class="bottomIconUrl" alt="" />
                  </div>
                </div>
              </div>
            </div>
            <!-- 活动场景展示 -->
            <div
              class="settlement-page"
              v-if="page.type === 'eventSceneDisplay'"
            >
              <div class="page-h2 color1">
                <img :src="onSiteSectionUrl" class="title-icon" alt="" />
                活动场景展示
              </div>
              <div class="bgw">
                <div class="activity-image-box">
                  <template
                    v-for="item in eventSceneDisplayImage.slice(
                      page.start,
                      page.end
                    )"
                  >
                    <div :key="item.url" class="activity-flex1">
                      <div class="activity-image-b">
                        <img :src="item.url" class="activity-image" alt="" />
                      </div>
                    </div>
                  </template>
                  <template v-if="eventSceneDisplayImage.length === 0">
                    <div class="nodate">暂无数据</div>
                  </template>
                </div>
              </div>
            </div>
            <!-- 执行团队展示-->
            <div
              class="settlement-page"
              v-if="page.type === 'executiveTeamPresentation'"
            >
              <div class="page-h2 color1" v-if="!page.subtitleShow">
                <img :src="onSiteSectionUrl" class="title-icon" alt="" />
                地推现场部分照片展示
              </div>
              <div class="bgw">
                <div class="activity-image-box">
                  <template
                    v-for="item in signInLogListImage.slice(
                      page.start,
                      page.end
                    )"
                  >
                    <div :key="item.url" class="activity-flex1">
                      <div class="activity-image-b">
                        <img :src="item.url" class="activity-image" alt="" />
                      </div>
                    </div>
                  </template>
                  <template v-if="signInLogListImage.length === 0">
                    <div class="nodate">暂无数据</div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { getVisitingplan as queryOne } from "@/api/dm/visiting/visitingplan";

import { queryList } from "@/api/dm/visiting/visitingplanobjectlist.js";
import { getIOSTime, format } from "@/utils/index";
import {
  getdrugstoreFeedbackList,
  getDmCommunityChannelUrlVisitType,
  getSigninlogType,
} from "@/utils/enumeration.js";
import {
  initColumnar,
  initColumnarTwoChart,
  initColumnarFourChart,
} from "../columnar.js";

const color = ["#005aff", "#f8b551"];
import { seximg } from "../images";
import { loadScript, getPosition } from "@/utils/index";
import {
  todotasksGetReportOrdinary as exportProjectAccurateId,
  exportProjectCommonList,
} from "@/api/dmDemand";
import { getQueryStr, domainURL } from "@/utils/index";

export default {
  props: {
    taskId: {
      type: [Number, String],
      default: null,
    },
    preview: {
      type: Boolean,
      default: true,
    },
    // 拼接域名 lvbao不支持本地调式图片
    domainUrl: {
      type: String,
      default: "https://lvbao-saas.oss-cn-shenzhen.aliyuncs.com/",
    },
    domainResult: {
      type: Array,
      default: function () {
        return ["https://file.greenboniot.cn/"];
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    filename: {
      type: String,
      default: "",
    },
    uuid: {
      type: String,
      default: "export-visiting-analysis-activity-report",
    },
    taskMonth: {
      type: [Number, String],
      default: "",
    },
    additional:{
      type:String,
      default:""
    }
  },
  data() {
    return {
      bottomStr: "",
      bottomIconUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/9.png",
      productImgUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/1.png",
      productBgUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/2.png",
      productResultUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/3.png",
      productResultAnalysisUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/4.png",
      onSiteSectionUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/5.png",
      recruitNewUserInformationUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/6.png",
      laNewUserUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/7.png",
      executiveSummaryUrl:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/8.png",

      bgImg:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/bg.jpg",
      domainUrl2: "https://file.greenboniot.cn/",
      saTime: 600,

      collectionType: null,
      pageSize: {
        height: 1123,
        width: 794,
      },
      themeImg:
        process.env.VUE_APP_PREFIX_URL +
        "/templateFile/report-image/precise-project-promotion/dirBg.jpg",
      // themeImg:
      //   process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/0.png",
      research: {
        // title: "关于高血压患者家属需求在全国地区的调研项目",
        // monthText: "2023-11",
      },
      topResult: [
        {
          label: "问卷名称",
          id: "title",
          value: "",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/1.png",
        },
        {
          label: "项目方",
          value: "江西施美药业股份有限公司",
          id: "projectName",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/2.png",
        },
        {
          label: "服务商",
          value: "广州兰图科技有限公司",
          id: "serviceName",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/3.png",
        },
        {
          label: "任务月度",
          value: "",
          id: "monthText",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/4.png",
        },
        {
          label: "问卷数量",
          id: "taskNumAllCount",
          value: "0",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/5.png",
        },
        {
          label: "项目执行人数",
          id: "taskUserNum",
          value: "0",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/6.png",
        },
        {
          label: "报告生成时间",
          id: "createTimeText",
          value: "",
          hidden: false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/7.png",
        },
      ],
      taskNumAllCount: 0,
      createTimeText: "",
      monthText: "",

      chartCount: 0,
      chartCountLength: 0,
      projectExecutorInformation: {
        age_0_17: "0.00%",
        age_18_30: "0.00%",
        age_31_40: "0.00%",
        age_41_50: "0.00%",
        age_51_70: "0.00%",
        age_71_100: "0.00%",
        sex_1: "0.00%", // 男性
        sex_2: "0.00%", //女性
        sex_0: "0.00%", // 未知
      },
      seximg,
      opageContent: [
        // 问卷活动调研
        {
          text: "所有内容",
          type: "allContent",
          authHeight: true,
          pageContent: {
            contentHtml: "",
          },
          dataAppendixResultspaceHeight:20,
          userVisitCountVosspaceHeight:20,
        },
      ],
      // 导出数据的标题
      pageContent: [],
      reportEnd: {
        text: "执行总结",
        type: "reportContentBooks",
        authHeight: true,
        pageContent: {},
      },
      // 题目
      topicObject: {
        text: "题目内容",
        type: "topicContent",
        authHeight: true,
        pageContent: {
          uuid: "my",
          hearders: [],
          tableData: [],
          isOnly: false,
        },
      },

      pageLoading: false,
      authHeightResult: [],
      initsuccesscount: 0,
      targetCount: 1,
      // 导出类型
      exportVisitType: 3,

      // 年龄段数据
      ageResult: [
        // {
        //   value: 0,
        //   name: "0-17周岁",
        //   oName: "0-17周岁",
        //   itemStyle: {
        //     normal: { color: "#6a9955" },
        //   },
        //   uuid: "age_0_17",
        // },
        {
          value: 0,
          name: "18-30周岁",
          oName: "18-30周岁",
          itemStyle: {
            normal: { color: "#2721ff" },
          },
          uuid: "age_18_30",
        },
        {
          value: 0,
          name: "31-40周岁",
          oName: "31-40周岁",
          itemStyle: {
            normal: { color: "#6763fe" },
          },
          uuid: "age_31_40",
        },
        {
          value: 0,
          name: "41-50周岁",
          oName: "41-50周岁",
          itemStyle: {
            normal: { color: "#cfcdfe" },
          },
          uuid: "age_41_50",
        },
        {
          value: 0,
          name: "51-70周岁",
          oName: "51-70周岁",
          itemStyle: {
            normal: { color: "#5dc0f9" },
          },
          uuid: "age_51_70",
        },
        // {
        //   value: 0,
        //   name: "70周岁以上",
        //   oName: "70周岁以上",
        //   itemStyle: {
        //     normal: { color: "#eab81a" },
        //   },
        //   uuid: "age_71_100",
        // },
      ],
      ageResultCount: 0,
      sexResultCount: 0,
      sexResult: [
        // itemStyle.normal.color
        {
          name: "女性",
          cName: "女性",
          value: 0,
          uuid: 2,
          itemStyle: {
            normal: {
              color: "#e2b62d",
            },
          },
        },
        {
          name: "男性",
          cName: "男性",
          value: 0,
          uuid: 1,
          itemStyle: {
            normal: {
              color: "#264894",
            },
          },
        },
        {
          name: "未知",
          cName: "未知",
          value: 0,
          uuid: 0,
          itemStyle: {
            normal: {
              color: "#ff002d",
            },
          },
        },
      ],
      auditResult: [
        {
          value: 0,
          name: "待处理",
          cName: "待处理",
          uuid: 1,
          itemStyle: {
            color: "#2822ff",
            // normal: { color: "#2822ff" },
          },
        },
        {
          value: 0,
          name: "处理中",
          cName: "处理中",
          uuid: 2,
          itemStyle: {
            color: "#fdc71c",
            // normal: { color: "#fdc71c" },
          },
        },
        {
          value: 0,
          name: "已处理",
          cName: "已处理",
          uuid: 3,
          itemStyle: {
            color: "#42d885",
            // normal: { color: "#2822ff" },
          },
        },
        // {
        //   value: 0,
        //   name: "已关闭",
        //   cName: "已关闭",
        //   uuid: 5,
        //   itemStyle: {
        //     color: "#f56c6c",
        //     // normal: { color: "#fdc71c" },
        //   },
        // },
        // {
        //   value: 0,
        //   name: "已撤销",
        //   cName: "已撤销",
        //   uuid: 6,
        //   itemStyle: {
        //     color: "#ffba00",
        //     // normal: { color: "#2822ff" },
        //   },
        // },
      ],
      auditObject: {
        audit_1_text: "待审核",
        audit_2_text: "审核通过",
        audit_3_text: "审核未通过",

        audit_1: "0.00%",
        audit_2: "0.00%",
        audit_3: "0.00%",
        // audit_5: "0.00%",
        // audit_6: "0.00%",
        audit_count_1: 0,
        audit_count_2: 0,
        audit_count_3: 0,
      },
      auditResultTotal: 0,

      tableData: [
        {
          title: "药店",
          title2: 2,
          title3: 3,
        },
        {
          title: "任务数（单）",
          title2: 2,
          title3: 3,
        },
        {
          title: "百分比（%）",
          title2: 2,
          title3: 3,
        },
      ],
      hearders: [
        {
          key: "title",
          prop: "title",
          title: "001",
        },
        {
          key: "title2",
          prop: "title2",
          title: "001",
        },
        {
          key: "title3",
          prop: "title3",
          title: "001",
        },
      ],
      // 执行人信息
      userReportVo: {},

      // 任务信息
      // taskReportVo: {},

      // 女性占比
      womanText: "0.00%",
      // 男性占比
      manText: "0.00%",

      // 项目信息
      demandInfo: {},

      visitingPlan: {},
      taskAllocationReportVoList: [],

      productAnalysisInfo: {
        num_0_18: "0.00",
        num_18_36: "0.00",
        num_36_54: "0.00",
        num_54_72: "0.00",
        num_72_90: "0.00",
        num_72_: "0.00",

        num_0_18_count: 0,
        num_18_36_count: 0,
        num_36_54_count: 0,
        num_54_72_count: 0,
        num_72_90_count: 0,
        num_72__count: 0,
      },

      // 题目数据
      answerReportVoList: [],
      // 地推活动结果分析
      activityResultList: [],
      // 用户基本信息
      basicInfo: {},
      // 用户健康信息
      healthInfo: {},
      // 地推结果分析
      activityResultObject: {
        isSex: false, // 性别控件
        isAge: false, // 年龄
        isDisease: false, // 疾病
        isProvince: false, // 省市
        isDepartment: false, // 科室
        areaStr: "", // 地区值字符串
        allWriteOptionNum: 0, // 新注册人数
        diseaseStr: "", // 疾病字符串
      },
      // 活动场景展示
      eventSceneDisplayImage: [],
      // 签到图片
      signInLogListImage: [],
      // 执行人团队
      userVisitCountVos: [],
      userVisitCountVosInfo: {
        pvCount: 0,
        uvCount: 0,
      },
      // 数据附表
      dataAppendixResult: [],
      mainId: [],
      // 固定字段
      fixedFieldObject: {
        projectName: "关于小葫芦平台在全国区域的地推活动", // 项目名称
        serviceProvider: "", // 服务方
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己
        startTime: "", // 项目执行开始时间
        endTime: "", // 项目执行结束时间
        productTaskNumber: 0, // 地推团队数量
        paymentChannels:"",// 支付渠道
      },
    };
  },
  mounted() {},
  watch: {
    updatecount(n) {
      this.pageLoading = true;
      this.initsuccesscount = 0;
      this.pageContent = [...this.opageContent];
      this.answerReportVoList = [];
      this.activityResultList = [];
      this.chartCountLength = 0;
      this.chartCount = 0;

      const saToken = getQueryStr("satoken");
      if (saToken) {
        this.saTime = 300;
      } else {
        // 客户端预览
        this.saTime = 2000;
      }
      this.fixedFieldObject.paymentChannels = this.additional

      this.initEchart();
      // this.exportProjectAccurateId()
    },
  },
  methods: {
    getDaysInCurrentMonth() {
      let str = this.taskMonth;
      let arr = str.split("-");
      let year = arr[0];
      let month = arr[1];
      // 设置日期为下个月的第0天，这样它就会回滚到当前月的最后一天
      var nextMonthFirstDay = new Date(year, month, 1);
      var daysInMonth = new Date(nextMonthFirstDay - 1).getDate();
      return daysInMonth;
    },
    // 初始化执行时间
    initTime() {
      let daysInMonth = this.getDaysInCurrentMonth();
      let startTime = this.taskMonth + "-" + "01";
      let endTime = this.taskMonth + "-" + daysInMonth;
      this.fixedFieldObject.startTime = startTime;
      this.fixedFieldObject.endTime = endTime;
    },
    addEnd() {
      this.initActivityPage();
      // 初始化现场图片
      this.sceentInit();
      // this.sceentInitEventSceneDisplay()
      this.pageContent.push({
        text: "执行总结",
        type: "reportContentBooks",
        authHeight: true,
        pageContent: {},
      });
    },
    async getExportProjectCommonList() {
      if (this.mainId.length === 0) {
        return;
      }
      // const res = await exportProjectCommonList({
      //   condition: {
      //     businessType: 1,
      //     taskIdList: this.mainId,
      //   },
      //   current: 1,
      //   size: 20,
      // });
      const data = this.mainId;

      // const terminalTypeList = [
      //   { value: 1, label: "小程序" },
      //   { value: 2, label: "H5" },
      // ];
      const getDmCommunityChannelUrlVisitTypeArr = getDmCommunityChannelUrlVisitType();
      this.dataAppendixResult = data.map((item) => {
        item.visitTypeText = this.getEnumText(
          item.visitType,
          getDmCommunityChannelUrlVisitTypeArr
        );
        item.visitTime = format(item.visitTime, "YYYY-MM-DD HH:mm:ss");

        item.nickNameText =
          item.nickName && item.nickName != "" ? item.nickName : item.vestName;
        item.writeIp = item.ip
        return item;
      });
      let typeidx = this.pageContent.findIndex(item => item.type === 'allContent');
      this.pageContent[typeidx].dataAppendixResultspaceHeight = parseInt((this.dataAppendixResult.length / 23)) * 20;
    },
    // 赋值对应
    updateActivityItem(type, answerOptionVoList) {
      if (type === 15) {
        // 性别
        this.activityResultObject.isSex = true;
      } else if (type === 16) {
        // 年龄
        this.activityResultObject.isAge = true;
      } else if (type === 17) {
        // 科室标签
        this.activityResultObject.isDepartment = true;
      } else if (type === 18) {
        // 省市
        this.activityResultObject.isProvince = true;
        let areaStr = "";
        for (let i = 0; i < answerOptionVoList.length; i++) {
          if (areaStr === "") {
            areaStr +=
              answerOptionVoList[i].optionValue +
              "地区用户占比" +
              answerOptionVoList[i].selectOptionProportion +
              "%";
          } else {
            areaStr +=
              "，" +
              answerOptionVoList[i].optionValue +
              "地区用户占比" +
              answerOptionVoList[i].selectOptionProportion +
              "%";
          }
        }
        this.activityResultObject.areaStr = areaStr;
      } else if ([19,23].includes(type)) {
        // 疾病
        this.activityResultObject.isDisease = true;
        let diseaseStr = "";
        for (let i = 0; i < answerOptionVoList.length; i++) {
          if (diseaseStr === "") {
            diseaseStr +=
              answerOptionVoList[i].optionValue +
              "用户占比" +
              answerOptionVoList[i].selectOptionProportion +
              "%";
          } else {
            diseaseStr +=
              "，" +
              answerOptionVoList[i].optionValue +
              "用户占比" +
              answerOptionVoList[i].selectOptionProportion +
              "%";
          }
        }
        this.activityResultObject.diseaseStr = diseaseStr;
      } else if (type === 20) {
        // 姓名
        this.activityResultObject.isName = true;
      }
    },
    // 初始化活动场景
    initActivityPage() {
      // this.pageContent.push({
      //   text: "活动场景展示",
      //   type: "eventSceneDisplay",
      //   authHeight: true,
      //   end:9,
      //   start:0,
      //   pageContent: {
      //     // eventSceneDisplayImage: this.eventSceneDisplayImage,
      //   },
      // });
      this.pageContent.push({
        text: "执行团队展示",
        type: "executiveTeamPresentation",
        authHeight: true,
        end: 9,
        start: 0,
        pageContent: {
          // signInLogListImage: this.signInLogListImage,
        },
      });
    },
    sceentInitEventSceneDisplay() {
      console.log("signInLogListImage", this.eventSceneDisplayImage);
      if (this.eventSceneDisplayImage.length > 9) {
        let count = this.eventSceneDisplayImage.length - 9;
        let idx = this.pageContent.findIndex(
          (item) => item.type === "eventSceneDisplay"
        );
        let tidx = 0;
        while (count > 0) {
          count -= 9;
          tidx += 1;
          idx += 1;
          this.pageContent.splice(idx, 0, {
            text: "活动场景展示",
            type: "eventSceneDisplay",
            authHeight: true,
            end: 9 * (tidx + 1),
            start: 9 * tidx,
            pageContent: {
              signInLogListImage: [],
            },
          });
        }
      }
    },
    sceentInit() {
      console.log("signInLogListImage", this.signInLogListImage);
      if (this.signInLogListImage.length > 9) {
        let count = this.signInLogListImage.length - 9;
        let idx = this.pageContent.findIndex(
          (item) => item.type === "executiveTeamPresentation"
        );
        let tidx = 0;
        while (count > 0) {
          count -= 9;
          tidx += 1;
          idx += 1;
          this.pageContent.splice(idx, 0, {
            text: "地推现场照片展示",
            type: "executiveTeamPresentation",
            authHeight: true,
            end: 9 * (tidx + 1),
            start: 9 * tidx,
            pageContent: {
              signInLogListImage: [],
            },
          });
        }
      }
    },
    addComputeTag() {
      // class=“pdf_finish”
      const dom = document.createElement("div");
      dom.classList = "pdf_finish";
      document.body.append(dom);
    },
    // 生成模拟 3D 饼图的配置项
    getPie3DTwo(pieData, internalDiameterRatio) {
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let legendData = [];
      let k =
        typeof internalDiameterRatio !== "undefined"
          ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
          : 1 / 3;

      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;

        let seriesItem = {
          name:
            typeof pieData[i].name === "undefined"
              ? `series${i}`
              : pieData[i].name,
          type: "surface",
          parametric: true,
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k,
          },
        };

        if (typeof pieData[i].itemStyle != "undefined") {
          let itemStyle = {};

          typeof pieData[i].itemStyle.color != "undefined"
            ? (itemStyle.color = pieData[i].itemStyle.color)
            : null;
          typeof pieData[i].itemStyle.opacity != "undefined"
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null;

          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;

        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = this.getParametricEquationTwo(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          true,
          false,
          1
        );

        startValue = endValue;

        legendData.push(series[i].name);
      }
      console.log("legendData", legendData);

      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      let option = {
        //animation: false,
        legend: {
          show: true,
          data: legendData,
          type: "plain",
          // orient: "vertical",
          left: 0,
          top: 265,
          // bottom: 6,
          textStyle: {
            // color: "#CAEFFF",
            color: "#dbdbdb",
          },
          itemGap: 10,
        },

        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1.3,
          max: 1.3,
        },

        grid3D: {
          show: false,
          height: 350,
          width: 350,
          boxHeight: 20, // 饼图厚度
          // left: "5%",
          // top: "0%",
          top: "-20%",
          left: "-3%",
          viewControl: {
            // 3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 25,
            distance: 300, //调整视角到主体的距离，类似调整zoom
            rotateSensitivity: 0,
            zoomSensitivity: 0,
            panSensitivity: 0,
            autoRotate: false, // 控制是否自动旋转
            //   autoRotateSpeed: 5,
            //   autoRotateAfterStill: 10
          },
        },
        series: series,
      };
      return option;
    },
    // 同步渲染
    syncStatisisItem(fn) {
      let timer = this.saTime;
      return new Promise((resolve, reject) => {
        fn();
        setTimeout(() => {
          resolve(true);
        }, timer);
      });
    },
    initEchart() {
      // this.$nextTick(() => {
      console.log("initEchart");

      return new Promise((resolve, reject) => {
        loadScript(`${this.domainUrl2}cdnjs/echarts.js`)
          .then(() => {
            loadScript(`${this.domainUrl2}cdnjs/echarts-gl.js`)
              .then(() => {
                console.log("window", window);

                this.exportProjectAccurateId().then(async (res) => {
                  if (this.collectionType != 4) {
                    let itx = this.topResult.findIndex(
                      (item) => item.id === "taskUserNum"
                    );
                    this.topResult[itx].hidden = false;
                  } else {
                    let itx = this.topResult.findIndex(
                      (item) => item.id === "taskUserNum"
                    );
                    this.topResult[itx].hidden = true;
                  }
                  await this.getExportProjectCommonList();
                  this.addEnd();

                  this.$nextTick(() => {
                    setTimeout(() => {
                      this.updateSuccess();
                      resolve(true);
                    }, 600);
                  });
                  // this.initProblemData();
                });
              })
              .catch((err) => {
                // console.log(err);
              });
          })
          .catch((err) => {
            console.log(err);
          });
      });

      // });
    },
    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquationTwo(startRatio, endRatio, isSelected, isHovered, k) {
      // 计算
      let midRatio = (startRatio + endRatio) / 2;

      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;

      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }

      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== "undefined" ? k : 1 / 3;

      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1;

      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },

        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },

        x: function (u, v) {
          if (u < startRadian) {
            return (
              offsetX +
              Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        y: function (u, v) {
          if (u < startRadian) {
            return (
              offsetY +
              Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u);
          }
          return Math.sin(v) > 0 ? 1 : -1;
        },
      };
    },
    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    // 初始化题目分析报告
    initProblemData() {
      // 后端返回数据
      // const problemData = this.answerReportVoList;
      const problemData = this.activityResultList;
      let str = "mm";
      let allWriteOptionNum = 0;
      for (let i = 0; i < problemData.length; i++) {
        this.updateActivityItem(
          problemData[i].formType,
          problemData[i].answerOptionVoList
        );
        if (allWriteOptionNum < problemData[i].allWriteOptionNum) {
          allWriteOptionNum = problemData[i].allWriteOptionNum;
        }

        problemData[i].uuid = str + "_" + i;
        this.topicObject.pageContent.title =
          problemData[i].formTemplateTitle +
          "(" +
          (problemData[i].formType === 1 ? "单选" : "多选") +
          ")";

        this.topicObject.pageContent.uuid = problemData[i].uuid;
        this.topicObject.pageContent.showBottom = i % 2 !== 1 && i % 3 !== 1;

        this.topicObject.pageContent.hearders = [
          {
            key: "optionValue",
            prop: "optionValue",
            title: "选项",
            width: 180,
          },
          {
            key: "selectOptionNum",
            prop: "selectOptionNum",
            title: "答案人数（人）",
            width: 180,
          },
          {
            key: "selectOptionProportionText",
            prop: "selectOptionProportionText",
            title: "百分比（%）",
            // width:180,
          },
        ];
        let totalTargetNumber = 0;
        // 是否是年龄
        let isage = false;
        for (let k = 0; k < problemData[i].answerOptionVoList.length; k++) {
          let val = problemData[i].answerOptionVoList[k].optionValue;
          if (val.indexOf("age_") !== -1) {
            val = val.split("age_")[1] + "岁";
            isage = true;
            problemData[i].answerOptionVoList[k].sortVal = val.split("_")[0];
          }
          problemData[i].answerOptionVoList[k].optionValue = val;
          problemData[i].answerOptionVoList[k].selectOptionProportionText = (
            problemData[i].answerOptionVoList[k].selectOptionProportion - 0
          ).toFixed(2);

          // totalTargetNumber +=
        }
        if (isage) {
          problemData[i].answerOptionVoList.sort(
            (a, b) => a.sortVal - b.sortVal
          );
        }

        this.topicObject.pageContent.tableData =
          problemData[i].answerOptionVoList;
        this.topicObject.pageContent.isOnly = i === 0;

        // totalTargetNumber
        this.topicObject.pageContent.totalTargetNumber =
          problemData[i].allWriteOptionNum - 0;

        this.topicObject.pageContent.index = i + 1;

        this.pageContent.push(JSON.parse(JSON.stringify(this.topicObject)));
      }
      this.taskNumAllCount = allWriteOptionNum;

      let idx3 = this.topResult.findIndex(
        (item) => item.id === "taskNumAllCount"
      );
      this.topResult[idx3].value = allWriteOptionNum;

      // console.log("this.pageContent", this.pageContent);
      // 初始化
      this.initActivityPage();

      this.pageContent.push(this.reportEnd);

      this.$nextTick(async () => {
        for (let i = 0; i < problemData.length; i++) {
          let item = problemData[i];

          let seriesData = [];
          let textData = [];

          for (let k = 0; k < item.answerOptionVoList.length; k++) {
            let val = item.answerOptionVoList[k].optionValue;
            textData.push(val);
            seriesData.push({
              value: item.answerOptionVoList[k].selectOptionNum,
              // name: this.getTargetText(item.answerOptionVoList[k].optionValue),
              name: val,
            });
          }

          item.seriesData = seriesData;
          item.textData = textData;
          await this.initProbleDataChart(item, i);
        }
      });
    },
    getTargetText(text) {
      let str = text.substr(0, 6);
      let temp = text.substr(6);
      while (temp.length != 0) {
        let t = temp.substr(0, 6);
        temp = temp.substr(6);
        str += "\n" + t;
      }
      return str;
    },
    initProbleDataChart(item, idx) {
      console.log(idx, "idx=========");
      return new Promise((resolve, reject) => {
        let that = this;
        const str = this.uuid;
        let strUUID = "#" + this.uuid + "_" + item.uuid;
        console.log("strUUID", strUUID);
        const dom = document.getElementById(str);
        //1 实例化对象
        let myCharts4 = window.echarts.init(dom.querySelector(strUUID));
        const colorResult = [
          "rgba(127, 181, 255,0.6)",
          "rgba(166, 228, 239,0.6)",
          "rgba(188, 161, 254,0.6)",
          "rgba(103, 96, 254,0.6)",

          "rgba(232, 177, 0,0.6)",
          "rgba(2, 170, 254,0.6)",
          "rgba(129, 78, 204,0.6)",
          "rgba(203, 130, 81,0.6)",
          "rgba(163, 200, 78,0.6)",
          "rgba(23, 179, 103,0.6)",
        ];
        // let tidx = parseInt(Math.random(10));
        let tidx = idx;
        for (let i = 0; i < item.seriesData.length; i++) {
          let color = "";
          if (!colorResult[tidx]) {
            tidx = 0;
          }
          color = colorResult[tidx];
          tidx += 1;
          item.seriesData[i].itemStyle = {
            color: color,
            opacity: 0.6,
          };
        }
        let timer = this.saTime;
        //----------------------------------------------

        if (idx % 4 === 1) {
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          initColumnarFourChart(myCharts4, optionData, yAxisData);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess(true);
          }, 1000);
        } else if (idx % 2 === 1) {
          // this.initChart(item.seriesData, myCharts4);
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          initColumnar(myCharts4, optionData, yAxisData);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess(true);
          }, timer);
        } else if (idx % 3 === 1) {
          // initFixColumnarChart
          // this.initChart(item.seriesData, myCharts4);
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          initColumnarTwoChart(myCharts4, optionData, yAxisData);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess(true);
          }, timer);
        } else {
          // 传入数据生成 option
          let option = this.getPie3DTwo(item.seriesData, 0.59);
          // 绘制图表
          myCharts4.setOption(option);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess(true);
          }, timer);
        }
      });
    },
    // 获取分析报告
    async exportProjectAccurateId() {
      let additional = this.additional.split('-Im-');
      let useWorkConfigId = additional[0];
      let taskTypes = additional[1] ? additional[1].split('-').join(',') : null;
      const res = await exportProjectAccurateId({
        taskMonth: this.taskMonth,
        useWorkConfigId,
        taskTypes
      },{
        "no-time-manage": 1
      });

      const data = res.data;
      // 执行人信息
      if (data.userReportVo instanceof Object) {
        this.userReportVo = data.userReportVo || {};

        // 性别信息
        if (this.userReportVo.genderReportVoList) {
          let genderReportVoList = this.userReportVo.genderReportVoList;

          for (let i = 0; i < genderReportVoList.length; i++) {
            for (let j = 0; j < this.sexResult.length; j++) {
              this.sexResultCount = genderReportVoList[i].allNum;
              if (this.sexResult[j].uuid === genderReportVoList[i].gender) {
                let percent =
                  genderReportVoList[i].num !== 0
                    ? (
                        (genderReportVoList[i].num /
                          genderReportVoList[i].allNum) *
                        100
                      ).toFixed(2)
                    : "0.00";
                if (this.sexResult[j].uuid === 2) {
                  this.womanText = percent + "%";
                } else if (this.sexResult[j].uuid === 1) {
                  this.manText = percent + "%";
                }
                this.sexResult[j].value = genderReportVoList[i].num;
                this.sexResult[j].name =
                  this.sexResult[j].cName + percent + "%";
                this.sexResult[j].num = this.sexResult[j].value;
                let str = "sex_" + genderReportVoList[i].gender;
                this.projectExecutorInformation[str] = percent + "%";
                break;
              }
            }
          }
        }
      }

      // 任务信息

      if (data.authStatusGroupVoList instanceof Object) {
        let taskStatusReportVoList = data.authStatusGroupVoList;

        if (taskStatusReportVoList) {
          console.log("taskStatusReportVoList", taskStatusReportVoList);
          let auditResultTotal = 0;
          for (let i = 0; i < taskStatusReportVoList.length; i++) {
            for (let j = 0; j < this.auditResult.length; j++) {
              if (
                this.auditResult[j].uuid ===
                taskStatusReportVoList[i].authStatus
              ) {
                this.auditResult[j].value =
                  taskStatusReportVoList[i].num &&
                  taskStatusReportVoList[i].num !== ""
                    ? taskStatusReportVoList[i].num
                    : 0;
                auditResultTotal += this.auditResult[j].value;

                let percent = taskStatusReportVoList[i].proportion;
                this.auditResult[j].name =
                  this.auditResult[j].cName + percent + "%";
                // this.auditResultTotal = taskStatusReportVoList[i].allNum;

                let str = "audit_" + this.auditResult[j].uuid;
                this.auditObject[str] = percent + "%";
                let str2 = "audit_count_" + this.auditResult[j].uuid;
                this.auditObject[str2] = taskStatusReportVoList[i].num;
                let str3 = "audit_" + this.auditResult[j].uuid + "_text";
                this.auditObject[str3] = taskStatusReportVoList[i].authDesc;

                break;
              }
            }
          }
          this.auditResultTotal = auditResultTotal;
        }
      }
      if (data.taskReportVo instanceof Object) {
        if (data.taskReportVo.taskAllocationReportVoList instanceof Object) {
          this.taskAllocationReportVoList =
            data.taskReportVo.taskAllocationReportVoList;
        }
      } else {
      }

      if (data.demand instanceof Object) {
        if (data.todoTasksList instanceof Object) {
          data.demand.taskNumber = data.todoTasksList.length;
        } else {
          data.demand.taskNumber = 0;
        }
        if (data.taskUserVoList instanceof Object) {
          data.demand.productTaskNumber = data.taskUserVoList.length;
        } else {
          data.demand.productTaskNumber = 0;
        }
        data.demand.createTimeText = getIOSTime(data.demand.createTime);
        let date = new Date(data.demand.createTime);
        // data.demand.monthText = date.getMonth() + 1;
        data.demand.monthText =
          date.getFullYear() + "-" + (date.getMonth() + 1);
        data.demand.yearText = date.getFullYear();
        data.demand.startTime = format(data.demand.startTime, "YYYY-MM-DD");
        data.demand.endTime = format(data.demand.endTime, "YYYY-MM-DD");

        let idx = this.topResult.findIndex((item) => item.id === "taskUserNum");
        this.topResult[idx].value = data.demand.taskUserNum;

        this.demandInfo = data.demand;
      }

      if (data.visitingPlan instanceof Object) {
        this.visitingPlan = data.visitingPlan;
      }

      console.log("data.answerReportVoList", data.answerReportVoList);

      if (data.answerReportVoList instanceof Object) {
        this.answerReportVoList = [];
        this.chartCountLength = 0;
        // const answerReportVoList = data.answerReportVoList;
        // this.answerReportVoList = answerReportVoList;
        // this.chartCountLength = data.answerReportVoList.length;
        this.chartCount = 0;
      }

      if (data.answerPreciseReportVoList instanceof Object) {
        let logCount = 0;
        data.answerPreciseReportVoList = data.answerPreciseReportVoList.filter((item) => {
          if([19,23].includes(item.formType)) {
            logCount += 1;
            if(item.answerOptionVoList.length === 0 && logCount !== 1) {
              return false;
            }
          }
          return true;
        })
        const answerPreciseReportVoList = data.answerPreciseReportVoList;
        this.activityResultList = answerPreciseReportVoList;
        let allWriteOptionNum = 0;
        for (let i = 0; i < answerPreciseReportVoList.length; i++) {
          allWriteOptionNum = answerPreciseReportVoList[i].allWriteOptionNum;
          // healthInfo
          // basicInfo
          if (answerPreciseReportVoList[i].formType == 16) {
            for (
              let j = 0;
              j < answerPreciseReportVoList[i].answerOptionVoList.length;
              j++
            ) {
              let optionValue =
                answerPreciseReportVoList[i].answerOptionVoList[j].optionValue;
              if (optionValue == "age_18_30") {
                this.basicInfo.age_18_30 =
                  answerPreciseReportVoList[i].answerOptionVoList[
                    j
                  ].selectOptionProportion;
              } else if (optionValue == "age_31_40") {
                this.basicInfo.age_31_40 =
                  answerPreciseReportVoList[i].answerOptionVoList[
                    j
                  ].selectOptionProportion;
              } else if (optionValue == "age_41_50") {
                this.basicInfo.age_41_50 =
                  answerPreciseReportVoList[i].answerOptionVoList[
                    j
                  ].selectOptionProportion;
              } else if (optionValue == "age_51_70") {
                this.basicInfo.age_51_70 =
                  answerPreciseReportVoList[i].answerOptionVoList[
                    j
                  ].selectOptionProportion;
              } else if (optionValue == "age_71_100") {
                this.basicInfo.age_71_100 =
                  answerPreciseReportVoList[i].answerOptionVoList[
                    j
                  ].selectOptionProportion;
              }
            }
          }
          if (answerPreciseReportVoList[i].formType == 15) {
            for (
              let j = 0;
              j < answerPreciseReportVoList[i].answerOptionVoList.length;
              j++
            ) {
              let optionValue =
                answerPreciseReportVoList[i].answerOptionVoList[j].optionValue;
              if (optionValue == "男") {
                this.basicInfo.man =
                  answerPreciseReportVoList[i].answerOptionVoList[
                    j
                  ].selectOptionProportion;
              } else if (optionValue == "女") {
                this.basicInfo.girl =
                  answerPreciseReportVoList[i].answerOptionVoList[
                    j
                  ].selectOptionProportion;
              }
            }
          }
        }
        this.activityResultObject.allWriteOptionNum = allWriteOptionNum;
        this.chartCountLength = data.answerPreciseReportVoList.length;
        this.chartCount = 0;
      }

      if (data.research instanceof Object) {
        this.research.title = data.research.title;
        let idx = this.topResult.findIndex((item) => item.id === "title");
        this.topResult[idx].value = data.research.title;

        let allContentIdx = this.pageContent.findIndex(
          (item) => item.type === "allContent"
        );

        this.pageContent[allContentIdx].pageContent.backgroundContentHtml =
          data.research.background || "";
        this.pageContent[allContentIdx].pageContent.methodSampleContentHtml =
          data.research.methodSample || "";
        this.pageContent[allContentIdx].pageContent.resultAnalysisContentHtml =
          data.research.resultAnalysis || "";
        this.pageContent[
          allContentIdx
        ].pageContent.resultConclusionContentHtml =
          data.research.resultConclusion || "";

        let idx3 = this.topResult.findIndex((item) => item.id === "monthText");
        this.topResult[idx3].value = data.research.taskMonthly;
        this.monthText = data.research.taskMonthly;

        let idx4 = this.topResult.findIndex(
          (item) => item.id === "createTimeText"
        );
        this.topResult[idx4].value = data.research.reportTimeStr;
        this.collectionType = data.research.collectionType;
        let hidden = this.collectionType == 4;
        let iidx = this.pageContent.findIndex(
          (item) => item.type === "projectExecutorInformation"
        );
        if (iidx !== -1) {
          this.pageContent[iidx].hidden = hidden;
        }
      }

      if (data.taskUserVoList instanceof Object) {
        // this.activityResultObject.allWriteOptionNum =
        //   data.taskUserVoList.length;
      }
      // 任务主体
      if (data.todoTasksList instanceof Object) {
        let todoTasksList = data.todoTasksList;
        // let sceneImages = [];
        let ids = [];
        for (let i = 0; i < todoTasksList.length; i++) {
          ids.push(todoTasksList[i].id);
        }
        // this.eventSceneDisplayImage = sceneImages;
        this.fixedFieldObject.productTaskNumber = data.todoTasksList.length;
      }

      if (data.operatingList instanceof Object) {
        let todoTasksList = data.operatingList;
        let sceneImages = [];
        oneRoot: for (let i = 0; i < todoTasksList.length; i++) {
          let imageIds = todoTasksList[i].imageIds;
          if (imageIds !== "") {
            let arr = imageIds.split(",");
            for (let k = 0; k < arr.length; k++) {
              if (sceneImages.length < 27 && arr[k] !== "") {
                sceneImages.push({
                  url: domainURL(arr[k]),
                });
              } else {
                break oneRoot;
              }
            }
          }
        }
        this.eventSceneDisplayImage = sceneImages;
      }



      if (data.operatingList instanceof Object) {
        // this.mainId = data.signInLogList;

        let imagePathArr = [];
        oneRoot: for (let i = 0; i < data.operatingList.length; i++) {
          let imagePath = data.operatingList[i].imageIds;
          if (imagePath !== "" && imagePath !== "[]") {
            let arr = imagePath.split(",");
            for (let k = 0; k < arr.length; k++) {
              if (imagePathArr.length === 27) {
                break oneRoot;
              }
              if (arr[k] === "") {
                continue;
              }
              imagePathArr.push({
                url: domainURL(arr[k]) + '?x-oss-process=image/auto-orient,1',
                
              });
            }
          }
        }
        this.signInLogListImage = imagePathArr;
      }

      if(data.imagePathList instanceof Object && this.signInLogListImage.length < 27) {
        let imagePathListArr = data.imagePathList;
        for(let i=0;i<imagePathListArr.length;i++) {
          let bstr = imagePathListArr[i];
          if(bstr !== '' && bstr !== '[]') {
            let tarr = bstr.split(',');
            for(let k=0;k<tarr.length;k++) {
              if(this.signInLogListImage.length < 27 && tarr[k] !== '') {
                this.signInLogListImage.push({
                  url:domainURL(tarr[k]) + '?x-oss-process=image/auto-orient,1'
                })
              }
            }
          }
        }
      }
      if (data.miniChannelLinkLogList instanceof Object) {
        this.mainId = data.miniChannelLinkLogList
      }
      if (data.userVisitCountVos instanceof Object) {
        let userVisitCountVos = data.userVisitCountVos;
        this.userVisitCountVos = userVisitCountVos;
        let pvCount = 0;
        let uvCount = 0;
        for (let i = 0; i < userVisitCountVos.length; i++) {
          pvCount += userVisitCountVos[i].pvCount;
          uvCount += userVisitCountVos[i].uvCount;
        }
        this.userVisitCountVosInfo.pvCount = pvCount;
        this.userVisitCountVosInfo.uvCount = uvCount;
        let meIdx = this.pageContent.findIndex(item => item.type === 'allContent');
        this.pageContent[meIdx].userVisitCountVosspaceHeight = parseInt((this.userVisitCountVos / 32)) * 20
      }
      // 服务方
      this.fixedFieldObject.serviceProvider = data.tenantName;
      // 初始执行时间
      this.initTime();
    },
    // 项目执行人任务数量分析
    initProductAnalysis() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);

      // taskAllocationReportVoList
      let cData = [];
      var obj = {
        num_0_18: 1,
        num_18_36: 2,
        num_36_54: 3,
        num_54_72: 4,
        num_72_90: 5,
        num_72_: 6,
      };
      for (let key in obj) {
        cData.push(0);
      }
      for (let i = 0; i < this.taskAllocationReportVoList.length; i++) {
        let idxx = obj[this.taskAllocationReportVoList[i].descValue];
        if (idxx) {
          cData[idxx - 1] = this.taskAllocationReportVoList[i].num;

          let percent = (
            (this.taskAllocationReportVoList[i].num /
              this.taskAllocationReportVoList[i].allNum) *
            100
          ).toFixed(2);
          this.productAnalysisInfo[
            this.taskAllocationReportVoList[i].descValue
          ] = percent + "%";
          // this.taskAllocationReportVoList[i]
          let str2 = this.taskAllocationReportVoList[i].descValue + "_count";
          this.productAnalysisInfo[str2] =
            this.taskAllocationReportVoList[i].num;
          // this.productAnalysisInfo[this.taskAllocationReportVoList[i].descValue] = this.taskAllocationReportVoList[i].num;
        }
      }
      console.log("cData", cData, this.taskAllocationReportVoList);

      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".barTarget .chart")
      );

      let option4 = {
        grid3D: {},
        xAxis3D: {
          type: "value",
        },
        yAxis3D: {
          type: "category",
          data: ["0-18", "18-36", "36-54", "54-72", "72-90", "90单以上"],
          axisLabel: {
            formatter: "{value}",
          },
        },
        zAxis3D: {},
        // xAxis: {
        //   type: "value",
        // },
        // yAxis: {
        //   type: "category",
        //   data: ["0-18", "18-36", "36-54", "54-72", "72-90", "90单以上"],
        //   axisLabel: {
        //     formatter: "{value}",
        //   },
        //   // data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        // },
        series: [
          {
            data: cData,
            // type: "bar",
            type: "bar3D",
          },
        ],
      };

      //3 把配置给实例对象
      initColumnar(myCharts4, cData);
      // myCharts4.setOption(option4);
      //4 让图标自适应
      window.addEventListener("resize", function () {
        myCharts4.resize();
      });
    },
    // 获取拜访详情
    async queryOne() {
      const res = await queryOne({ id: this.taskId, isCurrentUser: 2 });

      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (idx !== -1) {
        console.log("kkk");
        this.pageContent[idx].pageContent.articleContent = res.data.content;
        this.exportVisitType = res.data.type - 0;

        //  item.value = [res.data.startTime, res.data.endTime];
        this.pageContent[idx].pageContent.planTime =
          res.data.startTime + " 至 " + res.data.endTime;

        this.$forceUpdate();
      }
      let timer = this.timer;

      this.$nextTick(() => {
        setTimeout(() => {
          this.updateSuccess();
        }, timer);
      });

      // this.updateSuccess();

      console.log("res", res);
    },
    // 获取拜访列表
    async getVisitCordList(type) {
      const res = await queryList({
        type: type,
        planId: this.taskId,
      });

      const data = res.data;
      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (type === 1) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.defaultTableData = data;
        }
      } else if (type === 2) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.characterTableData = data;
        }
      } else if (type === 3) {
        if (idx !== -1) {
          const drugstoreFeedbackArr = getdrugstoreFeedbackList();

          this.pageContent[idx].pageContent.pharmacyTableData = data.map(
            (item) => {
              item.writeTimeText = getIOSTime(item.writeTime);
              item.drugstoreFeedbackText = this.getEnumText(
                item.drugstoreFeedback,
                drugstoreFeedbackArr
              );
              return item;
            }
          );
        }
      }

      this.$nextTick(() => {
        this.updateSuccess();
      });

      // return data;
    },
    async loadChart() {},
    async updateSuccess(is) {
      if (!is) {
        this.initsuccesscount += 1;
      }

      console.log(
        "this.initsuccesscount",
        this.initsuccesscount,
        this.targetCount
      );

      if (
        this.initsuccesscount + this.chartCount ===
        this.targetCount + this.chartCountLength
      ) {
        await this.loadChart();
        this.pageLoading = false;
        let timer = this.timer;

        this.$nextTick(() => {
          this.initpage();
          setTimeout(() => {
            this.$emit("compute", {});
            this.addComputeTag();
          }, timer);
        });
      }
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    initpageData() {
      // 获取详情
      this.queryOne().then((res) => {
        if (this.exportVisitType === 1) {
          // 获取拜访记录- 默认
          this.getVisitCordList(1);
        } else if (this.exportVisitType === 2) {
          // 获取拜访记录- 人物
          this.getVisitCordList(2);
        } else if (this.exportVisitType === 3) {
          // 获取拜访记录- 药店
          this.getVisitCordList(3);
        }
      });
    },
    initpage() {
      this.authHeightResult = {};
      for (let i = 0; i < this.pageContent.length; i++) {
        if (this.pageContent[i].authHeight) {
          let id = "authHeight" + i;
          this.authHeightResult[i] = id;
        }
      }

      this.$nextTick(() => {
        this.save();
      });
    },
    // 页面的倍数
    initHeight(height) {
      let pagecount = 1;
      while (height > this.pageSize.height) {
        pagecount += 1;
        height -= this.pageSize.height;
      }

      return pagecount * this.pageSize.height;
    },
    // 保存前初始化页面
    save() {
      // for (let key in this.authHeightResult) {
      //   let id = this.authHeightResult[key];
      //   let dom = document.getElementById(id);
      //   const count = dom.clientHeight / this.pageSize.height;
      //   const height = count * 20;
      //   this.pageContent[key].spaceHeight = height;
      // }
      for (let key in this.authHeightResult) {
        let id = this.authHeightResult[key];

        let dom = document.getElementById(id);
        console.log(dom.clientHeight, this.pageContent[key]);
        this.pageContent[key].targetHeight = this.initHeight(dom.clientHeight);
        // this.pageContent[key].targetHeight = dom.clientHeight;
      }
      this.$forceUpdate();
    },
  },
};
</script>




<style lang="scss" scoped>
$ColorMain: #4787ff;
.spaceItem{
  width: 100%;
  height: 20px;
}
.mgb40{
  margin-bottom: 40px;
}
.userVisitCountVos-t{
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.data-form {
  margin-top: 40px;
}
.pd30 {
  // padding-bottom: 100px !important
}
.activity-flex1 {
  // flex: 1;
  min-width: 33.333%;
  display: flex;
  margin-bottom: 2px;
  // justify-content: center;
}
.theme-img {
  // width: 100%;
  // height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  img {
    width: 100%;
    height: 100%;
  }
}
.nodate {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 12px;
  color: #909399;
}
.ue-table {
  margin-top: 30px;
}
.c-table-t {
  font-size: 16px;
  line-height: 2;
  font-weight: 550;
  margin-top: 50px;
}
.general {
  font-weight: normal !important;
  line-height: 1.5;
}

.settlement-page {
  padding: 20px;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
  .page-h2 {
    padding-bottom: 15px;
    font-weight: bold;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .mh100 {
    min-height: 100%;
  }
  .color1 {
    color: #1f6ee1;
  }
  .page-h3 {
    padding-bottom: 15px;
    font-weight: bold;
    font-size: 20px;
  }
  .title-icon {
    width: 55px;
    height: 55px;
    margin-right: 5px;
  }
  .btw {
    padding: 20px;
    background: #fff;
    width: 100%;
    box-sizing: border-box;
  }
  .bgw {
    width: 100%;
    background: #fff;
  }
  .imgBox {
    display: flex;
    justify-content: center;
  }
}
// 活动场景展示
.activity-title {
  display: flex;
  justify-content: center;
  line-height: 2;
  margin-bottom: 20px;
  font-weight: 550;
  font-size: 18px;
}
.activity-image-box {
  display: flex;
  // width: 570px;
  flex-wrap: wrap;
  margin: 10px auto 0;
  background: #fff;
  box-sizing: border-box;
  padding: 10px 15px;
  padding-left: 60px;
  // padding: 20px;
  .activity-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  .activity-image-b {
    width: 180px;
    height: 320px;
    // margin-right: 2px;
  }
}
.ps50 {
  margin-top: 50px;
  margin-bottom: 50px;
}
.bgMain {
  // background: #edf3ff;
  background: url("https://lvbao-saas.oss-cn-shenzhen.aliyuncs.com/projectList/common.png")
    no-repeat;
  background-size: cover;
}
.bgMain2 {
  // background: #edf3ff;
  background: url("https://lvbao-saas.oss-cn-shenzhen.aliyuncs.com/projectList/common2.jpg")
    no-repeat;
  background-size: 100%;
}
.project-basics-content {
  width: 100%;
  .project-page {
    width: 100%;
    height: 100%;
    // padding:140px 40px;
    .project-info,
    .project-introduce,
    .project-result {
      .title {
        font-weight: bold;
        font-size: 22px;
      }
      .info {
        font-size: 16px;
        text-indent: 2em;
        line-height: 60px;
        padding-top: 20px;
      }
      .title-table {
        margin-top: 20px;
        border: 1px solid #dbdbdb;
        font-size: 18px;
        font-weight: bold;
        .name,
        .execution,
        .project-bott {
          // span{
          //   font-weight: bold;
          // }
          .project-bott-l,
          .project-bott-r {
            // span{
            //   font-weight: bold;
            // }
          }
          .project-bott-r {
            // span{

            // }
          }
        }
        .name,
        .execution {
          padding: 20px 10px;
          border-bottom: 1px solid #dbdbdb;
        }
        .execution {
        }
        .project-bott {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .project-bott-l {
            display: flex;
            flex: 1;
            border-right: 1px solid #dbdbdb;
            padding: 20px 10px;
            align-items: center;
          }
          .project-bott-r {
            display: flex;
            flex: 1;
            padding: 20px 10px;
          }
        }
      }
    }
    .project-introduce {
      margin-top: 110px;
      .title {
      }
      .info {
      }
    }
    .project-result {
      margin-top: 50px;
      .title {
      }
      .info {
      }
      .title-table {
        border: 1px solid #dbdbdb;

        .item {
          display: flex;
          border-bottom: 1px solid #dbdbdb;
          .item-l {
            border-right: 1px solid #dbdbdb;
          }
          .item-l,
          .item-r {
            display: flex;
            flex: 2;
            align-items: center;
            justify-content: center;
            padding: 20px 10px;
          }
          .item-r {
            flex: 3;
            font-weight: normal;
          }
        }
        .serve {
          display: flex;
          border-bottom: 1px solid #dbdbdb;
          .serve-l {
            border-right: 1px solid #dbdbdb;
          }
          .serve-l,
          .serve-r {
            display: flex;
            flex: 2;
            align-items: center;
            justify-content: center;
            padding: 20px 10px;
          }
          .serve-r {
            flex: 3;
            font-weight: normal;
          }
        }
        .executions {
          display: flex;
          border-bottom: 1px solid #dbdbdb;
          .executions-l {
            border-right: 1px solid #dbdbdb;
          }
          .executions-l,
          .executions-r {
            display: flex;
            flex: 2;
            align-items: center;
            justify-content: center;
            padding: 20px 10px;
          }
          .executions-r {
            flex: 3;
            font-weight: normal;
          }
        }
        .team {
          display: flex;
          border-bottom: 1px solid #dbdbdb;
          .team-l {
            border-right: 1px solid #dbdbdb;
          }
          .team-l,
          .team-r {
            display: flex;
            flex: 2;
            align-items: center;
            justify-content: center;
            padding: 20px 10px;
          }
          .team-r {
            flex: 3;
            font-weight: normal;
          }
        }
        .complete {
          display: flex;
          .complete-l {
            border-right: 1px solid #dbdbdb;
          }
          .complete-l,
          .complete-r {
            display: flex;
            flex: 2;
            align-items: center;
            justify-content: center;
            padding: 20px 10px;
          }
          .complete-r {
            flex: 3;
            font-weight: normal;
          }
        }
      }
    }
  }
}
.report-content-box {
  background: #edf3ff;
}
.pageContent {
}
#chart20 {
  width: 100%;
  height: 100%;
}
.information-c {
  margin-bottom: 30px;
}
.bg-top-theme {
  height: 325px;
  display: flex;
  align-items: center;
  padding: 0px 30px;
  .bg-top-theme-l {
    flex: 1;
    color: $ColorMain;
    padding-right: 60px;
  }
  .bg-top-theme-info-t {
    font-weight: 550;
    width: 250px;
    overflow: hidden;
  }
  .bg-top-img {
    width: 255px;
    height: 168px;
    // height: 208px;
  }
  .analysis-report-t {
  }
  .bgimg {
    width: 100%;
    height: auto;
  }
  .bg-top-theme-info-t {
    font-size: 28px;
    font-weight: 500;
    line-height: 1.5;
    color: $ColorMain;
  }
  .bg-top-theme-info {
    margin-top: 30px;
    background: #fff;
    display: inline-block;
    font-size: 22px;
    line-height: 2;
    padding: 0 20px;
    border-radius: 20px;
  }
}
.bg-top-border-one {
  // height: 300px;
  padding: 10px;
  box-sizing: border-box;
  background: #cbdeff;
  border-radius: 10px;
  margin-bottom: 20px;
  .bg-top-border-two {
    background: #fff;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    padding: 30px 45px 48px 30px;
  }
  .bg-top-border-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  .bg-top-border-item:last-child {
    margin-bottom: 0;
  }
  .bg-top-border-label {
    min-width: 230px;
    width: 230px;
    display: flex;
    align-items: center;
    color: $ColorMain;
  }
  .bg-top-border-value {
    flex: 1;
  }
  .bg-top-border-ico {
    width: 25px;
    height: 25px;
    margin-right: 10px;
  }
}
.user-content-box {
  // background: #fff;
  overflow: hidden;
  .project-info,
  .project-introduce,
  .project-result {
    padding: 20px;
    background: #fff;
    .title {
      font-weight: bold;
      font-size: 22px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .info {
      font-size: 14px;
      text-indent: 2em;
      line-height: 2;
      padding-top: 20px;
    }
    .title-table {
      margin-top: 20px;
      border: 1px solid #dbdbdb;
      font-size: 16px;
      font-weight: 550;
      .name,
      .execution,
      .project-bott {
        span {
          // font-weight: bold;
          // font-size: 24px;
        }
        .project-bott-l,
        .project-bott-r {
          span {
            // font-weight: bold;
            // font-size: 24px;
          }
        }
        .project-bott-r {
          span {
          }
        }
      }
      .name,
      .execution {
        padding: 20px 10px;
        border-bottom: 1px solid #dbdbdb;
      }
      .execution {
      }
      .project-bott {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .project-bott-l {
          display: flex;
          flex: 1;
          border-right: 1px solid #dbdbdb;
          padding: 20px 10px;
          align-items: center;
        }
        .project-bott-r {
          display: flex;
          flex: 1;
          padding: 20px 10px;
        }
      }
    }
  }
  .project-introduce {
    margin-top: 50px;
    .title {
    }
    .info {
    }
  }
  .project-result {
    margin-top: 50px;
    .title {
    }
    .info {
    }
    .title-table {
      border: 1px solid #dbdbdb;
      font-size: 18px;

      .item {
        display: flex;
        border-bottom: 1px solid #dbdbdb;
        .item-l {
          border-right: 1px solid #dbdbdb;
        }
        .item-l,
        .item-r {
          display: flex;
          flex: 2;
          align-items: center;
          justify-content: center;
          padding: 20px 10px;
          // font-weight: bold;
        }
        .item-r {
          flex: 3;
          font-weight: normal;
        }
      }
      .serve {
        display: flex;
        border-bottom: 1px solid #dbdbdb;
        .serve-l {
          border-right: 1px solid #dbdbdb;
        }
        .serve-l,
        .serve-r {
          display: flex;
          flex: 2;
          align-items: center;
          justify-content: center;
          padding: 20px 10px;
          font-weight: bold;
          // font-size: 24px;
        }
        .serve-r {
          flex: 3;
          font-weight: normal;
        }
      }
      .executions {
        display: flex;
        border-bottom: 1px solid #dbdbdb;
        font-weight: bold;
        .executions-l {
          border-right: 1px solid #dbdbdb;
        }
        .executions-l,
        .executions-r {
          display: flex;
          flex: 2;
          align-items: center;
          justify-content: center;
          padding: 20px 10px;
          // font-size: 24px;
        }
        .executions-r {
          flex: 3;
          font-weight: normal;
        }
      }
      .team {
        display: flex;
        border-bottom: 1px solid #dbdbdb;
        .team-l {
          border-right: 1px solid #dbdbdb;
        }
        .team-l,
        .team-r {
          display: flex;
          flex: 2;
          align-items: center;
          justify-content: center;
          padding: 20px 10px;
          font-weight: bold;
          // font-size: 24px;
        }
        .team-r {
          flex: 3;
          font-weight: normal;
        }
      }
      .complete {
        display: flex;
        .complete-l {
          border-right: 1px solid #dbdbdb;
        }
        .complete-l,
        .complete-r {
          display: flex;
          flex: 2;
          align-items: center;
          justify-content: center;
          padding: 20px 10px;
          font-weight: bold;
          // font-size: 24px;
        }
        .complete-r {
          flex: 3;
          font-weight: normal;
        }
      }
    }
  }

  .user-content-t {
    // margin-top: 40px;
    font-size: 28px;
    font-weight: 550;
    background: #cbdefe;
    line-height: 1.5;
    // padding: 0 20px;
    border-radius: 10px;
    // margin-bottom: 20px;
    // display: flex;
    // justify-content: center;
    display: inline-block;
    font-size: 18px;
    padding: 5px 20px;
    margin: 40px auto 20px;
  }
  .user-content-c {
    margin-bottom: 40px;
    padding: 0 20px;
    line-height: 1.5;
  }
  .user-d-flex {
    display: flex;
    justify-content: center;
  }
}
.user-activity-box {
  .title {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 24px;
  }
  .basic-info {
    font-size: 16px;
    line-height: 1.5;
    .basic-title {
      padding-top: 40px;
      // padding-bottom: 30px;
      font-weight: bold;
      font-size: 16px;
    }
    .basic-text1 {
    }
    .basic-text2 {
    }
    .basic-text3 {
    }
  }
  .health-info {
    margin-top: 30px;
    font-size: 16px;
    line-height: 1.5;

    .health-title {
      padding-bottom: 15px;
      font-weight: bold;
      font-size: 20px;
    }
    .health-text {
    }
  }
  .barTarget .chart {
    width: 100%;
    height: 100%;
  }
  .panel {
    width: 100%;
    height: 400px;
  }
}
.user-execute-box {
  .title {
    font-weight: bold;
    font-size: 24px;
    line-height: 2;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .basic-info {
    font-size: 16px;
    line-height: 30px;
    .basic-text1,
    .basic-text2 {
      text-indent: 2em;
    }
  }
}

.mg50 {
  margin-bottom: 50px;
  margin-top: 50px;
}
.reportContent-tip {
  line-height: 2;
  padding: 10px;
  text-align: right;
  font-size: 14px;
}
//饼图的大小
.chartsGl {
  height: 200px;
  width: 380px;
}
//饼图底座（我也想给你们底座图片 可是我不知道咋给）
.buttomCharts {
  background: center top
    url(data:image/png;base64,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)
    no-repeat;
  background-size: 100% 100%;
  height: 350px;
  width: 365px;
  margin-top: -443px;
  margin-left: -29px;
}

.reportContent-lt {
  font-size: 14px;
  font-weight: 550;
  padding: 20px 0;
}
.reportContent-t {
  font-size: 18px;
  text-align: center;
  font-weight: 550;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: $ColorMain;
}
.reportContent-tl {
  height: 7px;
  width: 150px;
  /* background: #737576; */
  margin-right: 20px;
  background: linear-gradient(to right, #edf3ff, #4e8cfd);
  border-radius: 40px;
  // border-radius: 2;
}
.reportContent-tr {
  height: 7px;
  width: 150px;
  /* background: #737576; */
  margin-left: 20px;
  background: linear-gradient(to right, #4e8cfd, #edf3ff);
  border-radius: 40px;
}
.gender-content {
  width: 100%;
  .title-table {
    margin-top: 20px;
    border: 1px solid #dbdbdb;
    .item-title,
    .serve-title,
    .executions-title,
    .team-title {
      display: flex;
      border-bottom: 1px solid #dbdbdb;
      .item,
      .serve,
      .executions,
      .team {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        padding: 20px 10px;
        font-weight: bold;
        // font-size: 24px;
        &:nth-child(1) {
          border-right: 1px solid #dbdbdb;
        }
        &:nth-child(2) {
          border-right: 1px solid #dbdbdb;
        }
      }
    }
    .team-title {
      border-bottom: 0;
    }
  }
}
.reportContent-l {
  flex: 1;
  width: 100%;
}
.reportContent-box {
  .d-flex {
    display: flex;
  }
  .barTarget .chart {
    width: 100%;
    height: 100%;
  }
  .panel {
    width: 100%;
    height: 400px;
  }

  .barTarget .chart10,
  .barTarget .chart11 {
    width: 100%;
    height: 100%;
  }
  .table-right-txt {
    display: flex;
    justify-content: flex-end;
    line-height: 2;
    color: #7c7c7c;
    padding: 10px 0;
  }

  .project-executor-t {
    font-size: 16px;
    font-weight: 550;
    padding: 20px 0;

    // line-height: 2;
  }

  .information-border-one {
    // border: 2px solid #a0a1a4;
    padding: 5px;
    // background: #f8fbff;
    margin-top: 50px;
  }
  .information-border-two {
    // border: 1px solid #a0a1a4;
    padding: 20px;
    background: #fff;
  }
  .information-txt-box {
    // display: flex;
    // align-items: center;
    line-height: 1.5;
    flex-wrap: wrap;
    word-break: break-all;
    font-size: 16px;
  }
  .information-t {
    font-size: 16px;
    font-weight: 550;
    padding-right: 10px;
    display: flex;
    align-items: center;
  }
  .information-t-r {
    width: 5px;
    height: 5px;
    background: #dbdbdb;
    border-radius: 50%;
    margin-right: 5px;
  }

  .barTarget .barChart {
    width: 100%;
    height: 100%;
  }
}

.project-executor-information-box {
  .leftLine .chart,
  .rightLine .chart,
  .rightLine .chart4,
  .rightLine .chart2,
  .rightLine .chart3,
  .barTarget .chart,
  .rightLine .chart24,
  .rightLine .chart22 {
    width: 100%;
    height: 100%;

    // .pannel-footer {
    //   position: absolute;
    //   width: 100%;
    //   bottom: 0;
    //   left: 0;
    //   &::before {
    //     position: absolute;
    //     content: "";
    //     left: 0;
    //     bottom: 0;
    //     width: 10px;
    //     height: 10px;
    //     border-left: 2px solid #02a6b5;
    //     border-bottom: 2px solid #02a6b5;
    //   }

    //   &::after {
    //     position: absolute;
    //     content: "";
    //     right: 0;
    //     bottom: 0;
    //     width: 10px;
    //     height: 10px;
    //     border-right: 2px solid #02a6b5;
    //     border-bottom: 2px solid #02a6b5;
    //   }
    // }
  }

  .panel {
    width: 100%;
    height: 300px;
    position: relative;
    // height: 100%;
  }
  .d-flex {
    display: flex;
    align-items: center;
  }
  .project-executor-information-l,
  .project-executor-information-r {
    flex: 1;
  }
  .project-executor-information-r {
    display: flex;
    justify-content: center;
    height: 300px;
    // align-items: center;
  }

  .age-tabs {
    display: flex;
    // align-items: center;
    align-items: flex-start;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 90%;
    min-height: 102px;
  }
  .age-tab-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-right: 10px;
    margin-bottom: 10px;
    line-height: 2;
  }
  .age-radio {
    width: 15px;
    height: 15px;
    margin-right: 10px;
    border-radius: 50%;
  }
  .pannel-footer {
    position: absolute;
    // height: 150px;
    top: 255px;
    bottom: 0;
    left: 0;
    right: 0;
    // background: yellow;
    width: 100%;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .age-tabs-t {
    // position: absolute;
    // bottom: 0;
    display: flex;
    justify-content: center;
    font-size: 14px;
    font-weight: 550;
    width: 40%;
    border: 1px solid #dbdbdb;
    line-height: 2;
    margin-bottom: 10px;
    width: 180px;
  }
  .age-tabs-info {
    font-size: 24px;
    display: flex;
    justify-content: center;
    font-weight: 550;
    width: 40%;
    // border: 1px solid #dbdbdb;
    line-height: 2;
    margin-bottom: 10px;
  }
  .sex-border-one {
    height: 300px;
    display: flex;
    width: 300px;
    justify-content: center;
    overflow: hidden;
    position: relative;
  }
  .sex-border-one-l {
    width: 70px;
    height: 70px;
    border-top: 1px dashed #dbdbdb;
    border-left: 1px dashed #dbdbdb;
    transform: rotate(-45deg);
    position: absolute;
    top: 10px;
    left: 0;
  }
  .sex-border-one-c {
    width: 185px;
    border-top: 1px dashed #dbdbdb;
    border-bottom: 1px dashed #dbdbdb;
  }
  .seximg-box {
    width: 168px;
    height: 95px;
    margin-top: 80px;
    position: relative;
  }
  .seximg {
    width: 168px;
    height: 95px;
  }
  .seximg-txt {
    position: absolute;
    bottom: 14px;
    left: 26px;
    font-size: 14px;
    color: #fff;
    width: 70px;
    text-align: center;
  }
  .seximg-txt-right {
    position: absolute;
    bottom: 37px;
    right: 5px;
    font-size: 14px;
    color: #fff;
    width: 70px;
    text-align: center;
  }

  .information-border-one {
    // border: 2px solid #a0a1a4;
    padding: 5px;
    background: #f8fbff;
    margin-top: 50px;
  }
  .information-border-two {
    // border: 1px solid #a0a1a4;
    padding: 20px;
    background: #fff;
  }
  .information-txt-box {
    // display: flex;
    // align-items: center;
    line-height: 1.5;
    flex-wrap: wrap;
    word-break: break-all;
    font-size: 16px;
  }
  .information-t {
    font-size: 16px;
    font-weight: 550;
    padding-right: 10px;
  }
  .project-executor-t {
    font-size: 16px;
    font-weight: 550;
    padding: 20px 0;

    // line-height: 2;
  }
  .pannel-footer3 {
    // background: yellow;
    width: 100%;
    flex-direction: column;
    display: flex;
    // justify-content: center;
    align-items: center;
    height: 180px;
  }
}
.analysis-report-box {
  position: absolute;
  top: 200px;
  bottom: 210px;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .title,
  .activity {
    font-size: 35px;
    font-weight: bold;
  }
  .activity {
    margin-top: 40px;
  }
  .project-report {
    margin-top: 180px;
    margin-bottom: 80px;
    font-size: 24px;
  }
  .server {
    margin-top: 220px;
  }
  .server,
  .project-leader {
    font-size: 22px;
    font-weight: bold;
  }
  .project-leader {
    margin-top: 20px;
  }
}

::v-deep .header-row-class-name th {
  background: #dfe6ec;
}

.mgt30 {
  margin-top: 30px;
}
.pageContent {
  width: 100%;
}
.everyPage {
  overflow: hidden;
  padding: 0 0px 5px;
  box-sizing: border-box;
}
.everyPageContent {
  width: 100%;
  height: 100%;
  // background: #fff;
  background: #edf3ff;
}
// 封面
.cover-page {
  padding-top: 100px;
  padding-left: 50px;
  height: 100%;
  overflow: hidden;
  background: #fff;
  box-sizing: border-box;

  .cover-page-bottom-txt {
    // display:flex;
    margin-top: 50px;
    line-height: 2;
    font-size: 18px;
    text-align: center;
  }
  .cover-text-box-info {
    font-size: 16px;
    color: #7c7c7c;
  }

  .cover-text-box-bottom {
    position: absolute;
    bottom: 100px;
    left: 50px;
    color: #fff;
  }

  .cover-text-box-title {
    font-size: 40px;
    color: #fff;
  }

  .cover-page-icon {
    margin-bottom: 30px;
  }

  .cover-page-img-box {
    position: relative;
  }

  .cover-text-box {
    position: absolute;
    top: 100px;
    left: 30px;
  }

  .cover-page-img {
    width: 100%;
  }

  .cover-text-box-info {
    color: #fff;
    font-size: 24px;
  }
}
// 目录
.directory-page {
  padding: 20px;
  .directory-title {
    font-size: 28px;
    line-height: 2;
    font-weight: 550;
    margin-bottom: 30px;
  }
}

// 问卷活动调研
.serve-page {
  padding: 20px;

  .serve-page-title {
    height: 50px;
    // background: #3a78f1;
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 550;
    // color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0e0800;
  }

  .serve-page-execute-time {
    text-align: center;
    font-weight: 550;
    margin-bottom: 10px;
  }

  .serve-page-execute-sub1 {
    font-weight: 550;
    margin-bottom: 20px;
  }

  .serve-page-execute-center-t1 {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
    font-weight: 550;
  }

  .serve-page-execute-introduction {
    text-indent: 2em;
    color: #7c7c7c;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 40px;
  }

  .serve-cols {
    display: flex;
    align-items: center;
    height: 40px;
    // border-bottom: 1px solid #dbdbdb;
    border-top: none;
  }
  .serve-col {
  }
  .serve-cols-title {
    padding: 20px;
    background: #ffefef;
    // border: 2px solid #de4040;
    line-height: 1.5;
    color: #de4040;
  }
  .border-bottom {
    border-bottom: 1px solid #dbdbdb;
  }
  .serve-col-span {
    width: 130px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .serve-col-label {
    padding-left: 30px;
  }
  .serve-cols-info {
    line-height: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #edf3ff;
    color: blue;
  }
}

.person-serve-detail {
  padding: 20px;

  .person-serve-detail-title {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 550;
    font-size: 18px;
  }
  .person-serve-detail-until {
    display: flex;
    justify-content: flex-end;
  }
}

.one {
  background: yellow;
}
.two {
  background: blue;
}
</style>


<style lang="scss">
.el-table.ue-table {
  background: #edf3ff;
  th,
  td {
    background: #edf3ff;
  }
}
</style>