/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 * 安装单位分组
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/installgroup/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/installgroup/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/installgroup/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/installgroup/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/installgroup/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/installgroup/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/installgroup/update`, data)
}

// 获取绑定分页
export function queryBindPage(data) {
  return requestV1.postJson(`${prefix}/installgroup/query/bind/page`, data)
}

// 获取未绑定分页
export function queryUnbindPage(data) {
  return requestV1.postJson(`${prefix}/installgroup/query/unbind/page`, data)
}

// 新增绑定
export function installgrouprelationBind(data) {
  return requestV1.postJson(`${prefix}/installgrouprelation/bind`, data)
}

// 批量删除安装单位分组和安装单位关联
export function installgrouprelationDeleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/installgrouprelation/delete/batch/${data.ids}`)
}

// 安装单位分组列表查询 包含安装单位ids
export function installgrouprelationQueryList(data) {
  return requestV1.get(`${prefix}/installgroup/query/list/bybusiness`, data)
}

// 获取安装单位分组
export function installgroupQueryList(data) {
  return requestV1.get(`${prefix}/installgroup/query/list`, data)
}
