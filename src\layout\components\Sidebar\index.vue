<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <div v-show="sidebar.opened" class="search-bar-wrapper">
      <SearchBar @change="(e) => { searchRouter = e }" />
    </div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" :background-color="variables.menuBg"
        :text-color="variables.menuText" :unique-opened="false" :active-text-color="variables.menuActiveText"
        :hover-background="variables.hoverBackground" 
        :collapse-transition="false" mode="vertical">
        <sidebar-item v-for="(route, index) in searchRouter" :key="index" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import path from 'path'
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'
import SearchBar from './SearchBar.vue'
import AppLink from './Link'
import { generateTitle } from '@/utils/i18n'
import Item from './Item'

export default {
  components: {
    SidebarItem,
    Logo,
    SearchBar,
    AppLink,
    Item
  },
  data() {
    return {
      searchRouter: []
    }
  },
  created() {
    this.searchRouter = [...this.permission_routes]
    this.handleGetBrandLogo()
  },
  computed: {
    ...mapGetters([
      'permission_routes',
      'sidebar'
    ]),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
      // return true
    }
  },
  mounted() {
    // let h = document.querySelector('.fixed-header').clientHeight;
    // document.querySelector('.app-main').style.paddingTop = h + 'px'

    // console.log(this.permission_routes)
  },
  methods: {
    generateTitle,
    resolvePath(basePath, routePath) {
      return path.resolve(basePath, routePath)
    },
    handleGetBrandLogo(){
      this.$store.dispatch('user/getBrandQueryInitLogo');
    }
  }
}
</script>

<style scoped>
  >>>.el-menu{
    background: rgba(0, 0, 0, 0);
 }
.search-bar-wrapper {
  margin: 5px 15px;
}
</style>
