import requestV1 from '@/common/utils/modules/request'

const prefix = '/sop/api/v1'

/**
 * 送货单字段对照表
 */

// 绑定物料
export function bind (data) {
    return requestV1.postJson(`${prefix}/comparisonmaterial/bind`, data)
}

// 已绑定分页查询
export function queryBindPage (data) {
    return requestV1.postJson(`${prefix}/comparisonmaterial/query/bind/page`, data);
}

// 未绑定分页查询
export function queryUnBindPage (data) {
    return requestV1.postJson(`${prefix}/comparisonmaterial/query/un/bind/page`, data);
}

// 取消关联
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/comparisonmaterial/delete/one/${data.id}`)
}
