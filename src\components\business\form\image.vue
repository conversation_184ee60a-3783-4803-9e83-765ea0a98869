<!--上传图片组件-->
<template>
    <div class="upload">
      <el-upload
        ref="uploadImage"
        :class="childClass"
        :on-preview="imagePreview"
        :on-remove="imageRemove"
        :file-list="file.image.list"
        :multiple="config.multiple"
        :headers="header"
        :data="config.param"
        :limit="config.limit"
        :before-upload="imageBefore"
        :on-success="imageSuccess"
        :on-error="imageError"
        :on-change="onChange"
        :accept="config.accept"
        :action="action"
        :auto-upload="config.autoUpload"
        list-type="picture-card"
        class="upload-image"
      >
        <el-button size="mini" type="primary" >
          点击上传
        </el-button>
        <div slot="tip" class="el-upload__tip">
          规格说明:支持{{ config.accept }}类型，数量最多{{ config.limit }}张，单张不能超过{{ config.size/1000 }}Mb
        </div>
      </el-upload>
      <el-dialog
        :visible.sync="file.image.dialogImageVisible"
        :modal="false"
      >
        <img :src="file.image.dialogImageUrl" width="100%" alt="">
      </el-dialog>
    </div>
</template>
<script>
import { getToken } from '@/utils/auth'
export default {
  name: 'Image',
  components: {

  },
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '图片上传',
          name: 'image',
          limit: 3,
          size: 100000,
          accept: 'image/*',
          multiple: false,
          param: {},
          data: [],
          number: 2,
          autoUpload: true,
          rules: [
            { required: true, message: '请上传图片', trigger: 'blur' }
          ]
        }
      }
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    data: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      action: this.$env.ctx + '/basics/api/v1/attachment/upload',
      header: {
        Authorization: getToken()
      },
      file: {
        image: {
          dialogImageUrl: '',
          dialogImageVisible: false,
          list: [],
          data: []
        }
      },
      fileList:[]
    }
  },
  watch: {
    data: {
      handler(val) {
        if (val.length !== 0) {
          const array = val.split(',')
          for (const k of array) {
            const imageData = this.file.image.data
            let isExit = false
            for (const j of imageData) {
              if (k === j.dir) {
                isExit = true
                break
              }
            }
            if (isExit) {
              return
            }
            const obj = {
              name: '',
              url: this.$env.file_ctx + k,
              dir: k
            }

            this.file.image.list.push(obj)
            this.file.image.data.push(obj)
          }
        } else {
          this.file.image.list = []
          this.file.image.data = []
        }
      },
      deep: true
    }
  },
  methods: {
    /**
     * 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
     * @param file
     * @param fileList
     */
    onChange(file, fileList){
      // debugger
      // console.log(file)
      // console.log(fileList)
      this.fileList = fileList
    },
    /**
     * 点击上传，当设置为手动上传的时候
     */
    submitUpload() {
      if (this.fileList.length === 0){
        this.$eltool.warnMsg('请选择文件上传')
        return false
      }
      this.$nextTick(() => {
        this.$refs.uploadImage.submit()
      })
    },
    /**
     * 图片预览
     * @param file
     */
    imagePreview(file) {
      // debugger
      this.file.image.dialogImageUrl = file.url
      this.file.image.dialogImageVisible = true
    },
    /**
     * 删除图片
     * @param file
     * @param fileList
     */
    imageRemove(file, fileList) {
      this.fileList = fileList
      // debugger
      const that = this
      const dir = file.dir
      that.file.image.data = that.file.image.data.filter((item) => {
        return item.dir !== dir
      })
      that.file.image.list = that.file.image.list.filter((item) => {
        return item.dir !== dir
      })
      that.$emit('updateImage', that.config.name, that.file.image.data)
      that.$emit('uploadResult', that.config.name, that.file.image.data)
    },
    /**
     * 上传成功
     * @param response
     * @param file
     * @param fileList
     */
    imageSuccess(response, file, fileList) {
      // debugger
      const that = this
      const data = response
      if (data.code === 0) {
        for (const v of data.result) {
          const listData = {
            name: v.name,
            url: this.$env.file_ctx + v.dir,
            dir: v.dir
          }
          that.file.image.list.push(listData)
          that.file.image.data.push(v)
        }
        that.$emit('updateImage', that.config.name, that.file.image.data)
        that.$emit('uploadResult', that.config.name, that.file.image.data)
      } else {
        return false
      }
    },
    /**
     * 上传失败
     * @param err
     * @param file
     * @param fileList
     */
    imageError(err, file, fileList) {
      // debugger
      console.log(err)
      this.$eltool.errorMsg('上传发生异常')
      return false
    },
    /**
     * 图片上传前的钩子函数
     * @param file
     */
    imageBefore(file) {
      if (this.file.file.data.length === 0 && this.config.number > 0) {
        this.$eltool.warnMsg('文件不能为空！')
      }
      if (this.config.number !== 0) {
        if (this.file.image.data.length >= this.config.number) {
          this.$eltool.warnMsg('只能上传' + this.config.number + '张图片')
          return false
        }
      }

      if (file.size / 1000 > this.config.size) {
        this.$eltool.warnMsg('已超过文件大小限制')
        return false
      }
    }
  }
}
</script>
