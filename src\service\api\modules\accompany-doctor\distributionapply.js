/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 申请审核 
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/distributionapply/delete/batch/${data.ids}`)
}

// 保存数据-添加
export function insert (data) {
    return requestV1.postJson(`${prefix}/distributionapply/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/distributionapply/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/distributionapply/query/one`, data)
}

// 分页列表查询代办模板
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/distributionapply/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/distributionapply/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/distributionapply/update`, data)
}

// 审核记录
export function audit (data) {
  return requestV1.postForm(`${prefix}/distributionapply/auditLog`, data)
}
