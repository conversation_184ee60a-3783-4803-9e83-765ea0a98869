import requestV1 from '@/common/utils/modules/request'

const prefix = '/sop/api/v1'

/**
 * 退货审核记录
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/returngoodauditlog/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/returngoodauditlog/query/page`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/returngoodauditlog/query/list`, data);
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/returngoodauditlog/query/one`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/returngoodauditlog/update`, data)
}

// 审核
export function auditCommit (data) {
  return requestV1.postForm(`${prefix}/returngoodauditlog/audit/commit`, data)
}
