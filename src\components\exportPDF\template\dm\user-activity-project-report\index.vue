<template>
  <div :id="uuid" class="report-content-box" v-loading="pageLoading">
    <!-- 封面 -->
    <div
      class="settlement-page"
      :style="{
        height: pageSize.height + 'px',
        width: pageSize.width + 'px',
      }"
    >
      <div class="theme-img">
        <img :src="bgImg" class="" alt="" />
      </div>
      <div class="analysis-report-box">
        <div class="activity">
          {{ thumbTitle }}
        </div>
        <div class="project-report">
          项&nbsp;目&nbsp;服&nbsp;务&nbsp;报&nbsp;告
        </div>
        <div class="execution-time">
          执行时间：{{ fixedFieldObject.startTime }} 至
          {{ fixedFieldObject.endTime }}
        </div>
        <div class="server">服务方：{{ fixedFieldObject.serviceProvider }}</div>
        <div class="project-leader">
          项目方：{{ fixedFieldObject.projectParty }}
        </div>
      </div>
    </div>
    <!-- 目录 -->
    <div
      class="settlement-page"
      :style="{
        height: pageSize.height + 'px',
        width: pageSize.width + 'px',
      }"
    >
      <div class="theme-img">
        <img :src="themeImg" class="" alt="" />
      </div>
    </div>
    <div class="pageContent">
      <template v-for="(page, index) in pageContent">
        <div
          :key="index"
          v-if="page.authHeight && !page.hidden"
          :style="{
            width: pageSize.width + 'px',
            height: page.targetHeight ? page.targetHeight + 'px' : 'auto',
          }"
          :id="authHeightResult[index]"
        >
          <div class="everyPageContent">
            <!-- 基础信息、北京介绍、验收结果 -->
            <div class="settlement-page" v-if="page.type === 'allContent'">
              <div class="user-content-box">
                <!-- 信息 -->
                <div class="project-info">
                  <div class="title color1">
                    <img :src="productImgUrl" class="title-icon" alt="" />
                    项目基础信息
                  </div>
                  <div class="title-table">
                    <div class="name">
                      项目名称：<span class="general">{{
                        fixedFieldObject.projectName
                      }}</span>
                    </div>
                    <div class="execution">
                      项目执行周期：<span class="general"
                        >{{ fixedFieldObject.startTime }} 至
                        {{ fixedFieldObject.endTime }}</span
                      >
                    </div>
                    <div class="project-bott">
                      <div class="project-bott-l">
                        项目方：
                        <span class="general">{{
                          fixedFieldObject.projectParty
                        }}</span>
                      </div>
                      <div class="project-bott-r">
                        <span class="white-space">服务商：</span>
                        <span class="general">{{
                          fixedFieldObject.serviceProvider
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 介绍 -->
                <div class="project-introduce">
                  <div class="title color1">
                    <img :src="productBgUrl" class="title-icon" alt="" />
                    项目背景介绍
                  </div>
                  <div class="info">
                    <div class="info-t">
                      小葫芦平台，是{{
                        fixedFieldObject.projectParty
                      }}，潜心打造的医患社交平台。旨在打造一个医药大健康信息平台，为用户提供真实可靠的病友互助沟通渠道。该平台日常发布权威的健康科普资讯，为用户提供专业的健康咨询服务。同时，患者也可以在平台上发布健康求助贴子，与其他病友分享治疗经验，共同探索健康的捷径。
                    </div>
                    <br />
                    <div class="info-b">
                      <template v-if="businessType === 18">
                        为了进一步提升{{
                          fixedFieldObject.projectParty
                        }}旗下小葫芦平台的用户数量、活跃度和互动率，我们组织了线上推广团队，通过精准定位目标用户群体，利用微信生态内的强大社交属性，引导潜在用户高频参与内容点赞、评论等社交互动，构建用户与平台的情感联结，持续提升小葫芦平台的活跃度并增强用户粘性。
                      </template>
                      <template v-else>
                        为了进一步提升{{
                          fixedFieldObject.projectParty
                        }}旗下小葫芦平台的用户数量、活跃度和互动率，我们组织了推广团队，在全国范围的医院、药店等人流密集的场景中开展了线下用户活动，通过引导潜在用户高频参与内容点赞、评论等社交互动，构建用户与平台的情感联结，持续提升小葫芦平台的活跃度并增强用户粘性。
                      </template>
                    </div>
                  </div>
                </div>
                <!-- 结果 -->
                <div class="project-result" style="margin-top: 460px">
                  <div class="title color1">
                    <img :src="productResultUrl" class="title-icon" alt="" />
                    项目验收结果
                  </div>
                  <div class="info">
                    <template v-if="businessType === 18">
                      项目“关于小葫芦平台的线上用户活动项目”由{{
                        fixedFieldObject.serviceProvider
                      }}于 {{ fixedFieldObject.startTime }} 至
                      {{ fixedFieldObject.endTime }} 承接执行。{{
                        fixedFieldObject.projectParty
                      }}于当月最后一天验收项目，本项目由
                      {{ fixedFieldObject.productTaskNumber }}
                      个推广人员执行，共完成新用户注册数{{
                        activityResultObject.allWriteOptionNum
                      }}人，点赞数{{
                        fixedFieldObject.approveNumber
                      }}条，评论数{{
                        fixedFieldObject.commentNumber
                      }}条，验收结果如下。
                    </template>
                    <template v-else>
                      项目“关于小葫芦平台的线下用户活动项目”由{{
                        fixedFieldObject.serviceProvider
                      }}于 {{ fixedFieldObject.startTime }} 至
                      {{ fixedFieldObject.endTime }} 承接执行。{{
                        fixedFieldObject.projectParty
                      }}于当月最后一天验收项目，本项目由
                      {{ fixedFieldObject.productTaskNumber }}
                      个推广团队执行，共完成新用户注册数{{
                        activityResultObject.allWriteOptionNum
                      }}人，点赞数{{
                        fixedFieldObject.approveNumber
                      }}条，评论数{{
                        fixedFieldObject.commentNumber
                      }}条，验收结果如下。
                    </template>
                  </div>
                  <div class="title-table">
                    <div class="serve">
                      <div class="serve-l">项目方</div>
                      <div class="serve-r">
                        {{ fixedFieldObject.projectParty }}
                      </div>
                    </div>
                    <div class="serve">
                      <div class="serve-l">服务方</div>
                      <div class="serve-r">
                        {{ fixedFieldObject.serviceProvider }}
                      </div>
                    </div>
                    <div class="executions">
                      <div class="executions-l">项目执行时间</div>
                      <div class="executions-r">
                        {{ fixedFieldObject.startTime }} 至
                        {{ fixedFieldObject.endTime }}
                      </div>
                    </div>
                    <div class="team">
                      <div class="team-l">
                        <template v-if="businessType === 18">
                          推广人员数量
                        </template>
                        <template v-else> 推广团队数量 </template>
                      </div>
                      <div class="team-r">
                        {{ fixedFieldObject.productTaskNumber }}
                      </div>
                    </div>
                    <div class="team">
                      <div class="team-l">完成新用户注册数量</div>
                      <div class="team-r">
                        {{ activityResultObject.allWriteOptionNum }}
                      </div>
                    </div>
                    <div class="team">
                      <div class="team-l">点赞数量</div>
                      <div class="team-r">
                        {{ fixedFieldObject.approveNumber }}
                      </div>
                    </div>
                    <div class="complete">
                      <div class="complete-l">评论数量</div>
                      <div class="complete-r">
                        {{ fixedFieldObject.commentNumber }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="settlement-page"
              id="locationDistributionOfTheLocal"
              v-if="page.type === 'locationDistributionOfTheLocal'"
            >
              <!-- 地推团队位置分布 -->
              <div class="user-activity-box">
                <div class="title color1">
                  <img
                    :src="productResultAnalysisUrl"
                    class="title-icon"
                    alt=""
                  />
                  项目结果分析
                </div>
                <div class="basic-info">
                  <div class="page-h3">
                    <template v-if="businessType === 18">
                      1.推广人员位置分布
                    </template>
                    <template v-else> 1.推广团队位置分布 </template>
                  </div>
                  <div class="page-location-table">
                    <div class="reportContent-box">
                      <div class="chartItem">
                        <div
                          style="
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                          "
                        >
                          <div class="reportContent-l">
                            <div class="panel barTarget">
                              <div
                                class="barChart"
                                :id="uuid + '_locationArea'"
                              ></div>
                              <!-- <div
                                class="buttomCharts"
                                v-if="
                                  page.pageContent.staffAreaObject.showBottom
                                "
                              ></div> -->
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="taskUserRegionSpace"></div>
                    <el-table
                      :data="page.pageContent.taskUserRegion"
                      border
                      style="width: 100%"
                    >
                      <el-table-column
                        prop="optionValue"
                        label="地区"
                        width="180"
                        :isReportTable="true"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="selectOptionNum"
                        label="人数"
                        :isReportTable="true"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="selectOptionProportion"
                        label="占比"
                        :isReportTable="true"
                      >
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
            <!-- 地推活动结果分析 -->
            <div class="settlement-page" v-if="page.type === 'activityResult'">
              <div class="user-activity-box">
                <!-- <div class="title">地推活动结果分析</div> -->
                <div class="basic-info">
                  <div class="page-h2 color1">
                    <img
                      :src="recruitNewUserInformationUrl"
                      class="title-icon"
                      alt=""
                    />
                    拉新用户信息
                  </div>
                  <div
                    class="basic-text1 mgb10"
                    v-if="activityResultObject.isSex"
                  >
                    在新注册的用户中，男性占{{ basicInfo.man }}%，女性占{{
                      basicInfo.girl
                    }}%。
                  </div>
                  <div
                    class="basic-text2 mgb10"
                    v-if="activityResultObject.isAge"
                  >
                    年龄分布上，18-30岁的患者占{{
                      basicInfo.age_18_30
                    }}%，31-40岁的患者占{{
                      basicInfo.age_31_40
                    }}%，41-50岁的患者占{{
                      basicInfo.age_41_50
                    }}%，51-70岁的患者占{{ basicInfo.age_51_70 }}%。
                  </div>
                </div>
                <div class="health-info" v-if="activityResultObject.isDisease">
                  <div class="health-text">
                    在疾病上，{{ activityResultObject.diseaseStr }}
                  </div>
                </div>
                <div class="reportContent-box" style="margin-top: 50px">
                  <div class="reportContent-lt">
                    {{ page.pageContent.staffAreaIndex
                    }}{{ page.pageContent.staffAreaTitle }}
                  </div>
                  <div class="chartItem">
                    <div
                      style="
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      <div class="reportContent-l">
                        <div class="panel barTarget">
                          <div class="barChart" :id="uuid + '_sexArea'"></div>
                          <div
                            class="buttomCharts"
                            v-if="page.pageContent.staffSexObject.showBottom"
                          ></div>
                        </div>
                      </div>
                      <div class="gender-content">
                        <el-table
                          :data="page.pageContent.staffSexObject.tableData"
                          style="
                            width: 100%;
                            border-left: none;
                            border-right: none;
                          "
                          border
                        >
                          <template
                            v-for="item in page.pageContent.staffSexObject
                              .hearders"
                          >
                            <el-table-column
                              :key="item.key"
                              :prop="item.key"
                              :label="item.title"
                              :width="item.width"
                              :isReportTable="true"
                            >
                              <template slot-scope="scope">
                                <div
                                  class="exporttxt"
                                  :style="scope.row[item.key + 'Style'] || ''"
                                >
                                  {{ scope.row[item.key] }}
                                </div>
                              </template>
                            </el-table-column>
                          </template>
                        </el-table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="settlement-page"
              v-if="page.type === 'newRegistrationDetailsForXiaohuluLa'"
            >
              <!-- newRegistrationDetailsForXiaohuluLa -->
              <div class="health-info">
                <!-- <div class="page-h2">{{ tCount }}.小葫芦拉新注册明细</div> -->
                <div class="page-h2 color1">
                  <img :src="laNewUserUrl" class="title-icon" alt="" />
                  小葫芦平台拉新注册明细
                  <template v-if="userVisitCountVos.length >= 500">
                    （仅展示{{ 500 }}条）
                  </template>
                  <template v-else>
                    （共{{ userVisitCountVos.length }}条）
                  </template>
                </div>
                <div class="btw pd30">
                  <div
                    class="c-table"
                    :style="{
                      width: page.pageContent.serveHeadersWidth + 'px',
                      zoom: page.pageContent.serveHeadersWidthScale,
                    }"
                  >
                    <el-table
                      :data="userVisitCountVos.slice(0, 500)"
                      border
                      style="width: 100%"
                    >
                      <template v-for="cols in userVisitCountVosHeader">
                        <el-table-column
                          :key="cols.prop"
                          :prop="cols.prop"
                          :label="cols.label"
                          :width="cols.width"
                          :isReportTable="true"
                        >
                          <template slot-scope="scope">
                            <template v-if="['科室/疾病'].includes(cols.prop)">
                              <template
                                v-if="
                                  scope.row[cols.prop] &&
                                  scope.row[cols.prop] !== ''
                                "
                              >
                                {{ scope.row[cols.prop] }}
                              </template>
                              <template v-else>
                                {{ scope.row["疾病"] }}
                              </template>
                            </template>
                            <template v-else-if="['疾病'].includes(cols.prop)">
                              <template
                                v-if="
                                  scope.row[cols.prop] &&
                                  scope.row[cols.prop] !== ''
                                "
                              >
                                {{ scope.row[cols.prop] }}
                              </template>
                              <template v-else>
                                {{ scope.row["科室/疾病"] }}
                              </template>
                            </template>
                            <template v-else>
                              {{ scope.row[cols.prop] }}
                            </template>
                          </template>
                        </el-table-column>
                      </template>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
            <div class="settlement-page" v-if="page.type === 'topicContent'">
              <div class="reportContent-box">
                <div class="chartItem">
                  <div class="reportContent-lt">
                    {{ page.pageContent.index }}{{ page.pageContent.title }}
                  </div>
                  <div
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                    "
                  >
                    <div class="reportContent-l">
                      <div
                        class="panel barTarget"
                        :style="{
                          height: page.pageContent.echartHeight + 'px',
                        }"
                      >
                        <div
                          class="barChart"
                          :id="uuid + '_' + page.pageContent.uuid"
                        ></div>
                        <div
                          class="buttomCharts"
                          v-if="page.pageContent.showBottom"
                          :style="{
                            'margin-top':
                              page.pageContent.echartMarginTop + 'px',
                          }"
                        ></div>
                      </div>
                    </div>
                    <div class="gender-content">
                      <el-table
                        :data="page.pageContent.tableData"
                        style="
                          width: 100%;
                          border-left: none;
                          border-right: none;
                        "
                        border
                      >
                        <template v-for="item in page.pageContent.hearders">
                          <el-table-column
                            :key="item.key"
                            :prop="item.key"
                            :label="item.title"
                            :width="item.width"
                            :isReportTable="true"
                          >
                            <template slot-scope="scope">
                              <div
                                class="exporttxt"
                                :style="scope.row[item.key + 'Style'] || ''"
                              >
                                {{ scope.row[item.key] }}
                              </div>
                            </template>
                          </el-table-column>
                        </template>
                      </el-table>
                      <!-- <div class="reportContent-tip">
                        实际提交：{{ page.pageContent.totalTargetNumber || 0 }}
                        份问卷
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="settlement-page"
              v-if="page.type === 'reportContentBooks'"
            >
              <div class="user-execute-box">
                <div class="title color1">
                  <img :src="executiveSummaryUrl" class="title-icon" alt="" />
                  执行总结
                </div>
                <div class="btw" style="margin-top: 30px">
                  <div class="basic-info">
                    <template v-if="businessType === 18">
                      <div class="basic-text1">
                        本次项目共组织了{{
                          fixedFieldObject.productTaskNumber
                        }}个线上推广人员，通过精准定位目标用户群体，利用微信生态内的强大社交属性，引导潜在用户高频参与内容点赞、评论等社交互动，构建用户与平台的情感联结，持续提升小葫芦平台的活跃度并增强用户粘性。
                      </div>
                      <br />
                      <div class="basic-text1">
                        本次活动中，累计{{
                          activityResultObject.allWriteOptionNum
                        }}位用户完成平台新注册，实现{{
                          fixedFieldObject.approveNumber
                        }}次点赞、{{
                          fixedFieldObject.commentNumber
                        }}条评论的互动数据沉淀（项目服务成果与详细数据，可在项目方系统后台查看详情）。这些结果不仅增加了小葫芦平台的用户数量，还提升了平台整体的活跃度和互动率。通过线上用户活动项目的执行，小葫芦平台在全国范围内的知名度和用户数量得到了显著提升，有效地将平台推广给潜在用户，进一步提高了平台在该领域的影响力，为小葫芦平台的发展奠定了坚实的基础。通过增加用户数量和提高活跃度，平台能够更好地满足用户需求，进一步加强与用户的互动和沟通，这将为平台的未来发展和推广提供有力支持。
                      </div>
                      <br />
                    </template>
                    <template v-else>
                      <div class="basic-text1">
                        本次项目共组织了{{
                          fixedFieldObject.productTaskNumber
                        }}个推广团队，深入全国范围的医院、药店等人流密集场景，开展线下用户活动项目来推广小葫芦平台。推广人员通过赠送小礼品等方式，引导潜在用户扫码进入平台，参与内容点赞、评论等社交互动，构建用户与平台的情感联结，持续提升小葫芦平台的活跃度并增强用户粘性。
                      </div>
                      <br />
                      <div class="basic-text1">
                        本次活动中，累计{{
                          activityResultObject.allWriteOptionNum
                        }}位用户完成平台新注册，实现{{
                          fixedFieldObject.approveNumber
                        }}次点赞、{{
                          fixedFieldObject.commentNumber
                        }}条评论的互动数据沉淀（项目服务成果与详细数据，可在项目方系统后台查看详情）。这些结果不仅增加了小葫芦平台的用户数量，还提升了平台整体的活跃度和互动率。通过线下用户活动项目的执行，小葫芦平台在全国范围内的知名度和用户数量得到了显著提升，有效地将平台推广给潜在用户，进一步提高了平台在该领域的影响力，为小葫芦平台的发展奠定了坚实的基础。通过增加用户数量和提高活跃度，平台能够更好地满足用户需求，进一步加强与用户的互动和沟通，这将为平台的未来发展和推广提供有力支持。
                      </div>
                      <br />
                    </template>

                    <div class="basic-text1">
                      小葫芦平台致力于打造一个患者交流圈，为患者提供一个交流互动的平台。在平台上，患者可以通过发帖、评论等方式与其他患者进行交流，分享自己的经验和感受。小葫芦平台设有健康科普专区，为用户提供丰富的健康知识和科普信息。在该专区，用户可以浏览各类健康科普文章、提高自身的健康意识和健康素养，以预防疾病并更好地管理自己的健康。
                    </div>
                    <br />

                    <div class="basic-text1">
                      小葫芦平台还提供在线咨询服务，用户可以通过平台与医生进行实时的在线咨询。用户可以通过文字、图片、语音等方式向医生描述自己的病情，医生会根据用户信息提供相关建议。这种在线咨询服务方便快捷，为用户提供了便利的医疗服务，尤其对于一些日常症状和健康问题，用户可以及时获得专业的医生建议。
                    </div>
                    <br />

                    <div class="basic-text1">
                      总之，小葫芦平台通过患者交流圈、健康科普专区和在线咨询服务等功能，为用户提供了一个全面的健康管理平台。通过地推活动的推广，小葫芦平台在全国范围内得到了更多用户的关注和参与，未来，小葫芦平台将继续致力于为用户提供更好的健康服务和互动体验，助力用户更好地管理自己的健康。
                    </div>
                  </div>
                  <div class="imgBox">
                    <img :src="bottomIconUrl" class="bottomIconUrl" alt="" />
                  </div>
                </div>
              </div>
            </div>
            <!-- 活动场景展示 -->
            <div
              class="settlement-page"
              v-if="page.type === 'eventSceneDisplay'"
            >
              <div class="activity-title">活动场景展示</div>
              <div class="activity-image-box">
                <!-- page.activitySceneList -->
                <template
                  v-for="item in page.pageContent.eventSceneDisplayImage"
                >
                  <div
                    :key="item.url"
                    class="activity-flex1"
                    :class="{
                      'image-one': imageStyleOne,
                    }"
                  >
                    <div
                      class="activity-image-b"
                      :class="{
                        'image-one': imageStyleOne,
                      }"
                    >
                      <img :src="item.url" class="activity-image" alt="" />
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <!-- 执行团队展示-->
            <div
              class="settlement-page"
              v-if="page.type === 'executiveTeamPresentation'"
            >
              <!-- <div class="activity-title">执行团队展示</div> -->
              <div class="page-h2 color1" v-if="!page.subtitleShow">
                <img :src="onSiteSectionUrl" class="title-icon" alt="" />
                <template v-if="businessType === 18">
                  线上用户活动部分截图展示
                </template>
                <template v-else> 推广现场部分照片展示 </template>
              </div>
              <div class="bgw">
                <div class="activity-image-box">
                  <template
                    v-for="item in signInLogListImage.slice(
                      page.start,
                      page.end
                    )"
                  >
                    <div :key="item.url" class="activity-flex1">
                      <div class="activity-image-b">
                        <img :src="item.url" class="activity-image" alt="" />
                      </div>
                    </div>
                  </template>
                  <template v-if="signInLogListImage.length === 0">
                    <div class="nodate">暂无数据</div>
                  </template>
                </div>
              </div>
            </div>
            <template v-if="page.type === 'promotionDetails'">
              <dynamicsTable
                :pageSize="pageSize"
                :allRenderList="taskUserVoList"
                :updatecount="taskUserVoListUpdateCount"
                :tableHeader="tableHeader"
                @success="successDynamics"
              ></dynamicsTable>
            </template>
            <template v-if="page.type === 'user-activity-personal-detail'">
              <userActivityPersonalReport
                :taskMonth="businessId"
                :taskId="taskId"
                :updatecount="userActivityPersonalReportUpdateCount"
                :businessType="userActivityPersonalReportPageType"
                :inType="isInType"
                @compute="successActivityPersonalReport"
                :levelParams="levelParams"
              ></userActivityPersonalReport>
            </template>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import accurateReportMixins from "@/components/exportPDF/template/mixins/accurate-report.js";
import userActivityPersonalReport from "@/components/exportPDF/template/dm/user-activity-personal-report/index.vue";
import { todotasksGetReportDetail } from "@/api/dmDemand";
import { maxSizeReportImageList } from "@/utils/report-error-image.js";
import { domainURL } from "@/utils/index";
export default {
  mixins: [accurateReportMixins],
  props: {
    businessType: {
      type: Number,
      default: 18,
    },
  },
  components: {
    userActivityPersonalReport,
  },
  data() {
    return {
      userActivityPersonalReportUpdateCount: 0,
      isInType: "user-activity-project",
      targetCount: 2,
      // 固定字段
      fixedFieldObject: {
        projectName: "关于小葫芦平台的线上用户活动项目", // 项目名称
        serviceProvider: "", // 服务方
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己
        startTime: "", // 项目执行开始时间
        endTime: "", // 项目执行结束时间
        productTaskNumber: 0, // 地推团队数量
        approveNumber: 0,
        commentNumber: 0,
      },
      requestType: 6, // 新增type,  精准推广 = 3, 线上推广 = 6
      userImageBoolean: true,
      userImageCount: 4,
      maxImageCount: 40,
      taskItemIdList: [],
      levelParams: {},
      tableHeader: [
        {
          prop: "recordName",
          label: "执行团队代表人姓名",
          width: 180,
        },
        {
          prop: "genderText",
          label: "性别",
          width: 180,
        },
        {
          prop: "userPhone",
          label: "手机号",
          width: 180,
        },
        {
          prop: "submitNum",
          label: "完成新用户注册数量",
          width: 180,
        },
        {
          prop: "userLikeNum",
          label: "邀请点赞数量",
          width: 180,
        },
        {
          prop: "userCommentNum",
          label: "邀请评论数量",
          width: 180,
        }
      ],
    };
  },
  methods: {
    async successActivityPersonalReport() {
      this.pageContent.push(this.reportEnd);
      await this.$nextTick();
      // await this.loadChart();
      this.pageLoading = false;
      let timer = 2000;
      this.$nextTick(() => {
        this.initpage();
        setTimeout(() => {
          this.$emit("compute", {});
          this.addComputeTag();
        }, timer);
      });
    },
    async updateSuccess() {
      this.initsuccesscount += 1;
      console.log(
        "this.initsuccesscount",
        this.initsuccesscount,
        this.targetCount,
        this.chartCountLength
      );
      if (this.initsuccesscount === this.targetCount + this.chartCountLength) {
        this.pageContent.push({
          type: "user-activity-personal-detail",
          authHeight: true,
        });
        this.levelParams = {
          randomValueDesc: 1,
          startCreateTime: this.fixedFieldObject.startTime + " 00:00:00",
          endCreateTime: this.fixedFieldObject.endTime + " 23:59:59",
          taskIdList:
            this.taskItemIdList.length !== 0 ? this.taskItemIdList : [1],
        };
        console.log("this.levelParams====", this.levelParams);
        this.$nextTick(() => {
          this.userActivityPersonalReportUpdateCount += 1;
        });
      }
    },
    async exportProjectAccurateId() {
      const res = await todotasksGetReportDetail(
        {
          taskMonth: this.taskMonth,
          taskType: this.businessType === 18 ? 30 : 32,
        },
        {
          "no-time-manage": 1,
        }
      );
      if (this.businessType === 18) {
        this.fixedFieldObject.projectName = "关于小葫芦平台的线上用户活动项目";
      } else if (this.businessType === 19) {
        this.fixedFieldObject.projectName = "关于小葫芦平台的线下用户活动项目";
      }
      const data = res.data;
      let taskItemIdList = data.taskItemIdList || [];
      this.taskItemIdList = taskItemIdList;
      this.userVisitCountVos = [];
      this.activityResultList = [];
      this.fixedFieldObject.productTaskNumber = data.executeUserNum || 0;
      if (data.taskUserRegion instanceof Object) {
        let taskUserRegion = data.taskUserRegion;
        let tdx = this.pageContent.findIndex(
          (item) => item.type === "locationDistributionOfTheLocal"
        );
        this.pageContent[tdx].pageContent.taskUserRegion = taskUserRegion.map(
          (item) => {
            item.selectOptionProportion += "%";
            return item;
          }
        );
        this.taskUserRegion = taskUserRegion;
        if (this.taskUserRegion.length !== 0) {
          let idx = this.activityResultList.findIndex(
            (item) => item.formType === 18
          );
          if (idx === -1) {
            this.activityResultList.unshift({
              flowConfigId: "",
              flowConfigOrder: 1,
              formTemplateId: "",
              formTemplateOrder: 4,
              formTemplateTitle: "省市",
              formType: 18,
              mandatoryStatus: 1,
              submitId: "",
              answerOptionVoList: [],
              allWriteOptionNum: 0,
            });
            this.chartCountLength = this.activityResultList.length;
          }
        }
      }
      if (data.operatingImageList instanceof Object) {
        let cidx = this.pageContent.findIndex(
          (item) => item.type === "executiveTeamPresentation"
        );
        if (this.imageStyleOne) {
          this.pageContent[cidx].start = 0;
          this.pageContent[cidx].end = 1;
        } else if (this.userImageBoolean) {
          this.pageContent[cidx].start = 0;
          this.pageContent[cidx].end = this.userImageCount;
        }
        let imagePathArr = [];
        for (let i = 0; i < data.operatingImageList.length; i++) {
          let url = data.operatingImageList[i];
          if (imagePathArr.length === this.maxImageCount) {
            break;
          }
          if (url === "") {
            continue;
          }
          if (maxSizeReportImageList.includes(url)) {
            imagePathArr.push({
              url: domainURL(url),
            });
          } else {
            imagePathArr.push({
              url: domainURL(url) + "?x-oss-process=image/auto-orient,1",
            });
          }
        }
        this.pageContent[cidx].pageContent.signInLogListImage = imagePathArr;
        this.signInLogListImage = imagePathArr;
      }
      // 服务方
      this.fixedFieldObject.serviceProvider = data.tenantName;
      this.fixedFieldObject.approveNumber = data.realCommentCount || 0;
      this.fixedFieldObject.commentNumber = data.realLikeCount || 0;
      this.activityResultObject.allWriteOptionNum = data.userNum || 0;
      // 初始化执行时间
      this.initTime();
      if (data.taskUserVoList instanceof Object) {
        this.taskUserVoList = data.taskUserVoList;
        this.$nextTick(() => {
          this.taskUserVoListUpdateCount += 1;
        });
      } else {
        this.updateSuccess();
      }
    },
  },
  computed: {
    userActivityPersonalReportPageType() {
      if (this.businessType === 18) {
        return 16;
      } else if (this.businessType === 19) {
        return 17;
      }
    },
    thumbTitle() {
      if (this.businessType === 18) {
        return "关于小葫芦平台的线上用户活动项目";
      } else if (this.businessType === 19) {
        return "关于小葫芦平台的线下用户活动项目";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../css/accurate-report.scss";
.activity-image-box {
  .activity-flex1 {
    min-width: 50%;
    justify-content: center;
  }
  .activity-image-b {
    width: 269px;
    height: 480px;
  }
}
</style>

<style lang="scss">
.el-table.ue-table {
  background: #edf3ff;
  th,
  td {
    background: #edf3ff;
  }
}
</style>