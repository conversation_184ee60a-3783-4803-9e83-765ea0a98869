<template>
  <el-drawer
    :title="title"
    :visible.sync="addShow"
    :size="size"
    :before-close="handleClose"
    custom-class="custom-class"
    :append-to-body="true"
    :modal-append-to-body="false"
  >
    <div class="drawer-body">
      <slot></slot>
    </div>
  </el-drawer>
</template>

<script>


export default {
  components: {

  },
  props: {
    title: {
      type: String,
      default: null
    },
    show: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: function () {
        return {}
      }
    },
    size: {
      type: [String, Number],
      default: function () {
        return '40%'
      }
    },
    closeTipsStr: {
      type: [String],
      default: function () {
        return null
      }
    },
    closeTips: {
      type: <PERSON><PERSON><PERSON>,
      default: function () {
        return false

      }
    },
    direction: {
      type: String,
      default: function () {
        return 'rtl'
      }
    }
  },
  data() {
    return {
      addShow: false
    }
  },
  watch: {
    show(n) {
      this.addShow = n
    }
  },
  mounted() {

  },
  methods: {
    handleClose(done) {
      if (this.closeTipsStr || this.closeTips) {
        const that = this
        this.$confirm(this.closeTipsStr || '输入数据将不会保存，确定关闭？').then(_ => {
          that.close()
        })

      } else {
        this.close()
      }

    },
    close() {
      this.$emit('update:show', false)
    }

  }

}
</script>
<style scoped lang="scss">
.drawer-body {
  padding: 20px;
}
</style>
<style>
.custom-class {
  overflow-y: scroll !important;
}
</style>
