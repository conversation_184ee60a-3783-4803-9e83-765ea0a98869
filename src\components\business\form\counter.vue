<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">
    <el-input-number
      v-model="form.data.counter"
      :min="config.min"
      :max="config.max"
      :class="childClass"
      style=""
      @change="handleChange"/>
  </el-form-item>
</template>

<script>
export default {
  name: 'Counter',
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '计数器',
          name: 'counter',
          min: 0,
          max: 100,
          rules: [
            { required: true, message: '请输入计数器', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: String,
      required: true
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          counter: ''
        }
      }
    }
  },

  watch: {
    data: {
      handler(val) {
        this.form.data.counter = Number(val)
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.counter.toString())
      },
      deep: true
    }
  },
  methods: {
    /**
     * input框change事件
     * @param value
     */
    handleChange(value) {
      this.form.data.counter = Number(value)
    }
  }
}
</script>

<style lang="scss">

</style>
