/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * 在线会议-聊天室
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/meetingchat/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/meetingchat/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/meetingchat/insert?nickName=${(data && data.nickName) ? data.nickName : ''}`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/meetingchat/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/meetingchat/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/meetingchat/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/meetingchat/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.postJson(`${prefix}/meetingchat/updateContent`, data)
}

// 导入聊天数据excel
export const uploadexcel = `${env.ctx + prefix}/meetingchat/uploadexcel`

// 取消定时执行评论 
export function cannelMettingChat(data) {
  return requestV1.postForm(`${prefix}/meetingchat/cancel`, data);
}

// 批量操作上下架 
export function onShelfBatch(data) {
  return requestV1.postJson(`${prefix}/meetingchat/onShelf/batch`, data);
}

// 药品说明书-聊天记录
export function meetingchatQueryMerchantsPage(data) {
  return requestV1.postJson(`${prefix}/meetingchat/query/merchants/page`, data);
}

export function meetingchatExport(data, fileName = '聊天记录.xlsx'){
  return requestV1.download(`${prefix}/meetingchat/export`, data, fileName, 'post',true,{},true)
}