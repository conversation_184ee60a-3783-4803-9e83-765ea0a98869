<template slot-scope="scope">
  <div class="voice-main">

    <audio controls style="height: 46px;">
      <source :src="imgServer+fileUrl">
    </audio>
  </div>
</template>

<script>
import {imgServer} from '@/api/config'

export default {
  name: 'ShowVoiceList',
  props: {
    fileUrl: String,

  },
  data() {
    return {
      imgServer: imgServer,
      title: '',
      srcList: [],
      dialogVisible: false,
      imageSrc: ''
    }
  },
  watch: {},
  mounted() {
    // this.loadData()
  },
  methods: {}
}
</script>

<style scoped>

</style>
