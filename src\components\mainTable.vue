<template>
  <div>
    <div class="content tableContent">
      <div v-if="!noSearch" ref="search" class="search">

        <div v-show="showInstall" class="main">
          <span>安装单位</span>

          <el-select size="mini" v-model="search.installId" multiple filterable placeholder="请选择" class="select" clearable>
            <el-option
              v-for="item in installList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
        <div v-show="showOperator" class="main">
          <span>经销商</span>
          <el-select size="mini" v-model="search.operatorId " filterable placeholder="请选择" class="select" clearable>
            <el-option
              v-for="item in operatorList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>

        </div>
        <div v-show="showArea" class="main">
          <span>地区选择</span>
          <addressSelect
            :area="search.area"
            :style-str="{width:'60%'}"
            @change="addressHandleChange"
          />
        </div>
        <div v-show="showClientId" class="main">
          <span>设备编号</span>
          <el-input size="mini" v-model="search.clientId" class="input" placeholder="请输入设备编号" clearable />
        </div>
        <slot name="search" />
        <!-- class="main" -->
        <div class="main" style="min-width: 10px;width: auto;">
          <el-button v-if="!notSearchButton" type="primary" size="mini"  style="margin-left: 30px" @click="initData('search')">查询
          </el-button>
          <slot name="buttons" />
        </div>
      </div>
      <slot name="tableBeforeText" />
      <div class="table">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          border
          class="lvTable"
          :style="{width:tableWidth}"
          @selection-change="handleSelectionChange"
        >
        <!-- :max-height="tableHeight" -->
          <slot />
        </el-table>
      </div>
    </div>
    <div class="page">
      <pagination
        :current="data.current"
        :size="data.size"
        :total="data.total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

  </div>
</template>

<script>
import { getTableHeight } from '@/utils/index'
import pagination from '@/components/MyPagination'
import addressSelect from '@/components/addressSelect'
import {queryList} from '@/api/operator'
import {listByInstallType} from '@/api/installUnit'
import {
  CodeToText
} from 'element-china-area-data'

export default {
  name: 'MainTable',
  components: { pagination, addressSelect },
  props: {
    // 默认参数
    defaultParams:{
      type:Object,
      default:function () {
        return {}
      }
    },
    tableUrl: {
      type: String,
      default: ''
    },
    tableWidth: {
      type: String,
      default: '100%'
    },
    search: {
      type: Object,
      default: function() {
        return {}
      }
    },
    showInstall: {
      type: Boolean,
      default: false
    },
    notSearchButton: {
      type: Boolean,
      default: false
    },
    showOperator: {
      type: Boolean,
      default: false
    },
    noLoad:{
      type: Boolean,
      default: false
    },
    showClientId: {
      type: Boolean,
      default: false
    },
    showArea: {
      type: Boolean,
      default: false
    },
    noSearch: {
      type: Boolean,
      default: false
    },
    show10Size: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      installList: [],
      operatorList: [],
      tableHeight: null,
      tableData: [],
      searchHeight: 0,
      data: {
        total: 0,
        size: 20,
        page: 1
      }
    }
  },
  watch: {},
  mounted() {
    const search = this.$refs.search
    const searchHeight = search && search.offsetHeight < 400 ? search.offsetHeight : 0

    this.searchHeight = searchHeight
    this.tableHeight = getTableHeight(searchHeight)
    console.log('searchHeight ', this.searchHeight)
    if (!this.noLoad) {
      this.initData()
    }
    this.$emit('getSearchHeight', this.searchHeight)
    // 经销商
    if (this.showOperator) {
      this.getOperationList()
    }
    // 安装单位
    if (this.showInstall) {
      this.getInstallList()
    }
    if (this.show10Size) {
      this.$set(this.data, 'size', 10)
    }
  },
  methods: {
    // 多选的触发
    handleSelectionChange(val) {
      this.$emit('handleSelectionChange', val)
    },
    addressHandleChange(value) {
      let arr = []
      value.map((item) => {
        arr.push(CodeToText[item])
      })
      arr = arr.join('')
      this.addressArr = arr
      // console.log('===', this.addressArr)
      this.search.province = value[0]
      this.search.city = value[1]
      this.search.district = value[2]
    },
    reload(page = null) {
      if (page) {
        this.data.current = 1
      }
      this.initData();
      console.log("initData ")
    },
    handleSizeChange(val) {
      this.data.size = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.data.current = val
      this.initData()
    },
    getOperationList() {
      const data = {}
      queryList(data).then((res) => {
        this.operatorList = res.data
      }).catch((err) => {

      })
    },
    initData(search) {
      if (search) {
        this.data.current = 1
      }
      this.loading(true)
      let data = {
        ...this.data,
      }
      // data = this.data
      if (this.search.installId != null && this.search.installId.length == 0) {
        delete this.search.installId
      }
      // data.condition = this.search
      data.condition = {
        ...this.search,
        ...this.defaultParams

      }
      // 触发了查询请求
      this.$emit('trigger-search',{
        condition:data.condition
      })
      console.log('initData')
      this.$requestV1.postJson(this.tableUrl, data).then((res) => {
        this.tableData = res.data.records
        const temp = res.data
        this.data.total = temp.total
        this.data.size = temp.size
        this.data.pages = temp.pages
        this.loading(false)
        // console.log(this.data)
        this.$emit('clickSearch', temp) // 把返回的数据传出去，也可以知道点击了查询按钮
      }).catch((err) => {
        this.loading(false)
      })

    },
    loading(flag) {
      this.tableLoading = flag
    },
    /**
     * 获取安装单位
     * */
    getInstallList() {
      const data = {}
      listByInstallType(data).then((res) => {
        this.installList = res.data
      }).catch((err) => {

      })
    }
  }
}
</script>

<style scoped lang="scss">

.content {
  min-height: 0px;

  .search {
    min-height: 2px;
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;

    .main {

      display: flex;
      align-items: center;
      width: 321px;
      margin-right: 1px;
      margin-bottom: 10px;

      span {
        min-width: 125px;
        line-height: 40px;
        text-align: center;
      }
    }
  }

}

.input {
  width: 256px;
  margin-right: 20px;
}

.select {
  width: 256px;
}

.table {
  margin-left: 10px;
}

.page {
  margin-left: 20px;
}
</style>
