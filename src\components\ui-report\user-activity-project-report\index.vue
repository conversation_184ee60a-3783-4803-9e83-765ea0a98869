<template>
  <div class="pageContent" v-loading="pageLoading">
    <template v-for="(page, index) in pageContent">
      <div
        class="everyPage"
        :key="index"
        :style="{
          width: pageSize.width + 'px',
          height: pageSize.height + 'px',
        }"
        v-if="!page.authHeight"
      >
        <template v-if="page.type === 'thumb'">
          <thumb
            :pageObject="page.pageObject"
            :updatecount="thumbUpdateCount"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
          >
          </thumb>
        </template>
        <template v-else-if="page.type === 'directory'">
          <directory
            :pageObject="page.pageObject"
            :updatecount="directoryUpdateCount"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
          >
          </directory>
        </template>
      </div>
      <template v-else-if="page.authHeight">
        <template v-if="page.type === 'information'">
          <information
            :key="index"
            :updatecount="informationUpdateCount"
            :pageObject="page.pageObject"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
          ></information>
        </template>
        <template
          v-else-if="
            ['promotionDetail', 'userActivityProjectReportOne','noteCircleDetail'].includes(
              page.type
            )
          "
        >
          <pullNewDetail
            :key="index"
            :updatecount="pullNewDetailUpdateCount"
            :pageObject="page.pageObject"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
          ></pullNewDetail>
        </template>
        <template v-else-if="['noteCircleAnalysis','promotionPersonnelLocation'].includes(page.type)">
          <promotionPersonnelLocation
            :key="index"
            :updatecount="promotionPersonnelLocationUpdateCount"
            :pageObject="page.pageObject"
            :renderUpdateCount="promotionPersonnelLocationRenderUpdateCount"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
          ></promotionPersonnelLocation>
        </template>
        <template v-else-if="page.type === 'groundPushOnSitePictures'">
           <groundPushOnSitePicturesTwo
            :key="index"
            :updatecount="groundPushOnSitePicturesUpdateCount"
            :pageObject="page.pageObject"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
            :reportStyle='reportStyle'
            v-if="pagetype == 18"
          ></groundPushOnSitePicturesTwo>
          <groundPushOnSitePictures
            :key="index"
            :updatecount="groundPushOnSitePicturesUpdateCount"
            :pageObject="page.pageObject"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
            v-else-if="pagetype == 19"
          ></groundPushOnSitePictures>
        </template>
        <template v-else-if="page.type === 'executiveSummary'">
          <executiveSummary
            :key="index"
            :updatecount="executiveSummaryUpdateCount"
            :pageObject="page.pageObject"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
          ></executiveSummary>
        </template>
        <invitationRegistration
          :key="index"
          v-else-if="page.type === 'pullNewDetail'"
          :updatecount="pullNewDetailsUpdateCount"
          :pageObject="page.pageObject"
          renderObject="log"
          :inType="'user-activity-project-report'"
          @success="updateSuccess"
          @updatePageing='updatePageing'
          :pageNumObject='pageNumObject'
        ></invitationRegistration>
        <invitationRegistration
          :key="index"
          v-else-if="page.type === 'kpiPostLikeDetails'"
          :updatecount="kpiPostLikeDetailsUpdateCount"
          :pageObject="page.pageObject"
          renderObject="dianzan"
          :inType="'user-activity-project-report'"
          @success="updateSuccess"
          @updatePageing='updatePageing'
          :pageNumObject='pageNumObject'
        ></invitationRegistration>
        <invitationRegistration
          :key="index"
          v-else-if="page.type === 'kpiPostCommentDetails'"
          :updatecount="kpiPostCommentDetailsUpdateCount"
          :pageObject="page.pageObject"
          renderObject="common"
          :inType="'user-activity-project-report'"
          @success="updateSuccess"
          @updatePageing='updatePageing'
          :pageNumObject='pageNumObject'
        ></invitationRegistration>
      </template>
    </template>
  </div>
</template>

<script>
import thumb from "@/components/ui-report/earth-push-project-report/components/styleOne/thumb.vue";
import directory from "@/components/ui-report/earth-push-project-report/components/styleOne/directory.vue";
import information from "@/components/ui-report/earth-push-project-report/components/styleOne/information.vue";
import pullNewDetail from "@/components/ui-report/earth-push-project-report/components/styleOne/pull-new-detail.vue";
import promotionPersonnelLocation from "@/components/ui-report/earth-push-project-report/components/styleOne/promotion-personnel-location.vue";
import groundPushOnSitePictures from "@/components/ui-report/earth-push-project-report/components/styleOne/ground-push-on-site-pictures.vue";
import invitationRegistration from "@/components/ui-report/components/invitation-registration.vue";
import executiveSummary from "@/components/ui-report/earth-push-project-report/components/styleOne/executive-summary.vue";
import groundPushOnSitePicturesTwo from "@/components/ui-report/earth-push-project-report/components/styleTwo/ground-push-on-site-pictures.vue";

import commonMixin from "@/components/ui-report/mixins/common.js";
import { getQueryStr, domainURL } from "@/utils/index";
import { todotasksGetReportDetail, useractivityinvitelogCircleStaticstics } from "@/api/dmDemand";
import { imgServer } from "@/api/config";
import { loadScript } from "@/utils/index";
import { maxSizeReportImageList, noVerticalImageToVisible } from "@/utils/report-error-image.js";
import { getIdentity } from '@/utils/auth'

export default {
  props: {
    taskMonth: {
      type: [Number, String],
      default: "",
    },
    pagetype: {
      type: [Number, String],
      default: "10",
    },
    reportStyle: {
      type: [Number, String],
      default: 1,
    },
    businessType: {
      type: Number,
      default: 18,
    },
  },
  components: {
    thumb,
    directory,
    information,
    pullNewDetail,
    promotionPersonnelLocation,
    groundPushOnSitePictures,
    invitationRegistration,
    executiveSummary,
    groundPushOnSitePicturesTwo
  },
  mixins: [commonMixin],
  provide() {
    return {
      pageSize: this.pageSize,
      domainUrl: this.domainUrl,
      pagetype: Number(this.pagetype),
      pageTopBgUrl: "",
      reportStyle: this.reportStyle,
      // pagetype: ,
    };
  },
  data() {
    return {
      tenantId: '-1',
      landscapeToVerticalList: [],
      executiveSummaryUpdateCount: 0,
      pullNewDetailsUpdateCount: 0,
      kpiPostLikeDetailsUpdateCount: 0,
      kpiPostCommentDetailsUpdateCount: 0,
      userImageCount: 4,
      maxImageCount: 40,
      moduleReportType: 1,
      imageStyleOne: false,
      domainUrl: imgServer + "static/",
      domainUrlV2: "https://file.greenboniot.cn/",
      pageContent: [
        {
          text: "封面",
          type: "thumb",
          pageObject: {},
        },
        {
          text: "目录",
          type: "directory",
          pageObject: {},
        },
        {
          text: "基本信息",
          type: "information",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "拉新明细",
          type: "userActivityProjectReportOne",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "推广人员布置",
          type: "promotionPersonnelLocation",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "推广明细",
          type: "promotionDetail",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "线上用户活动部分截图展示",
          type: "groundPushOnSitePictures",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "小葫芦平台邀请注册明细",
          type: "pullNewDetail",
          authHeight: true,
        },
        {
          text: '圈子分布',
          type: "noteCircleAnalysis",
          pageObject: {},
          authHeight: true,
          subTitle: '圈子分布'
        },
        {
          text: '圈子详情',
          type: "noteCircleDetail",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "KPI 帖子点赞明细",
          type: "kpiPostLikeDetails",
          authHeight: true,
        },
        {
          text: "KPI 帖子评论明细",
          type: "kpiPostCommentDetails",
          authHeight: true,
        },
        {
          text: "执行总结",
          type: "executiveSummary",
          authHeight: true,
          pageContent: {},
        },
      ],
      targetCount: 14,
      tenantName: "广州绿葆网络发展有限公司",
      thumbUpdateCount: 0,
      directoryUpdateCount: 0,
      informationUpdateCount: 0,
      pullNewDetailUpdateCount: 0,
      promotionPersonnelLocationUpdateCount: 0,
      promotionPersonnelLocationRenderUpdateCount: 0,
      groundPushOnSitePicturesUpdateCount: 0,
      fixedFieldObject: {
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己
        productTaskNumber: 0, // 地推团队数量
        startTime: "",
        endTime: "",
      },
      levelParams: {},
    };
  },
  watch: {
    updatecount(n) {
      this.pageLoading = true;
      this.initEchart();
    },
  },
  methods: {
    async initEchart() {
      await loadScript(`${this.domainUrlV2}cdnjs/echarts.js`);
      await loadScript(`${this.domainUrlV2}cdnjs/echarts-gl.js`);
      this.initMethod();
    },
    async initMethod() {
      const saToken = getQueryStr("satoken");
      if (saToken) {
        this.saTime = 300;
      } else {
        // 客户端预览
        this.saTime = 2000;
      }
      this.exportProjectAccurateId();
    },
    getDaysInCurrentMonth() {
      let str = this.taskMonth;
      let arr = str.split("-");
      let year = arr[0];
      let month = arr[1];
      // 设置日期为下个月的第0天，这样它就会回滚到当前月的最后一天
      var nextMonthFirstDay = new Date(year, month, 1);
      var daysInMonth = new Date(nextMonthFirstDay - 1).getDate();
      return daysInMonth;
    },
    initTime() {
      let daysInMonth = this.getDaysInCurrentMonth();
      let startTime = this.taskMonth + "-" + "01";
      let endTime = this.taskMonth + "-" + daysInMonth;
      this.fixedFieldObject.startTime = startTime;
      this.fixedFieldObject.endTime = endTime;
    },
    async exportProjectAccurateId() {
      const res = await todotasksGetReportDetail(
        {
          taskMonth: this.taskMonth,
          taskType: this.businessType === 18 ? 30 : 32,
        },
        {
          "no-time-manage": 1,
        }
      );
      const data = res.data;

      if(Array.isArray(data.operatingList)) {
        let operatingList = data.operatingList;
        if(operatingList.length > 0) {
          let vitem = operatingList[0]
          this.tenantId = vitem.tenantId;
          if(noVerticalImageToVisible[this.tenantId] && Array.isArray(noVerticalImageToVisible[this.tenantId])) {
            this.landscapeToVerticalList = noVerticalImageToVisible[this.tenantId];
          } else {
            this.landscapeToVerticalList = [];
          } 
        }
      }
      console.log('operatingList======',data.operatingList,this.tenantId)
      let taskItemIdList = data.taskItemIdList || [];
      let taskIdList = data.taskIdList || []
      taskIdList = taskIdList.length !== 0 ? taskIdList : [1]
      const cRes = await useractivityinvitelogCircleStaticstics(taskIdList,{
        "no-time-manage": 1,
      })
      const cData = cRes.data;
      this.initTime();
      console.log("fixedFieldObject", this.fixedFieldObject);
      this.levelParams = {
        randomValueDesc: 1,
        startCreateTime: this.fixedFieldObject.startTime + " 00:00:00",
        endCreateTime: this.fixedFieldObject.endTime + " 23:59:59",
        taskIdList: taskItemIdList.length !== 0 ? taskItemIdList : [1],
      };
      let commonObject = {
        projectParty: this.fixedFieldObject.projectParty,
        serviceProvider: data.tenantName,
        approveNumber: data.realCommentCount || 0,
        commentNumber: data.realLikeCount || 0,
        allWriteOptionNum: data.userNum || 0,
        startTime: this.fixedFieldObject.startTime,
        endTime: this.fixedFieldObject.endTime,
        productTaskNumber: data.executeUserNum || 0,
      };

      let taskUserVoList = [];
      if (data.taskUserVoList instanceof Object) {
        taskUserVoList = data.taskUserVoList;
      }
      let thumbObject = {
        ...commonObject,
      };
      let directoryObject = {
        ...commonObject,
      };
      let informationObject = {
        ...commonObject,
      };
      let pullNewDetailObject = {
        ...commonObject,
        taskUserVoList,
      };
      let promotionPersonnelLocationObject = {
        ...commonObject,
      };
      let groundPushOnSitePicturesObject = {};
      let kpiPostLikeDetailsObject = {
        levelParams: this.levelParams,
      };
      let executiveSummaryObject = {
        ...commonObject
      }
      if (data.taskUserRegion instanceof Object) {
        let taskUserRegion = data.taskUserRegion.map((item) => {
          item.selectOptionProportion += "%";
          return item;
        });
        promotionPersonnelLocationObject.taskUserRegion = taskUserRegion;
        promotionPersonnelLocationObject.isRender = true;
      }
      if (data.operatingImageList instanceof Object) {
        let signInLogListImage = [];
        for (let i = 0; i < data.operatingImageList.length; i++) {
          let url = data.operatingImageList[i];
          if (signInLogListImage.length === this.maxImageCount) {
            break;
          }
          if (url === "") {
            continue;
          }
          let landscapeToVertical = false;
          if(this.landscapeToVerticalList.includes(url)) {
            landscapeToVertical = true;
          }
          if (maxSizeReportImageList.includes(url)) {
            signInLogListImage.push({
              url: domainURL(url),
              landscapeToVertical
            });
          } else {
            signInLogListImage.push({
              url: domainURL(url) + "?x-oss-process=image/auto-orient,1",
              landscapeToVertical
            });
          }
        }
        groundPushOnSitePicturesObject.signInLogListImage = signInLogListImage;
      }
      let taskCircleVoList = Array.isArray(cData) ? cData : []
      let noteCircleAnalysis = {
        taskUserRegion: taskCircleVoList,
        isRender: taskCircleVoList.length !== 0,
        uuidKey: 'noteCircleAnalysis'
      };
      this.pageContent.forEach((item) => {
        switch (item.type) {
          case "thumb":
            thumbObject.parentUuid = item.type;
            item.pageObject = thumbObject;
            break;
          case "directory":
            directoryObject.parentUuid = item.type;
            item.pageObject = directoryObject;
            break;
          case "information":
            informationObject.parentUuid = item.type;
            item.pageObject = informationObject;
            break;
          case "userActivityProjectReportOne":
            pullNewDetailObject.parentUuid = item.type;
            item.pageObject = pullNewDetailObject;
            break;
          case "promotionPersonnelLocation":
            item.pageObject = {
              ...promotionPersonnelLocationObject,
              echartType: 2,
              isClassOne: true,
              parentUuid: item.type
            };
            break;
          case "promotionDetail":
            item.pageObject = {
              ...promotionPersonnelLocationObject,
              parentUuid: item.type
            };
            break;
          case "groundPushOnSitePictures":
            item.pageObject = {
              ...groundPushOnSitePicturesObject,
              parentUuid: item.type
            };
            break;
          case "pullNewDetail":
            item.pageObject = {
              ...kpiPostLikeDetailsObject,
              uuidKey: "pullNewDetail",
              parentUuid: item.type
            };
            break;
          case "kpiPostLikeDetails":
            item.pageObject = {
              ...kpiPostLikeDetailsObject,
              uuidKey: "kpiPostLikeDetails",
              parentUuid: item.type
            };
            break;
          case "kpiPostCommentDetails":
            item.pageObject = {
              ...kpiPostLikeDetailsObject,
              uuidKey: "kpiPostCommentDetails",
              parentUuid: item.type
            };
            break;
          case 'executiveSummary':
            executiveSummaryObject.parentUuid = item.type;
            item.pageObject = executiveSummaryObject
            break;
          case "noteCircleAnalysis":
            item.pageObject = {
              subTitle: '用户活动圈子分布',
              belong: 'user-activity-project-report',
              echartType: 20,
              isClassOne: true,
              ...noteCircleAnalysis,
              parentUuid: item.type
            };
            break;
          case "noteCircleDetail":
            item.pageObject = {
              ...noteCircleAnalysis,
              belong: 'user-activity-project-report',
              parentUuid: item.type
            };
            break;
        }
      });
      await this.$nextTick();

      // this.fixedFieldObject.productTaskNumber = data.executeUserNum || 0;

      this.thumbUpdateCount += 1;
      this.directoryUpdateCount += 1;
      this.informationUpdateCount += 1;
      this.pullNewDetailUpdateCount += 1;
      this.promotionPersonnelLocationUpdateCount += 1;
      this.groundPushOnSitePicturesUpdateCount += 1;
      this.pullNewDetailsUpdateCount += 1;
      this.kpiPostLikeDetailsUpdateCount += 1;
      this.kpiPostCommentDetailsUpdateCount += 1;
      this.executiveSummaryUpdateCount += 1;
      this.updateSuccess();
    },
    pageInitMethod() {
      console.log("pageInitMethod========", this.initsuccesscount);
      return new Promise((resolve, reject) => {
        this.promotionPersonnelLocationRenderUpdateCount += 1;
        setTimeout(() => {
          resolve(true);
        }, 3000);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.everyPage {
  overflow: hidden;
  background: #fff;
}
</style>
<style lang="scss">
@import "../css/earth-push-project-report.scss";
</style>