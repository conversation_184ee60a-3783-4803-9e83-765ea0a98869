/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 * 引流号-企业号许可证订单
 */

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/wxlicenseorder/query/page`, data);
}

// 同步许可证
export function synWxLicenseOrder(data) {
  return requestV1.postForm(`${prefix}/wxlicenseorder/syn/wx/license/order`, data);
}

// 拉取许可证（同步数据）
export function pullWxLicenseOrder(data) {
  return requestV1.postForm(`${prefix}/wxlicenseorder/pull/wx/license/order`, data);
}

// 获取没有使用过的许可证
export function getNotUseLicense(data) {
  return requestV1.get(`${prefix}/wxlicenseorder/get/not/use/license`, data);
}

// 激活企业号
export function activateLicense(data) {
  return requestV1.postForm(`${prefix}/wxlicenseorder/activate/license`, data);
}

// 许可证继承
export function batchTransferLicense(data) {
  return requestV1.postForm(`${prefix}/wxlicenseorder/batch/transfer/license`, data);
}
