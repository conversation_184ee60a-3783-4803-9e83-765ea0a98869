/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

const prefix = '/dm/api/v1'

/**
 * 药品说明书--品牌方管理
 */

// 药品分页列表
export function getDrugQueryPage (data) {
  return requestV1.postJson(`${prefix}/brand/query/page`,data)
}

// 药品保存数据
export function drugInsert (data) {
  return requestV1.postJson(`${prefix}/brand/insert`,data)
}

// 药品主键单一查询
export function drugQueryOne (data) {
  return requestV1.get(`${prefix}/brand/query/one`,data)
}

// 药品更新数据
export function updateDrugData (data) {
  return requestV1.putJson(`${prefix}/brand/update`,data)
}

// 药品批量删除
export function BatchDeleteDrug (data) {
  return requestV1.deleteForm(`${prefix}/brand/delete/batch/${data.ids}`)
}

// 药品说明书--产品管理
// 产品分页列表
export function getProductQueryPage (data) {
  return requestV1.postJson(`${prefix}/eiproduct/query/page`,data)
}

// 产品列表所有数据
export function getProductQueryList (data) {
  return requestV1.get(`${prefix}/eiproduct/query/list`,data)
}

// 产品保存数据
export function ProductInsert (data) {
  return requestV1.postJson(`${prefix}/eiproduct/insert`,data)
}

// 产品主键单一查询
export function ProductQueryOne (data) {
  return requestV1.get(`${prefix}/eiproduct/query/one`,data)
}

// 产品更新数据
export function updateProductData (data) {
  return requestV1.putJson(`${prefix}/eiproduct/update`,data)
}

// 产品批量删除
export function BatchDeleteProduct (data) {
  return requestV1.deleteForm(`${prefix}/eiproduct/delete/batch/${data.ids}`)
}

// 产品-电子说明书-根据相对路径查询附件信息
export function attachmentQueryParam(data) {
  return requestV1.get('/basics/api/v1/attachment/query/param', data)
}

// 修改上下架
export function BatchUpdateShelfStatus (data) {
  return requestV1.postForm(`${prefix}/eiproduct/update/shelfStatus/batch`,data)
}

// 附近药店-分页列表
export function getPharmacyQueryPage (data) {
  return requestV1.postJson(`${prefix}/pharmacy/query/page`,data)
}

// 附近药店-保存数据
export function pharmacyInsert(data) {
  return requestV1.postJson(`${prefix}/pharmacy/insert`,data)
}

// 附近药店-主键单一查询
export function pharmacyQueryOne (data) {
  return requestV1.get(`${prefix}/pharmacy/query/one`,data)
}

// 附近药店-更新数据
export function updatePharmacyData (data) {
  return requestV1.putJson(`${prefix}/pharmacy/update`,data)
}

// 附近药店-批量删除
export function BatchDeletePharmacy (data) {
  return requestV1.deleteForm(`${prefix}/pharmacy/delete/batch/${data.ids}`)
}

// 附近药店-单一删除
export function DeleteOnePharmacy (data) {
  return requestV1.deleteForm(`${prefix}/pharmacy/delete/one/${data.id}`)
}

// 附近药店-药店绑定和解绑药品
export function getPharmacyQueryBind (data) {
  return requestV1.postForm(`${prefix}/pharmacy/bind/and/unbind`,data)
}

// 用药提醒-分页列表
export function getPharmacyRemindQueryPage (data) {
  return requestV1.postJson(`${prefix}/medicineremind/query/page`,data)
}

// 用药提醒-保存数据
export function pharmacyRemindInsert(data) {
  return requestV1.postJson(`${prefix}/medicineremind/insert`,data)
}

// 用药提醒-主键单一查询
export function pharmacyRemindQueryOne (data) {
  return requestV1.get(`${prefix}/medicineremind/query/one`,data)
}

// 用药提醒-更新数据
export function updatePharmacyRemindData (data) {
  return requestV1.putJson(`${prefix}/medicineremind/update`,data)
}

// 用药提醒-批量删除
export function BatchDeletePharmacyRemind (data) {
  return requestV1.deleteForm(`${prefix}/medicineremind/delete/batch/${data.ids}`)
}

// 用药提醒-单一删除
export function DeleteOnePharmacyRemind (data) {
  return requestV1.deleteForm(`${prefix}/medicineremind/delete/one/${data.id}`)
}

//中央用户与租户档案关系分页列表查询 绑定的员工数据 
export function queryEmployeePage(data) {
  return requestV1.postJson('/manage/api/user/queryList', data);
} 

//员工解绑定
export function bindingEmployee(data) {
  return requestV1.postForm(`${prefix}/brand/binding/user`, data);
} 

//新加的附近药店
export function pharmacyMerchantsPage(data) {
  return requestV1.postJson(`${prefix}/pharmacy/merchants/page`, data);
} 

// 用药说明书商户端-新增的附近药店-药店绑定和解绑药品
export function getPharmacyBindAndUnbind (data) {
  return requestV1.postForm(`${prefix}/pharmacy/bind/and/unbind/merchants`,data)
}

// 完整说明书
// 完整说明书分页列表
export function getfullSpecificationQueryPage (data) {
  return requestV1.postJson(`${prefix}/fullinstructions/query/page`,data)
}
// 完整说明书保存数据
export function fullSpecificationInsert (data) {
  return requestV1.postJson(`${prefix}/fullinstructions/insert`,data)
}

// 完整说明书主键单一查询
export function fullSpecificationQueryOne (data) {
  return requestV1.get(`${prefix}/fullinstructions/query/one`,data)
}

// 完整说明书更新数据
export function updateFullSpecificationData (data) {
  return requestV1.putJson(`${prefix}/fullinstructions/update`,data)
}

// 完整说明书批量删除
export function BatchDeletefullSpecification (data) {
  return requestV1.deleteForm(`${prefix}/fullinstructions/delete/batch/${data.ids}`)
}

// 完整说明书根据主键id指定删除
export function fullSpecificationDeleteOne (data) {
  return requestV1.deleteForm(`${prefix}/fullinstructions/delete/one/${data.id}`)
}

// 用药说明书商户端-数据统计-访问分析
export function pharmacyVisitAnalyse (data) {
  return requestV1.postJson(`${prefix}/minichannellinklog/access/analysis`,data)
}

// 批量导入
export const importBatch = env.ctx + '/dm/api/v1/pharmacy/pharmacy/import'

// 获取企业菜单Logo
export function getBrandQueryInitLogo (data) {
  return requestV1.get(`${prefix}/brand/query/init/logo`,data)
}

// 用药指南配置入口-新增
export function productmedicationguideInsert (data) {
  return requestV1.postJson(`${prefix}/productmedicationguide/insert`,data)
}

// 用药指南配置入口-查询单一
export function productmedicationguideQueryOne (data) {
  return requestV1.get(`${prefix}/productmedicationguide/query/one/productId`,data)
}

// 企业产品动态配置 修改显示状态
export function eiproductUpdateBrandViewBatch (data) {
  return requestV1.postForm(`${prefix}/eiproduct/update/brandView/batch`,data)
}

// 完整说明书添加拉取音频
export function fullinstructionsNoAudioSync (data) {
  return requestV1.postJson(`${prefix}/fullinstructions/noAudio/sync`,data)
}

// 用药说明书管理-用户分享说明书统计
export function usersharestatisticsQueryPage (data) {
  return requestV1.postJson(`${prefix}/usersharestatistics/query/page`,data)
}
export function usersharerecordQueryPage (data) {
  return requestV1.postJson(`${prefix}/usersharerecord/query/page`,data)
}

// 用药说明书管理-用户分享说明书统计-保存数据
export function usersharestatisticsInsert (data) {
  return requestV1.postJson(`${prefix}/usersharestatistics/insert`,data)
}
// 用药说明书管理-用户分享说明书统计-保存数据
export function usersharestatisticsListStatistics (data) {
  return requestV1.postJson(`${prefix}/usersharestatistics/list/statistics`,data)
}
// 用药说明书管理-用户分享说明书统计-查看访客
export function usersharestatisticsListVisitor (data) {
  return requestV1.postJson(`${prefix}/usersharestatistics/list/visitor`,data)
}
// 用药说明书管理-完整说明书-查看阿里巴巴音色参数
export function instructionsalibabavoiesQueryList(data) {
  return requestV1.get(`${prefix}/instructionsalibabavoices/query/match/list`,data)
}
// 用药说明书管理-用户分享说明书统计-查看访客新的UV
export function usersharestatisticsListVisitorUv (data) {
  return requestV1.postJson(`${prefix}/usersharestatistics/list/visitor/uv`,data)
}
// 用药说明书管理-用户分享说明书统计-查看访客新的PV
export function usersharestatisticsListVisitorPv (data) {
  return requestV1.postJson(`${prefix}/usersharestatistics/list/visitor/pv`,data)
}
