/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 社区业务-变更审核记录
 */

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/businessauditlog/query/page`, data);
}

// 帖子审核分页
export function queryPostmessagePage (data) {
    return requestV1.postJson(`${prefix}/businessauditlog/query/postmessage/page`, data)
}

// 帖子审核
export function postmessageAudit (data) {
    return requestV1.putJson(`${prefix}/businessauditlog/postmessage/audit`, data)
}

// 一键批量帖子审核 新加的
export function postmessageBatchPostmessageAudit (data) {
    return requestV1.putJson(`${prefix}/businessauditlog/batchPostmessageAudit`, data)
}

// 审核日志
export function postmessageAuditLog (data) {
    // /v1/businessauditlog/query/page
    return requestV1.postJson(`${prefix}/businessauditlog/query/page`, data)
}

// 审核日志
export function businessmediacheckasynclogQueryPage (data) {
    // /v1/businessauditlog/query/page
    return requestV1.postJson(`/manage/api/v1/businessmediacheckasynclog/query/page`, data)
}
