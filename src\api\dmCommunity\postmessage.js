/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'
const prefix2 = '/dm/api/v2'

/**
 * 帖子管理
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/postmessage/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/postmessage/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/postmessage/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/postmessage/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    if(data.condition){
        data.condition.isDeletePutawayStatus = 1
    }
    return requestV1.postJson(`${prefix}/postmessage/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/postmessage/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/postmessage/update`, data)
}

// 修改状态
export function updateOpenStatus (data) {
    return requestV1.postForm(`${prefix}/postmessage/update/open/status`, data)
}

// 批量邀请评论
export function inviteBatch(data) {
  return requestV1.postJson(`${prefix}/postmessage/invite/batch`, data)
}


// 帖子一键下架 /api/v1/postmessage/batchOff
export function postmessageBatchOff(data) {
  return requestV1.putJson(`${prefix}/postmessage/batchOff`, data)
}


// 获取帖子关联项目
export function getNotAuthList(data) {
  return requestV1.get(`${prefix}/demand/query/notAuth/list`, data)
}

// 克隆操作
export function copyPostmessage(data) {
  return requestV1.postForm(`${prefix}/postmessage/clone`, data)
}


// 一键关联项目
export function postmessageBatchBinding(data) {
  return requestV1.postForm(`${prefix}/postmessage/batch/binding`, data)
}

// 一键关联产品
export function postmessageBatchBindingProductIds(data) {
  return requestV1.postForm(`${prefix}/postmessage/batch/binding/productIds`, data)
}

// 批量删除-真删除
export function deleteBatchV2 (data) {
  return requestV1.deleteForm(`${prefix2}/postmessage/delete/batch/${data.ids}`)
}

//用药说明书商户端-新加的查询所有用户
export function userOostmessageQueryList(data) {
  return requestV1.postJson(prefix + '/postmessage/merchants/creator', data);
}

// 帖子专题 - 帖子专题分页
export function postspecialQueryPage(data) {
  return requestV1.postJson(`${prefix}/postspecial/query/page`,data)
}
// 帖子专题 - 帖子专题根据主键单一查询
export function postspecialQueryOne (data) {
  return requestV1.get(`${prefix}/postspecial/query/one`,data)
}

// 帖子专题 - 新增帖子专题
export function postspecialInsert (data) {
  return requestV1.postJson(`${prefix}/postspecial/insert`,data)
}

// 帖子专题 - 编辑帖子专题
export function postspecialUpdate (data) {
  return requestV1.putJson(`${prefix}/postspecial/update`,data)
}

// 帖子专题 - 根据id删除帖子专题
export function postspecialDeleteOne (data) {
  return requestV1.deleteForm(`${prefix}/postspecial/delete/one/${data.id}`)
}

// 帖子专题 - 批量删除帖子专题
export function postspecialDeleteBatch (data) {
  return requestV1.deleteForm(`${prefix}/postspecial/delete/batch/${data.ids}`)
}

// 帖子完读率 -列表
export function contentfinishrateQueryPostAvgPage (data) {
  return requestV1.postJson(`${prefix}/contentfinishrate/query/post/avg/page`,data)
}

// 帖子完读率流水 - 列表
export function contentfinishrateQueryPage (data) {
  return requestV1.postJson(`${prefix}/contentfinishrate/query/page`,data)
}

// 帖子列表-基于未审核，退回的数量
export function postmessageGetProcessStatus (data) {
  return requestV1.postJson(`${prefix}/postmessage/get/process/status/statistic`,data)
}

// 帖子后端导出 
export function postmessageDataExport (data, fileName='帖子列表.xlsx') {
  return requestV1.download(`/dm/api/v1/postmessage/data/export`,data,fileName,'post',true,true)
}

// 说明书商户端-发布列表
export function postmessageQueryMerchantsPublicPage (data) {
  return requestV1.postJson(`${prefix}/postmessage/query/merchants/public/page`,data)
}
