/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
// import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * 通用系统导出任务
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/exporttasklog/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/exporttasklog/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/exporttasklog/insert`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/exporttasklog/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/exporttasklog/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/exporttasklog/query/page`, data);
}

// 分页列表--------项目报告入口
export function queryPageV3(data) {
  return requestV1.postJson(`/dm/api/v2/exporttasklog/query/page`, data);
}


// 执行状态 
export function exporttasklogUpdateExecuteStatus(data) {
  return requestV1.postForm(`${prefix}/exporttasklog/update/execute/status`, data);
}

// 执行失败，重新下载 
export function exporttasklogAgainCreateReport(data) {
  return requestV1.postForm(`${prefix}/exporttasklog/again/create/report`, data);
}

// 分页列表
export function queryPageV2(data) {
  return requestV1.postJson(`${prefix}/exporttasklog/query/page/group/businessId`, data);
}

// 导出记录列表
export function exporttasklogExport(data, fileName = '个人报告导出列表记录.xlsx') {
  return requestV1.download(`${prefix}/exporttasklog/export`, data, fileName, 'post', true);
}
