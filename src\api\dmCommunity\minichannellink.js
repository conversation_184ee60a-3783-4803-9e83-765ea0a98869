/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 小程序渠道链管理-二维码配置
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/minichannellink/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/minichannellink/query/page`, data)
}
// 分页查询-陪诊
export function queryAccompanyPage (data) {
    return requestV1.postJson(`${prefix}/minichannellink/query/accompanyPage`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/minichannellink/update`, data)
}

// 根据主键单一查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/minichannellink/query/one`, data)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/minichannellink/delete/one/${data.id}`)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/minichannellink/query/list`, data)
}

// 根据多参数进行列表查询
export function minichannellinklogExport (data) {
    return requestV1.download(`${prefix}/minichannellinklog/export`, data, `渠道链访问流水统计.xlsx`, 'post',{
      'content-type': 'application/json; charset=utf-8'
    })
}