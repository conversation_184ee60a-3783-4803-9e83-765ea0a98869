/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 拜访管理-拜访记录
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/visitingplanobjectlist/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/visitingplanobjectlist/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/visitingplanobjectlist/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data,headers = {}) {
    return requestV1.get(`${prefix}/visitingplanobjectlist/query/list`, data,null,headers)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/visitingplanobjectlist/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/visitingplanobjectlist/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/visitingplanobjectlist/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/visitingplanobjectlist/update`, data)
}

// 获取客户详情
export function getCustomerDetail (data) {
    return requestV1.get(`${prefix}/visitingplanobjectlist/get/customer/detail`, data)
}

// 提交候补信息
export function commit (data) {
    return requestV1.putJson(`${prefix}/visitingplanobjectlist/commit`, data)
}

// 更新签到数据
export function updateSign (data) {
    return requestV1.putForm(`${prefix}/visitingplanobjectlist/update/sign`, data)
}

// 删除签到签出
export function clearSign (data) {
    return requestV1.putForm(`${prefix}/visitingplanobjectlist/clear/sign`, data)
}