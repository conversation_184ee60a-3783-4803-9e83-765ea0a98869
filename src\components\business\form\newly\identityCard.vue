<!--身份证校验组件-->
<template>
  <div class="psw-input">
    <el-form-item
      v-if="isForm"
      :label="config.label"
      :rules="rules"
      :class="itemClass"
      :prop="config.name">
      <slot name="left"/>
      <el-input
        :disabled="disabled"
        v-model="form.data.input"
        :class="childClass"
        :placeholder="config.placeholder"
        :show-password="pswShow"
        :maxlength="maxlength"
        :minlength="minlength"
        :prefix-icon="prefixIcon"
        :suffix-icon="suffixIcon	"
        type="text"
        style=""/>
      <slot name="right"/>
    </el-form-item>
  </div>
</template>

<script>
const validateIdcardCheck = (rule, value, callback) => {
  value = value.replace(/(^\s*)|(\s*)$|(\s{1})/g, '') // 去除首尾和中间空格
  const reg = /^[1-9][0-7]\d{4}((19\d{2}(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(19\d{2}(0[13578]|1[02])31)|(19\d{2}02(0[1-9]|1\d|2[0-8]))|(19([13579][26]|[2468][048]|0[48])0229))\d{3}(\d|X|x)?$/
  if (value === '') {
    callback(new Error('身份证号码不能为空'))
  } else if (!reg.test(value)) {
    callback(new Error('身份证号码格式不正确'))
  } else {
    callback()
  }
}
export default {
  props: {
    // 是否是form表单组件
    isForm: {
      type: Boolean,
      required: false,
      default: true
    },
    // 配置参数 obj
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '身份证号码',
          name: 'IDNum',
          placeholder: '请输入身份证号码',
          rules: [
            { required: true, message: '身份证号码不能为空', trigger: 'blur' },
            { validator: validateIdcardCheck, trigger: 'blur' }
          ]
        }
      }
    },
    // 是否显示切换密码图标
    pswShow: {
      type: Boolean,
      required: false,
      default: true
    },
    // 最大长度
    maxlength: {
      type: Number,
      required: false,
      default: 16
    },
    minlength: {
      type: Number,
      required: false,
      default: 6
    },
    // 输入框头部图标
    prefixIcon: {
      type: String,
      required: false,
      default: ''
    },
    // 输入框尾部图标
    suffixIcon: {
      type: String,
      required: false,
      default: ''
    },
    // 禁止输入
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    // 是否显示切换密码图标
    idx: {
      type: Number,
      required: false,
      default: 0
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    data: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          input: ''
        }
      }
    }
  },
  computed: {
    rules() {
      let rule = []
      if (this.config.rules.length === 0) {
        rule = [
          { required: true, message: '身份证号码不能为空', trigger: 'blur' },
          { validator: validateIdcardCheck, trigger: 'blur' }
        ]
      } else {
        rule = this.config.rules
      }
      return rule
    }
  },
  watch: {
    data: {
      handler(val) {
        this.form.data.input = val
      },
      deep: true
    },
    // config: {
    //   handler(value) {
    //   },
    //   deep: true
    // },
    // 监听到form的数据变化则把config.name和form.data.input和idx传到父组件
    form: {
      handler(val) {
        if (this.isForm) {
          this.$emit('updateForm', '' + this.config.name, val.data.input, this.idx)
        } else {
          this.$emit('updateForm', '' + val.data.input, this.idx)
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">

</style>
