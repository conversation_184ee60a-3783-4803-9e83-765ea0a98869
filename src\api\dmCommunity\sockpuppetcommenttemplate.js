/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 马甲管理-随机评论模板
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/sockpuppetcommenttemplate/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/sockpuppetcommenttemplate/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/sockpuppetcommenttemplate/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/sockpuppetcommenttemplate/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/sockpuppetcommenttemplate/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/sockpuppetcommenttemplate/update`, data)
}

// 修改启动状态
export function switchOpenstatus (data) {
    return requestV1.get(`${prefix}/sockpuppetcommenttemplate/switch/openstatus`, data)
}

// 模板关联评论解绑
export function bindItemDelete (data) {
    return requestV1.deleteJson(`${prefix}/sockpuppetcommenttemplate/bind/item/delete`, data)
}

// 模板关联评论绑定
export function bindItemSave (data) {
    return requestV1.postJson(`${prefix}/sockpuppetcommenttemplate/bind/item/save`, data)
}

// 模板关联评论分页查询
export function queryRelationItemPage (data) {
    return requestV1.postJson(`${prefix}/sockpuppetcommenttemplate/query/relation/item/page`, data)
}

// 克隆
export function sockpuppetcommenttemplateClone (data) {
  return requestV1.postForm(`${prefix}/sockpuppetcommenttemplate/comment/clone`, data)
}