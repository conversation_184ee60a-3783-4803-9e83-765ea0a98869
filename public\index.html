<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <!-- <script src="//cdn.bootcss.com/echarts/3.2.2/echarts.simple.min.js"></script>
    <script src="https://cdn.bootcss.com/vue/2.5.3/vue.js"></script>
    <script src="https://cdn.bootcss.com/vue-router/2.7.0/vue-router.min.js"></script>
    <script src="https://cdn.bootcss.com/axios/0.17.1/axios.min.js"></script>
    <link rel="stylesheet" href="https://cdn.bootcss.com/element-ui/2.4.0/theme-chalk/index.css" rel="external nofollow">
    <script src="https://cdn.bootcss.com/element-ui/2.4.0/index.js"></script> -->


    <script src="https://file.greenboniot.cn/cdnjs/echarts.simple.min.js"></script>
    <script src="https://file.greenboniot.cn/cdnjs/vue.min.js"></script>
    <script src="https://file.greenboniot.cn/cdnjs/vue-router.min.js"></script>
    <script src="https://file.greenboniot.cn/cdnjs/axios.min.js"></script>
    <!-- 项目已经引用element-ui 的CSS 不用重复引用 -->
    <!-- <link rel="stylesheet" href="https://file.greenboniot.cn/cdnjs/index.css" rel="external nofollow"> -->
    <script src="https://file.greenboniot.cn/cdnjs/index.js"></script>
    <script src="https://file.greenboniot.cn/cdnjs/TCaptcha.js"></script>

    <title>
        <%= webpackConfig.name %>
    </title>
</head>

<body>
  <a href="https://xyt.xcc.cn/getpcInfo?sn=1684522661159337984&language=CN&certType=8&url=*.greenboniot.cn" target="_blank" style="position: fixed;display: inline-block;height: 38px;left: 0;top: 0;">
  <div style="width:100%;height:100%;position: absolute;top: 0;left: 0;"></div><embed src="https://program.xinchacha.com/web/1684522661159337984=*.greenboniot.cn.svg"width="103" height="38" type="image/svg+xml" pluginspage="//www.adobe.com/svg/viewer/install/"/></a>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
