/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 财务管理-用工分组
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/physiciangroup/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/physiciangroup/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/physiciangroup/insert`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/physiciangroup/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/physiciangroup/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/physiciangroup/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
  return requestV1.get(`${prefix}/physiciangroup/query/param`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/physiciangroup/update`, data)
}

// 修改启动状态
export function updateOpenStatus(data) {
  return requestV1.postForm(`${prefix}/physiciangroup/update/open/status`, data)
}

// 分页查询绑定
export function queryPageBind(data) {
  return requestV1.postJson(`${prefix}/physiciangroup/bind`, data)
}

// 分页查询未绑定
export function queryPageUnbind(data) {
  return requestV1.postJson(`${prefix}/physiciangroup/unbind`, data)
}

// 根据多参数进行列表查询
export function getPermissionGroup(data) {
  return requestV1.get(`${prefix}/physiciangroup/get/permission/group`, data)
}
// 批量绑定分组 
export function physicianinfoAddGroupIds(data) {
  return requestV1.postJson(`${prefix}/physicianinfo/add/groupIds`, data)
}

// 用工分组（不租户隔离）
export function queryListNotTenant(data) {
  return requestV1.get(`${prefix}/physiciangroup/query/list/not/tenant`, data);
}

// 绑定分组
export function batchBindGroupid(data) {
  return requestV1.postJson(`${prefix}/physiciangroup/batch/bind/groupid`, data);
}
