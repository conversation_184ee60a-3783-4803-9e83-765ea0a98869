/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 项目发款报告
 */

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/projectmoneyauditlog/insert`, data)
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/projectmoneyauditlog/query/page`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/projectmoneyauditlog/update`, data)
}

// 根据主键单一查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/projectmoneyauditlog/query/one`, data)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/projectmoneyauditlog/delete/one/${data.id}`)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/projectmoneyauditlog/query/list`, data)
}

// 统计接口-无权限控制
export function getStatistic(data) {
  return requestV1.postForm(`${prefix}/projectmoneyauditlog/get/statistic`, data)
}

// 修改启动状态
export function updateOpenStatus(data) {
  return requestV1.postForm(`${prefix}/projectmoneyauditlog/update/open/status`, data)
}
