import requestV1 from '@/common/utils/modules/request'

const prefix = '/sop/api/v1'

/**
 * 供应商
 */

// 批量删除
export function deleteBatch (data) {
  return requestV1.deleteForm(`${prefix}/supplier/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/supplier/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/supplier/query/page`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/supplier/query/list`, data);
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/supplier/query/one`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/supplier/update`, data)
}
