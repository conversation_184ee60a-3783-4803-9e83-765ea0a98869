import requestV1 from '@/common/utils/modules/request'

const prefix = '/sop/api/v1'

/**
 * 送货单字段对照表
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/comparisondeliverycharge/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/comparisondeliverycharge/query/page`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/comparisondeliverycharge/query/list`, data);
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/comparisondeliverycharge/query/one`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/comparisondeliverycharge/update`, data)
}
