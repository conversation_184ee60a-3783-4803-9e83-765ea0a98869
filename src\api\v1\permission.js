/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/auth/api/v1'


//根据角色id获取资源中的资源类型
export function getResourceByRoleid(data) {
    return requestV1.get(prefix + '/permission/get/resource/permission/by/roleid', data);
}

//获取当前系统所有资源列表
export function list(data) {
    return requestV1.get(prefix + '/permission/get/all/resource/list', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/permission/insert', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/permission/update', data);
}

//根据主键id指定删除
export function deleteOne(data) {
    return requestV1.deleteForm(`${prefix}/permission/delete/one/${data}`);
}

//获取所有小程序菜单
export function getAllAppletMenuPermission(data) {
    return requestV1.get(prefix + '/permission/get/all/applet/menu/permission', data);
}

//资源树
export function authztree(data) {
  return requestV1.get(prefix + '/permission/query/authztree', data);
}
