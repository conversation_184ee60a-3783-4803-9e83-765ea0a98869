/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

/**
 * 病例征集活动-流程提交记录
 */

// pc分页查询
export function queryPcPage(params) {
  return requestV1.postJson(`/dm/api/v1/casecollectsubmitlog/query/pc/page`, params)
}

// 根据主键单一查询
export function queryOne(params) {
  return requestV1.get(`/dm/api/v1/casecollectsubmitlog/query/one`, params)
}

// 审核
export function commitAudit(params) {
  return requestV1.postJson(`/dm/api/v1/casecollectsubmitlog/commit/audit`, params)
}

// 获取提交类型数量
export function getSubmitTypeCount(params) {
  return requestV1.get(`/dm/api/v1/casecollectsubmitlog/get/submit/type/count`, params)
}

// 获取下一份未审核的问卷
export function checkPending(params) {
  return requestV1.get(`/dm/api/v1/casecollectsubmitlog/check/pending`, params)
}


// 多级审核 
export function audittaskaudit(params) {
  return requestV1.postJson(`/dm/api/v1/audittask/audit`, params)
}


// 拜访审核
export function visitingplanobjectlistAudit(params) {
  return requestV1.postForm(`/dm/api/v1/visitingplanobjectlist/audit`, params)
}


// 活动管理 - 问卷活动 - 审核问卷 邀请人统计 /dm/api/v1/
export function casecollectsubmitloggetcount(params) {
  return requestV1.get(`/dm/api/v1/casecollectsubmitlog/get/submit/type/count`, params)
}


// 获取表单提交统计
export function casecollectsubmitloggetstatistics(params) {
  return requestV1.get(`/dm/api/v1/casecollectsubmitlog/get/form/template/statistics`, params)
}

// 获取生成流水 https://api.greenboniot.cn/dm/api/v1/
export function casecollectsubmitloggetpcpage(params) {
  return requestV1.postJson(`/dm/api/v1/casecollectsubmitlog/query/pc/page`, params)
}

// 一键删除问卷提交记录
// runStatus: 运行状态：1-草稿，2-进行中，3-已结束
// collectionType: 类型, 1个人, 2患者推广, 3精准地推, 4企业推广
// auditType: 审核类型:1-默认，2-多级
export function removeRemoveSubmitLog(params) {
  return requestV1.postForm(`/dm/api/v1/remove/submit/log`, params)
}
export function removeRemoveSubmitLogV2(params) {
  return requestV1.postForm(`/dm/api/v1/remove/submit/log/taskId`, params)
}

// 导出记录excel 
export function casecollectsubmitlogExport(params,fileName='提交记录.xlsx') {
  return requestV1.download(`/dm/api/v1/casecollectsubmitlog/export`, params,fileName,'post',true)
}

// 导入问卷记录 
export function casecollectsubmitlogImportData(data) {
  return requestV1.postJson(`/dm/api/v1/casecollectsubmitlog/import/data`, data)
}

// 批量删除记录 
export function deleteBatch(data) {
  return requestV1.deleteForm(`/dm/api/v1/casecollectsubmitlog/delete/batch/${data.ids}`)
}

// 查看记录-统计接口 
export function casecollectsubmitlogGetSubmitTypeCount(params) {
  return requestV1.postJson(`/dm/api/v2/casecollectsubmitlog/get/submit/type/count`, params)
}