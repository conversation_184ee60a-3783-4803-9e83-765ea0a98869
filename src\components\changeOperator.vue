<template>
  <div class="container">
    <span class="span">经销商</span>

    <el-select v-model="operator" size="mini" filterable placeholder="请选择" class="select" clearable @change="change">
      <el-option
        v-for="item in operatorList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
  </div>
</template>

<script>
import {queryList} from '@/api/operator'
  export default {
    name: "changeOperator",
    props: {
      operatorId: {
        type: [Number,String],
        default: null
      }
    },
    data() {
      return {
        operator: null,
        operatorList: []
      }
    },
    created() {
      this.initData();
    },
    methods: {
      change(val) {
        console.log(val);
        // this.operatorId=val;
        this.$emit('changeOperatorId', val)
      },
      initData() {
        const data = {};
        queryList(data).then((res) => {
          this.operatorList = res.data
          const all = {
            id: null,
            name: '全部'
          }
          this.operatorList.unshift(all)
        }).catch((err) => {

        })
      }
    },

  }
</script>

<style scoped lang="scss">
  .container {

    display: flex;
    width: 387px;
    margin-right: 20px;


    span {
      width: 122px;
      line-height: 40px;
      text-align: center;
    }

  }

  .select {
    width: 217px;
  }
</style>
