/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 拜访管理-拜访客户管理
 */

// 批量删除
export function deleteBatch(str) {
  return requestV1.deleteForm(`${prefix}/audittask/delete/batch/${str}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/audittask/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/audittask/insert`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/audittask/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/audittask/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/audittask/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
  return requestV1.get(`${prefix}/audittask/query/param`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/audittask/update`, data)
}


// 开始任务
export function startaudittasklog(data) {
  return requestV1.get(`${prefix}/audittask/start/${data.id}`)

}

// 取消任务执行 
export function cannelaudittasklog(data) {
  return requestV1.get(`${prefix}/audittask/cancel/${data.id}`)

}

// 完成任务执行 
export function completeaudittasklog(data) {
  return requestV1.get(`${prefix}/audittask/complete/${data.id}`)
}


// 获取任务统计 
export function audittaskstatistics(data) {
  return requestV1.get(`${prefix}/audittask/statistics`, data)
}