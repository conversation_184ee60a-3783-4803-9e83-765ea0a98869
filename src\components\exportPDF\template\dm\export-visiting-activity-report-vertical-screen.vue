<template>
  <!-- v-loading="pageLoading" -->
  <div class="pageContent" v-loading="pageLoading">
    <template v-for="(page, index) in pageContent">
      <div
        class="everyPage"
        :key="index"
        :style="{
          width: pageSize.width + 'px',
          height: pageSize.height + 'px',
        }"
        v-if="!page.authHeight"
      >
        <div class="everyPageContent">
          <!-- 封面 -->
        </div>
      </div>
      <div
        :key="index"
        v-else-if="page.authHeight"
        :style="{
          width: pageSize.width + 'px',
          height: page.targetHeight ? page.targetHeight + 'px' : 'auto',
        }"
        :id="authHeightResult[index]"
      >
        <div class="everyPageContent">
          <!-- 结算报告 -->
          <div class="settlement-page" v-if="page.type === 'settlementBooks'">
            <div class="settlement-page-t">
              {{ userName }} - 结算报告 {{ cycleTime }}
            </div>
            <div class="settlement-page-info">1.结算汇总</div>
            <el-table
              :data="page.pageContent.tableData"
              border
              style="width: 100%"
            >
              <template v-for="item in page.pageContent.hearders">
                <el-table-column
                  :key="item.key"
                  :prop="item.key"
                  :label="item.title"
                  :width="item.width ? item.width : 150"
                >
                  <template slot-scope="scope">
                    <div
                      class="exporttxt"
                      :style="scope.row[item.key + 'Style'] || ''"
                    >
                      {{ scope.row[item.key] }}
                    </div>
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </div>

          <div
            class="serve-page"
            v-if="page.type === 'surveyOfQuestionnaireActivities'"
          >
            <div class="serve-page-title">
              {{ activityName }}的数据报告
              <!-- <span class="underline">拜访计划</span> -->
            </div>
            <div class="serve-page-execute-time">
              计划日期：{{ page.pageContent.planTime }}
            </div>

            <div class="serve-page-execute-sub1">一、访谈内容</div>
            <!-- <div class="serve-page-execute-center-t1">标题</div> -->

            <div class="serve-page-execute-introduction">
              {{ page.pageContent.articleContent }}
            </div>

            <div class="serve-page-execute-sub1">二、拜访记录</div>
            <template v-if="exportVisitType === 1">
              <div class="serve-page-execute-center-t1">默认</div>
              <el-table
                :data="page.pageContent.defaultTableData"
                border
                style="width: 100%"
              >
                <template v-for="item in page.pageContent.hearders">
                  <el-table-column
                    :key="item.key"
                    :prop="item.key"
                    :label="item.title"
                    :width="item.width"
                  >
                    <template slot-scope="scope">
                      <div
                        class="exporttxt"
                        :style="scope.row[item.key + 'Style'] || ''"
                      >
                        {{ scope.row[item.key] }}
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </template>

            <template v-if="exportVisitType === 2">
              <div class="serve-page-execute-center-t1">人物</div>
              <el-table
                :data="page.pageContent.characterTableData"
                border
                style="width: 100%"
              >
                <template v-for="item in page.pageContent.hearders">
                  <el-table-column
                    :key="item.key"
                    :prop="item.key"
                    :label="item.title"
                    :width="item.width"
                  >
                    <template slot-scope="scope">
                      <div
                        class="exporttxt"
                        :style="scope.row[item.key + 'Style'] || ''"
                      >
                        {{ scope.row[item.key] }}
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </template>

            <template v-if="exportVisitType === 3">
              <!-- <div class="serve-page-execute-center-t1">药店</div> -->
              <el-table
                :data="page.pageContent.pharmacyTableData"
                border
                style="width: 100%"
              >
                <template v-for="item in page.pageContent.pharmacyHearders">
                  <el-table-column
                    :key="item.key"
                    :prop="item.key"
                    :label="item.title"
                    :width="item.width"
                  >
                    <template slot-scope="scope">
                      <div
                        class="exporttxt"
                        :style="scope.row[item.key + 'Style'] || ''"
                      >
                        {{ scope.row[item.key] }}
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </template>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>


<script>
import {
  getVisitingplan as queryOne,
  insert,
  update,
} from "@/api/dm/visiting/visitingplan";

import { queryList } from "@/api/dm/visiting/visitingplanobjectlist.js";

import { getIOSTime } from "@/utils/index";

import { getdrugstoreFeedbackList } from "@/utils/enumeration.js";

import { format } from "@/utils/index.js";

// 获取任务详情
import { queryOne as getTaskDetail } from "@/api/todotasks";

export default {
  components: {
    // directoryItem,
  },
  props: {
    // activityName:{
    //   type:String,
    //   default:""
    // },
    // 任务ID
    taskId: {
      type: [String, Number],
      default: null,
    },
    // 活动ID
    activityId: {
      type: [String, Number],
      default: null,
    },

    updatecount: {
      type: Number,
      default: 0,
    },
    // userId: {
    //   type: [String, Number],
    //   default: null,
    // },
  },
  data() {
    return {
      userId: null,
      activityName: "",
      pageLoading: false,
      userName: "默认值",
      cycleTime: "默认值",
      // imgURL,
      // iconUrl,
      pageSize: {
        // height: 595,
        // width: 881,
        width: 1123,
        height: 794,
      },
      pageContent: [
        // 问卷活动调研
        {
          text: "问卷活动调研",
          type: "surveyOfQuestionnaireActivities",
          authHeight: true,
          pageContent: {
            // 提交详情
            // 默认
            defaultTableData: [],

            // 人物
            characterTableData: [],

            // 导出数据的标题
            hearders: [
              { key: "title", title: "标题" },
              { key: "physicianInfoName", title: "拜访人", width: 80 },
              { key: "writeTime", title: "拜访日期", width: 80 },
              { key: "customerName", title: "客户名称", width: 80 },
              { key: "hospitalName", title: "医院", width: 80 },
              { key: "officeCodeText", title: "科室", width: 80 },
              { key: "content", title: "访谈内容", width: 80 },
              { key: "feedbackDesc", title: "客户反馈内容", width: 80 },
              { key: "writeStatus", title: "是否已填写", width: 80 },
              { key: "auditStatusText", title: "审核状态", width: 80 },

              // {
              //   key: "oneLevelAuditStatus",
              //   title: "一级审核状态",
              //   width: 80,
              // },
              // {
              //   key: "oneLevelAuditTimeText",
              //   title: "审核时间",
              //   width: 80,
              // },
              // {
              //   key: "oneLevelAuditUserName",
              //   title: "一级审核人",
              //   width: 80,
              // },
              // { key: "oneLevelAuditDesc", title: "备注", width: 80 },

              // {
              //   key: "twoLevelAuditStatus",
              //   title: "二级审核状态",
              //   width: 80,
              // },
              // {
              //   key: "twoLevelAuditTimeText",
              //   title: "审核时间",
              //   width: 80,
              // },
              // {
              //   key: "twoLevelAuditUserName",
              //   title: "二级审核人",
              //   width: 80,
              // },
              // { key: "twoLevelAuditDesc", title: "备注", width: 80 },

              // {
              //   key: "threeLevelAuditStatus",
              //   title: "三级审核状态",
              //   width: "100px",
              // },
              // {
              //   key: "threeLevelAuditTimeText",
              //   title: "审核时间",
              //   width: 80,
              // },
              // {
              //   key: "threeLevelAuditUserName",
              //   title: "三级审核人",
              //   width: 80,
              // },
            ],
            // 药店
            pharmacyHearders: [
              {
                key: "drugstoreName",
                title: "药店名",
                width: 80,
              },
              {
                key: "drugstoreUserName",
                title: "店员",
                width: 80,
              },
              {
                key: "drugstoreDuration",
                title: "拜访时长(分钟)",
                width: 80,
              },
              {
                key: "drugstoreTransmit",
                title: "传递关键信息",
                width: 120,
              },
              {
                key: "drugstoreFeedbackText",
                title: "客户反馈",
                width: 80,
              },
              {
                key: "terminalDesc",
                title: "终端位置",
                // width: 80,
              },
              {
                key: "writeTimeText",
                title: "拜访日期",
                width: 120,
              },
            ],
            pharmacyTableData: [],
          },
        },
      ],
      authHeightResult: [],
      initsuccesscount: 0,
      targetCount: 2,
      // 导出类型
      exportVisitType: 1,
    };
  },
  mounted() {},
  watch: {
    updatecount(n) {
      this.pageLoading = true;
      this.initsuccesscount = 0;
      // this.targetCount = 10000;
      // this.initpage();
      this.initpageData();
    },
  },
  methods: {
    addComputeTag() {
      // class=“pdf_finish”
      const dom = document.createElement("div");
      dom.classList = "pdf_finish";
      document.body.append(dom);
    },
    // 获取任务详情
    async getTaskDetail() {
      const res = await getTaskDetail({
        id: this.taskId,
      });
      const row = res.data;
      let receiveUserIds = [];
      if (row.receiveUserIds && row.receiveUserIds !== "") {
        receiveUserIds = JSON.parse(row.receiveUserIds);
      }

      let collectionUserId = receiveUserIds[0]
        ? receiveUserIds[0].userId
        : null;
      this.userId = collectionUserId;
      // this.activityName = row.activityName
      // this.activityName = receiveUserIds[0] ? receiveUserIds[0].recordName + '_' + row.activityName  : row.title;
      console.log("receiveUserIds", receiveUserIds);
      return row.businessId;
    },
    // 获取拜访详情
    async queryOne(taskId) {
      let id = this.activityId;
      if (taskId) {
        id = taskId;
      }
      const res = await queryOne({ id: id, isCurrentUser: 2 });

      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (idx !== -1) {
        console.log("kkk");
        this.pageContent[idx].pageContent.articleContent = res.data.content;
        this.exportVisitType = res.data.type - 0;
        this.activityName = res.data.title;

        //  item.value = [res.data.startTime, res.data.endTime];
        // let startTime = format(new Date(res.data.startTime), "YYYY-MM-DD");
        // let endTime = format(new Date(res.data.endTime), "YYYY-MM-DD");
        this.pageContent[idx].pageContent.planTime =
          res.data.startTimeStr + " 至 " + res.data.endTimeStr;

        this.$forceUpdate();
      }

      this.$nextTick(() => {
        setTimeout(() => {
          this.updateSuccess();
        }, 200);
      });

      // this.updateSuccess();

      console.log("res", res);
    },

    // 获取拜访列表
    async getVisitCordList(type, taskId) {
      let id = this.activityId;
      if (taskId) {
        id = taskId;
      }
      let params = {
        // userId
        type: type,
        planId: id,
      };
      if (this.userId) {
        params.userId = this.userId;
      }
      const res = await queryList(params);

      const data = res.data;
      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (type === 1) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.defaultTableData = data;
        }
      } else if (type === 2) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.characterTableData = data;
        }
      } else if (type === 3) {
        if (idx !== -1) {
          const drugstoreFeedbackArr = getdrugstoreFeedbackList();

          this.pageContent[idx].pageContent.pharmacyTableData = data.map(
            (item) => {
              // item.writeTimeText = format(item.writeTime, "YYYY-MM-DD");
              item.writeTimeText = item.writeTimeStr;
              item.drugstoreFeedbackText = this.getEnumText(
                item.drugstoreFeedback,
                drugstoreFeedbackArr
              );
              return item;
            }
          );
        }
      }

      this.$nextTick(() => {
        this.updateSuccess();
      });

      // return data;
    },
    updateSuccess() {
      this.initsuccesscount += 1;

      console.log(
        "this.initsuccesscount",
        this.initsuccesscount,
        this.targetCount
      );

      if (this.initsuccesscount === this.targetCount) {
        this.pageLoading = false;
        this.$nextTick(() => {
          // this.initpage();

          // this.$nextTick(() => {
          //   this.$emit("compute", {});
          // });
          setTimeout(() => {
            this.$emit("compute", {});
            this.addComputeTag();
          }, 800);
        });
      }
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    initpageData() {
      if (this.taskId && this.taskId !== "undefined") {
        this.getTaskDetail().then((id) => {
          // 获取详情
          this.queryOne(id).then((res) => {
            if (this.exportVisitType === 1) {
              // 获取拜访记录- 默认
              this.getVisitCordList(1, id);
            } else if (this.exportVisitType === 2) {
              // 获取拜访记录- 人物
              this.getVisitCordList(2, id);
            } else if (this.exportVisitType === 3) {
              // 获取拜访记录- 药店
              this.getVisitCordList(3, id);
            }
          });
        });
      } else {
        // 获取详情
        this.queryOne().then((res) => {
          if (this.exportVisitType === 1) {
            // 获取拜访记录- 默认
            this.getVisitCordList(1);
          } else if (this.exportVisitType === 2) {
            // 获取拜访记录- 人物
            this.getVisitCordList(2);
          } else if (this.exportVisitType === 3) {
            // 获取拜访记录- 药店
            this.getVisitCordList(3);
          }
        });
      }
    },

    initpage() {
      this.authHeightResult = {};
      for (let i = 0; i < this.pageContent.length; i++) {
        if (this.pageContent[i].authHeight) {
          let id = "authHeight" + i;
          this.authHeightResult[i] = id;
        }
      }

      this.$nextTick(() => {
        this.save();
      });
    },
    // 页面的倍数
    initHeight(height) {
      let pagecount = 1;
      while (height > this.pageSize.height) {
        pagecount += 1;
        height -= this.pageSize.height;
      }

      return pagecount * this.pageSize.height;
    },
    // 保存前初始化页面
    save() {
      for (let key in this.authHeightResult) {
        let id = this.authHeightResult[key];

        let dom = document.getElementById(id);
        console.log(dom.clientHeight, this.pageContent[key]);
        this.pageContent[key].targetHeight = this.initHeight(dom.clientHeight);
      }
      this.$forceUpdate();
    },
  },
};
</script>


<style lang='scss' scoped>
.mgt30 {
  margin-top: 30px;
}
.pageContent {
  // width: 100%;
}
.everyPage {
  overflow: hidden;
  padding: 0 0px 5px;
  box-sizing: border-box;
}
.everyPageContent {
  width: 100%;
  height: 100%;
  background: #fff;
}
// 封面
.cover-page {
  padding-top: 100px;
  padding-left: 50px;
  height: 100%;
  overflow: hidden;
  background: #fff;
  box-sizing: border-box;

  .cover-page-bottom-txt {
    // display:flex;
    margin-top: 50px;
    line-height: 2;
    font-size: 18px;
    text-align: center;
  }
  .cover-text-box-info {
    font-size: 16px;
    color: #7c7c7c;
  }

  .cover-text-box-bottom {
    position: absolute;
    bottom: 100px;
    left: 50px;
    color: #fff;
  }

  .cover-text-box-title {
    font-size: 40px;
    color: #fff;
  }

  .cover-page-icon {
    margin-bottom: 30px;
  }

  .cover-page-img-box {
    position: relative;
  }

  .cover-text-box {
    position: absolute;
    top: 100px;
    left: 30px;
  }

  .cover-page-img {
    width: 100%;
  }

  .cover-text-box-info {
    color: #fff;
    font-size: 24px;
  }
}
// 目录
.directory-page {
  padding: 20px;
  .directory-title {
    font-size: 28px;
    line-height: 2;
    font-weight: 550;
    margin-bottom: 30px;
  }
}

::v-deep .header-row-class-name {
  visibility: hidden;
}
::v-deep .header-row-class-name-size .cell {
  font-size: 10px;
  padding-left: 0px !important;
  padding-right: 0px !important;
  text-align: center;
}
::v-deep .cell-class-name-size .cell {
  font-size: 10px;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

// 问卷活动调研
.serve-page {
  padding: 20px;

  .serve-page-title {
    height: 50px;
    // background: #3a78f1;
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 550;
    // color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0e0800;
  }

  .serve-page-execute-time {
    text-align: center;
    font-weight: 550;
    margin-bottom: 10px;
  }

  .serve-page-execute-sub1 {
    font-weight: 550;
    margin-bottom: 20px;
  }

  .serve-page-execute-center-t1 {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
    font-weight: 550;
  }

  .serve-page-execute-introduction {
    text-indent: 2em;
    color: #7c7c7c;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 40px;
  }

  .serve-cols {
    display: flex;
    align-items: center;
    height: 40px;
    // border-bottom: 1px solid #000;
    border-top: none;
  }
  .serve-col {
  }
  .serve-cols-title {
    padding: 20px;
    background: #ffefef;
    // border: 2px solid #de4040;
    line-height: 1.5;
    color: #de4040;
  }
  .border-bottom {
    border-bottom: 1px solid #000;
  }
  .serve-col-span {
    width: 130px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .serve-col-label {
    padding-left: 30px;
  }
  .serve-cols-info {
    line-height: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #edf3ff;
    color: blue;
  }
}

.person-serve-detail {
  padding: 20px;

  .person-serve-detail-title {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 550;
    font-size: 18px;
  }
  .person-serve-detail-until {
    display: flex;
    justify-content: flex-end;
  }
}

.one {
  background: yellow;
}
.two {
  background: blue;
}
</style>