/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 用药说明栏目
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/medicineclassify/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/medicineclassify/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/medicineclassify/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/medicineclassify/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/medicineclassify/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/medicineclassify/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/medicineclassify/update`, data)
}

// 批量上下架栏目
export function medicineclassifyBatchStatus (data) {
  return requestV1.postForm(`${prefix}/medicineclassify/offline/batch`, data)
}
