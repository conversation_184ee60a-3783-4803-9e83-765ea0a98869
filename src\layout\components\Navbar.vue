<template>
  <div class="navbar">
    <div style="display: flex; align-items: center;">
      <!-- <hamburger
        id="hamburger-container"
        :is-active="sidebar.opened"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      /> -->

      <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />
    </div>

    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <div class="select" v-if="isAccompanyDoctor">
          <span style="margin-right: 10px">服务商:</span>
          <el-select
            size="mini"
            placeholder="请选择当前服务商"
            v-model="currentAccompanyDoctor"
            @change="accompanyDoctorChange"
           
          >
            <template slot="prefix">
              <span :style="`background-image: url('${file_ctx}/static/image/system/logo/icon-sys-select.png');`" class="prefix-icon"></span>
            </template>
            <el-option
              v-for="(item, index) in accompanyDoctorList"
              :key="index"
              :label="`${item.providerName}`"
              :value="item.id"
            />
          </el-select>
        </div>
        <div class="select" v-if="systemList.length !== 0">
          <span style="margin-right: 10px">系统:</span>
          <el-select
            size="mini"
            placeholder="请选择系统"
            v-model="selectValue"
            @change="sysChange"
            :ref="'selectsysRef'"
           
          >
          <!-- @click="AutoShow('selectsysRef')" -->
          <!-- @mouseenter.native="AutoShow('selectsysRef')" -->
            <template slot="prefix">
              <span :style="`background-image: url('${file_ctx}/static/image/system/logo/icon-sys-select.png');`" class="prefix-icon"></span>
            </template>
            <el-option
              v-for="(item, index) in systemList"
              :key="index"
              :label="`${item.name}`"
              :value="item.systemId"
            />
          </el-select>
        </div>
        <div class="select">
          <span style="margin-right: 10px">租户:</span>
          <el-select
            size="mini"
            placeholder="请选择租户"
            v-model="tenantListValue"
            @change="selectChange"
            ref="userselectRef"
          >
          <!-- @mouseenter.native="AutoShow('userselectRef')" -->
            <template slot="prefix">
              <span :style="`background-image: url('${file_ctx}/static/image/system/logo/icon-tenant-select.png');`" class="prefix-icon"></span>
            </template>
            <el-option
              v-for="(item, index) in tenantList"
              :key="index"
              :label="`${item.tenantName}`"
              :value="item.tenantId"
            />
          </el-select>
        </div>
        <!-- <div class="time">{{ time }}</div> -->
        <div class="userName">欢迎{{ userNameobj ? userNameobj.nickname : '无昵称' }}</div>
        <!-- <div class="userName">
          系统版本号:
          {{ version }}
        </div> -->
        <div v-if="isExcelExportPermission" class="right-menu-item hover-effect right-menu-icon-box" style="margin-left: 8px;" @click="excelExportShow = true">
          <img :src="file_ctx + '/static/image/system/icon-download-task.png'" alt="下载任务" title="下载任务" class="right-menu-icon">
        </div>
        <!-- <error-log class="errLog-container right-menu-item hover-effect" /> -->
        <screenfull id="screenfull" class="right-menu-item hover-effect right-menu-icon-box" />
        <ReLoad id="ReLoad" class="right-menu-item hover-effect right-menu-icon-box" />

        <!-- <el-tooltip :content="$t('navbar.size')" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>-->

        <!-- <lang-select class="right-menu-item hover-effect" /> -->
      </template>

      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <template v-if="userNameobj && userNameobj.avatar && userNameobj.avatar != ''">
            <img :src="domainURL(userNameobj.avatar)" class="user-avatar" />
          </template>
          <template v-else>
            <img :src="userHeaderPic" class="user-avatar" />
          </template>

          <i class="el-icon-caret-bottom" />
        </div>

        <el-dropdown-menu slot="dropdown">
          <router-link to="/">
            <el-dropdown-item>{{ $t("navbar.index") }}</el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="dialogVisible = true">
            <span style="display: block">修改密码</span>
          </el-dropdown-item>

          <el-dropdown-item divided @click.native="logout">
            <span style="display: block">{{ $t("navbar.logOut") }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dialog
      title="修改密码"
      :show-close="!isInitPassword"
      :close-on-press-escape="!isInitPassword"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
      append-to-body
    >
      <div>
        <span sty>旧密码</span>
        <el-input
          v-model="oldPassword"
          type="password"
          :show-password="true"
          placeholder="请输入旧密码"
          style="margin: 20px 0"
        />
      </div>
      <div>
        <span>新密码</span>
        <el-input
          v-model="newPassword"
          type="password"
          :show-password="true"
          placeholder="请输入新密码"
          style="margin: 20px 0"
          @input="checkPassword"
        />
        <div class="intensity">
          <span class="psdText">密码强度</span>
          <span
            class="line"
            :class="[level.includes('low') ? 'low' : '']"
          ></span>
          <span
            class="line"
            :class="[level.includes('middle') ? 'middle' : '']"
          ></span>
          <span
            class="line"
            :class="[level.includes('high') ? 'high' : '']"
          ></span>
          <div class="warningtext">
            密码应由8-16位数字、字母、符号组成。请不要使用容易被猜到的密码
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" v-if="!isInitPassword" @click="dialogVisible = false">取 消</el-button>
        <el-button size="mini" type="primary"  @click="save">确 定</el-button>
      </span>
    </el-dialog>

    <excel-export :show.sync="excelExportShow" />
  </div>
</template>

<script>
import { mapGetters,mapState } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
// import ErrorLog from '@/components/ErrorLog'
import Screenfull from '@/components/Screenfull'
// import SizeSelect from '@/components/SizeSelect'
// import LangSelect from '@/components/LangSelect'
// import Search from '@/components/HeaderSearch'
import { parseTime,domainURL } from '@/utils/index'
import user from '@/assets/user.jpg'
import ReLoad from '@/components/ReLoad'
// import { updatePassword } from '@/api/user'
import { select } from '@/components/business/form/select'
import { userid } from '@/api/v1/allsystem'
import { identity } from '@/api/v1/business'
import { getIdentity, getSystemId, getUserInfo } from '@/utils/auth'
import { updatePassword } from '@/api/v1/centeruser'
import { logout } from '@/api/v1/auth'
import {getAccompanyprovideruserProvider,getAccompanyprovideruserQueryOne,getAccompanyproviderQueryAll,accompanymultprovideruserJudgeUser,changeProvider} from "@/api/dmCommunity/accompany-doctor";
import excelExport from '@/views/excelExport/drawer.vue'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    // ErrorLog,
    Screenfull,
    ReLoad,
    excelExport
    // SizeSelect,
    // LangSelect,
    // Search,
  },
  data() {
    return {
      file_ctx: this.$env.file_ctx,
      time: parseTime(new Date().getTime()),
      oldPassword: '',
      newPassword: '',
      dialogVisible: false,
      selectValue: null,
      tenantListValue: null,
      userHeaderPic: domainURL('static/image/system/avatar/icon-default-users.png'),
      isInitPassword: false,
      level: [],
      excelExportShow: false,
      isAccompanyDoctor:false,
      // 服务商集合列表
      accompanyDoctorList:[],
      currentAccompanyDoctor:''
    }
  },
  watch: {
    selectValue(n) {
      this.$store.dispatch('user/setSystemId', n)
    },
    tenantListValue(n) {
      console.log('变了')
      this.$store.dispatch('user/setTenantId', n)
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device']),
    ...mapState('permission', {
      routes: state => state.routes
    }),
    ...mapState({
      userNameobj: state => state.user.staffFiles
    }),
    systemList() {
      console.log('c');
      return this.$u.getStore('systemList') || []
    },
    tenantList() {
      return this.$u.getStore('recordBySystemid') || []
    },
    // userName() {
    //   return getUserInfo().nickName || '游客'
    // },
    // userName() {
    //   return GET_STAFFPROMISE
    // },
    // userHeaderPic() {
    //   return getUserInfo().headPic ? `${this.$env.file_ctx}${JSON.parse(localStorage.getItem('userInfo')).headPic}` : user
    // },
    version() {
      return this.$env.version
    },
    isExcelExportPermission() {
      if (this.$validate.isNull(this.routes)) return false
      const excelExportRoute = this.routes.find(item => item.name === 'excelExport')
      if (this.$validate.isNull(excelExportRoute) || this.$validate.isNull(excelExportRoute.children)) return false
      return !!excelExportRoute.children.find(item => item.name === 'excelExportIndex')
    },
  },
  created() {
    // this.getNowTime()
    this.getSelectValue()
    this.getAccompanyproviderQueryAll()
    
    // this.userid()
  },
  mounted() {
    const isInitPassword = localStorage.getItem('isInitPassword')
    this.isInitPassword = (isInitPassword === true || isInitPassword === 'true')
    if (this.isInitPassword === true || this.isInitPassword === 'true') {
      this.dialogVisible = true
    }
  },
  methods: {
    // 校验密码
    checkPassword(value = this.newPassword, callback = this.$eltool.warnMsg) {
      this.level = []
      // 校验是数字
      const regex1 = /^\d+$/
      // 校验字母
      const regex2 = /^[A-Za-z]+$/
      // 校验符号
      const regex3 =
        /^[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]+$/
      if (regex1.test(value)) {
        this.level.push('low')
      } else if (regex2.test(value)) {
        this.level.push('low')
      } else if (regex3.test(value)) {
        this.level.push('low')
      } else if (/^[A-Za-z\d]+$/.test(value)) {
        this.level.push('low')
        this.level.push('middle')
      } else if (
        /^[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、\d]+$/.test(
          value
        )
      ) {
        this.level.push('low')
        this.level.push('middle')
      } else if (
        /^[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、A-Za-z]+$/.test(
          value
        )
      ) {
        this.level.push('low')
        this.level.push('middle')
      } else if (
        /^[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、A-Za-z\d]+$/.test(
          value
        )
      ) {
        this.level.push('low')
        this.level.push('middle')
        this.level.push('high')
      }
    },
    handleClose() {
      if (this.isInitPassword) {
        return false
      }
      this.dialogVisible = false
    },
    domainURL,
    AutoShow(key){
      console.log(key)
      // console.log(this.$refs)
      // console.log(this.$refs[key])
      if(window.screen.width >= 1280){
        this.$refs[key].toggleMenu();
      }
      // this.$refs.selectsys.click();
    },
    getSystemType(type) {
      if (type === 1) {
        return '业务'
      } else if (type === 2) {
        return '系统'
      }

    },
    getSelectValue() {
      const systemId = getSystemId()
      const tenantListValue = getIdentity() ? getIdentity().tenantId : null
      console.log(tenantListValue)


      if (this.systemList.length !== 0) {
        for (let i = 0; i < this.systemList.length; i++) {
          const item = this.systemList[i];
          if (item.systemId == systemId) {
            this.selectValue = systemId
            break
          }
        }
        if (!this.selectValue) {
          this.selectValue = this.systemList[0].systemId ? this.systemList[0].systemId : null

        }


      } else {
        this.selectValue = systemId ? systemId : null

      }

      console.log('this.selectValue',this.selectValue)
      if (this.tenantList.length !== 0) {
        this.tenantListValue = tenantListValue ? tenantListValue : this.tenantList[0].tenantId ? this.tenantList[0].tenantId : null
        console.log('tenantListValue',this.tenantListValue)
        let iconurl = this.tenantList[0].logoUrl;
        console.log(this.tenantList)
        for(let i=0;i<this.tenantList.length;i++){
          if(this.tenantList[i].tenantId == this.tenantListValue){
            iconurl = this.tenantList[i].logoUrl;

          }
        }
        console.log('iconurl',iconurl)
        this.$store.dispatch('user/updateLogo', iconurl)
      } else {
        this.tenantListValue = tenantListValue ? tenantListValue : null
      }



      let idx = this.systemList.findIndex(item => {
        return item.systemId == this.selectValue
      })

      console.log(idx);
      document.querySelector('title').innerHTML = this.systemList[idx] ? this.systemList[idx].name : ''
    },
    // 陪诊服务商获取logo
    async getPeiZhenLogo() {
    let userId = localStorage.getItem('id');
      if(!userId) return
      let {data:providerId} = await getAccompanyprovideruserProvider({userId:userId})      
      if(!providerId) return
      let {data} = await getAccompanyprovideruserQueryOne({id:providerId});
      this.currentAccompanyDoctor = providerId;
      if(data?.backLogo){
          this.$store.commit('user/SET_MENULOGO',data?.backLogo)
      }
    },
    async showChangeAccompanyDoctor(currentTab){
      if(!currentTab.name) return
      let {data:JudgeUserFlag} = await accompanymultprovideruserJudgeUser({userId:localStorage.getItem('id')})
      this.isAccompanyDoctor = currentTab.name === '陪诊-商户端' && JudgeUserFlag 
      currentTab.name === '陪诊-商户端' && this.getPeiZhenLogo();
    },
    async accompanyDoctorChange(e){
      await changeProvider({providerId:e,userId:localStorage.getItem('id')})
      // 刷新页面
      location.reload()
    },
    async getAccompanyproviderQueryAll(){
      let data = await getAccompanyproviderQueryAll();
      this.accompanyDoctorList = data?.data;
      let current = this.systemList.find(e=>e.systemId === this.selectValue)
      this.showChangeAccompanyDoctor(current)
    },
    isDashboard(route) {
      const name = route && route.name
      if (!name) {
        return false
      }
      return name.trim().toLocaleLowerCase() === 'index'.toLocaleLowerCase()
    },
    sysChange() {
      console.log('this.selectValue',this.selectValue)
      console.log('this.systemList',this.systemList);
      let idx = this.systemList.findIndex(item => {
        return item.systemId == this.selectValue
      })

      this.showChangeAccompanyDoctor(this.systemList[idx]);
      document.querySelector('title').innerHTML = this.systemList[idx].name
      this.$store.dispatch('user/changeRoles', this.selectValue)
      if(this.systemList[idx].name == '药品说明书-商户端'){
        this.$store.commit('user/SET_FLAGLOGO',1)
        this.$router.push('/pharmacyCyclopedia/drugBox/pharmacyCyclopedia/drugBox/brandManage/pharmacyCyclopedia/drugBox/brandManage')
      }else {
        this.$store.commit('user/SET_FLAGLOGO',2)
      }
      return
      const tenantId = getIdentity().tenantId
      identity({ tenantId }).then(res => {
        console.log(res.data)
        this.$store.dispatch('user/setIdentity', res.data)
        this.$eltool.successMsg('切换成功，正在刷新界面请稍等')

        setTimeout(() => {
          location.reload(true)
        }, 1000)
      })
    },
    selectChange(tenantId) {
      console.log('tenantId--------------', tenantId)
      let recordId = null
      if(tenantId) {
        const optionItem = this.tenantList.find(item => item.tenantId === tenantId)
        this.$store.dispatch('user/updateLogo', optionItem.logoUrl)
        recordId = this.$validate.isNull(optionItem.userTenantRecordList) ? null : optionItem.userTenantRecordList[0].recordId
      }
      identity({ tenantId, userTenantRecordType: 1, recordId }).then(res => {
        this.$store.dispatch('user/setIdentity', res.data)
        console.log(res.data)
        this.$eltool.successMsg('切换成功，正在刷新界面请稍等')
        setTimeout(() => {
          location.reload(true)
        }, 1000)
      })
    },
    save() {
      
      const oldPassword = this.oldPassword
      const newPassword = this.newPassword
      const centerUserId = localStorage.getItem('id')
      if (!newPassword) {
        this.$eltool.warnMsg('密码不能为空')
        return
      }
      if (newPassword.length < 8) {
        this.$eltool.warnMsg('密码不少于8位')
        return
      }
      if (newPassword.length > 16) {
        this.$eltool.warnMsg('密码不大于16位')
        return
      }
      if (oldPassword === newPassword) {
        this.$eltool.warnMsg('新密码不得与旧密码一致')
        return
      }
      updatePassword({ oldPassword, newPassword, centerUserId }).then(res => {
        this.$eltool.successMsg(res.msg)
        this.logout()
      })
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    logout() {
      logout({}).then(async res => {
        await this.$store.dispatch('user/logout')
        this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      })

    },
    getNowTime() {
      setInterval(() => {
        const time = new Date().getTime()
        this.time = parseTime(time)
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  display: flex;
  // align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  // height: 50px;
  // overflow: hidden;
  position: relative;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    // float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    // float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    // float: right;
    // flex: 1;
    display: flex;
    flex-wrap: nowrap;
    text-align: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }
    .select {
      display: inline-block;
      vertical-align: text-bottom;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      font-size: 16px;
      padding: 0 8px;
    }
    .time {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
    }
    .userName {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      font-size: 16px;
    }

    .right-menu-item {
      display: inline-block;
      // padding: 0 8px;
      height: 100%;
      font-size: 18px !important;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }

      &+.right-menu-item {
        margin-left: 16px;
      }
    }

    .right-menu-icon-box {
      display: flex;
      align-items: center;
      height: 50px;
    }

    .right-menu-icon {
      width: 21px;
    }

    .avatar-container {
      margin: 0 30px 0 23px;
      margin-top: 4px;
      .avatar-wrapper {
        display: flex;
        flex-direction: row;
        // margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 12px;
        }
      }
    }
    div,
    span {
      font-size: 14px !important;
    }
  }
}
.prefix-icon {
  display: inline-block;
  width: 20px;
  height: 100%;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}

.show_pwd {
  cursor: pointer;
  user-select: none;
  padding-right: 5px;
}
.intensity {
  .psdText {
    font-size: 14px;
    margin-right: 10px;
  }
  .line {
    display: inline-block;
    width: 48px;
    height: 4px;
    background: #d8d8d8;
    border-radius: 3px;
    margin-right: 8px;
    &.low {
      background: #f4664a;
    }
    &.middle {
      background: #ffb700;
    }
    &.high {
      background: #2cbb79;
    }
  }
  .level {
    margin: 0 16px 0 8px;
  }
  .warningtext {
    color: #5a5a5a;
    font-size: 12px;
    margin-top: 5px;
  }
}
</style>
