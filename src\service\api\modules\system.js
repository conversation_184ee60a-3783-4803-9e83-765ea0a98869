/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api'

// 系统通用接口
export default {
  queryOnePermission(params) {
    return requestV1.get( '/auth/api/v1/permission/query/one', params);
  },
  insertPermission(params) {
    return requestV1.postJson( '/auth/api/v1/permission/insert', params);
  },
  updatePermission(params) {
    return requestV1.putJson( '/auth/api/v1/permission/update', params);
  },
  delBatchPermission(ids) {
    return requestV1.deleteForm( '/auth/api/v1/permission/delete/batch/'+ ids);
  },
  queryLowestlevelAllPermission(params) {
    return requestV1.get( '/auth/api/v1/permission/query/lowestlevel/all',params);
  },
  insertAuthztreePermission(params) {
    return requestV1.postJson( '/auth/api/v2/permission/insert/authztree',params);
  },
  insertRoleinfo(params) {
    return requestV1.postJson( '/auth/api/v1/roleinfo/insert',params);
  },
  updateRoleinfo(params) {
    return requestV1.putJson( '/auth/api/v1/roleinfo/update',params);
  },
  queryOneRoleinfo(params) {
    return requestV1.get( '/auth/api/v1/roleinfo/query/one',params);
  },
  // Long systemId, Long tenantId, Integer type,获取资源code
  getPermissionCodeBySystemIdAndTenantIdAndType(params) {
    return requestV1.get( '/auth/api/v1/permission/get/permission/code/by/systemid/and/tenantid/and/type',params);
  },
  // 发送短信
  sendSms(params) {
    return requestV1.postForm('/basics/api/v1/sms/send/sms', params);
  },
}


