/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'
import env from '@/config/env'


/**
 * 爬虫库管理
 */

// 医院分页列表
export function getShospitalQueryPage (data) {
    return requestV1.postJson(`${prefix}/crawlershospital/query/page`,data)
}
// 医院 新增医院
export function getShospitalInsert (data) {
    return requestV1.postJson(`${prefix}/crawlershospital/insert`,data)
}
// 医院分页列表
export function getShospitalQueryList (data) {
    return requestV1.get(`${prefix}/crawlershospital/query/list`,data)
}

// 医院分页列表（不租户隔离）
export function getShospitalQueryPageNotTenant (data) {
  return requestV1.postJson(`${prefix}/crawlershospital/query/page/not/tenant`,data)
}

// 医院-单个查询
export function getShospitalQueryOne (data) {
  return requestV1.get(`${prefix}/crawlershospital/query/one`,data)
}

// 医院-更新接口
export function getShospitalUpdatedata (data) {
  return requestV1.putJson(`${prefix}/crawlershospital/update`,data)
}

// 医院下的科室分页列表
export function getShospitalDept (data) {
    return requestV1.postJson(`${prefix}/crawlershospitaldept/query/page`, data)
}

// 医生分页列表
export function getDoctorQueryPage (data) {
    return requestV1.postJson(`${prefix}/crawlershospitaldoctor/query/page`, data)
}

// 医院下的科室根据主键查询
export function getShopitalDeptQuery(data) {
    return requestV1.postForm(`${prefix}/crawlershospitaldept/hospital/dept`, data)
}

// 数据爬取-问答分页列表
export function getQuestionanswerPage(data) {
    return requestV1.postJson(`${prefix}/crawlersquestionanswer/query/page`, data)
}

// 数据爬取-问答新增
export function QuestionanswerInsert(data) {
    return requestV1.postJson(`${prefix}/crawlersquestionanswer/insert`, data)
}
// 数据爬取-批量删除
export function deleteBatchQuestionanswer(data) {
    return requestV1.deleteForm(`${prefix}/crawlersquestionanswer/delete/batch/${data.ids}`)
}
// 数据爬取-单个删除
export function deleteOneQuestionanswer(data) {
    return requestV1.deleteForm(`${prefix}/crawlersquestionanswer/delete/one/${data.id}`)
}
// 数据爬取-根据主键单一查询
export function questionanswerQueryOne(data) {
  return requestV1.get(`${prefix}/crawlersquestionanswer/query/one`,data)
}
// 数据爬取-更新数据
export function updateQuestionanswer(data) {
  return requestV1.putJson(`${prefix}/crawlersquestionanswer/update`,data)
}

// 数据爬取-根据主键id指定删除
export function deleteQuestionanswer(data) {
    return requestV1.deleteForm(`${prefix}/crawlersquestionanswer/delete/one/${data.id}`)
}

// 帖子QA模板马甲库-分页列表
export function postqataskQueryPage(data) {
  return requestV1.postJson(`${prefix}/postqatask/query/page`,data)
}

// 帖子QA模板马甲库-根据主键单一查询
export function postqataskQueryOne(data) {
  return requestV1.get(`${prefix}/postqatask/query/one`,data)
}

// 帖子QA模板马甲库-新增
export function postqataskInsert(data) {
  return requestV1.postJson(`${prefix}/postqatask/insert`,data)
}

// 帖子QA模板马甲库-更新
export function postqataskUpdate(data) {
  return requestV1.putJson(`${prefix}/postqatask/update`,data)
}

// 帖子QA模板马甲库-批量删除数据
export function postqataskBatchDelete(data) {
  return requestV1.deleteForm(`${prefix}/postqatask/delete/batch/${data.ids}`)
}

// 帖子QA模板马甲任务-分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/query/page`, data)
}

// 帖子QA模板马甲任务-查看马甲QA模板编辑分页
export function queryLookPage(data) {
  return requestV1.postJson(`${prefix}/postqataskitem/query/page`, data)
}

// 帖子QA模板马甲-导入模板库
export function postqataskInsertImport(data) {
  return requestV1.postJson(`${prefix}/postqataskitem/batch/insert`, data)
}

// 帖子QA模板马甲-编辑马甲库的更新配置
export function postqataskUpdateConfig(data) {
  return requestV1.putJson(`${prefix}/postqataskitem/update`, data)
}

// 帖子QA模板马甲-编辑马甲库的批量删除
export function postqataskTempBatchDelete(data) {
  return requestV1.deleteForm(`${prefix}/postqataskitem/delete/batch/${data.ids}`)
}

// 爬虫库管理--医生管理

// 新增医生
export function DoctorInsert (data) {
  return requestV1.postJson(`${prefix}/crawlershospitaldoctor/insert`, data)
}

// 更新医生
export function DoctorUpdate(data) {
  return requestV1.putJson(`${prefix}/crawlershospitaldoctor/update`, data)
}

// 根据主键查询单一医生
export function DoctorQueryOne(data) {
  return requestV1.get(`${prefix}/crawlershospitaldoctor/query/one`, data)
}

// 批量删除医生
export function batchDeleteDoctor(data) {
  return requestV1.deleteForm(`${prefix}/crawlershospitaldoctor/delete/batch/${data.ids}`)
}

// 医院导入
export const crawlershospitalExcelImport = env.ctx + prefix + '/crawlershospital/excel/import'
// 医生导入
export const crawlershospitalDoctorExcelImport = env.ctx + prefix + '/crawlershospital/doctor/excel/import'

// 批量审核 
export function postqataskitemBatchAudit(data) {
  return requestV1.postForm(`${prefix}/postqataskitem/batch/audit`,data)
}

// 启动操作
export function postqataskExecuteSwitch(data) {
  return requestV1.postForm(`${prefix}/postqatask/executeSwitch`,data)
}