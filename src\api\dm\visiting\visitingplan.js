/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'
import env from '@/config/env'

/**
 * 拜访管理-拜访计划
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/visitingplan/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/visitingplan/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/visitingplan/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/visitingplan/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/visitingplan/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/visitingplan/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/visitingplan/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/visitingplan/update`, data)
}

// 修改计划状态
export function updatePushStatus (data) {
    return requestV1.putForm(`${prefix}/visitingplan/update/push/status`, data)
}

// 根据id单一查询 比queryOne接口多返回拜访对象
export function getVisitingplan (data,headers={}) {
    return requestV1.get(`${prefix}/visitingplan/get/visitingplan`, data,null,headers)
}


// 药店拜访分析报告 
export function getVisitingPlanAnalysisReport (data, headers = {}) {
  return requestV1.get(`${prefix}/visitingplan/get/analysis/report`, data, null, headers)
}

// 药店拜访分析报告 
export const pharmacyVisitImport = `${env.ctx + prefix}/visitingplan/pharmacy/visit/import`
