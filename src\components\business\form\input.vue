<!--输入框组件-->
<template>
  <el-form-item
    :label="config.label"
    :label-width="config.width"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name"
  >
    <el-input
      v-model="form.data.input"
      :disabled="disabled"
      :class="childClass"
      :maxlength="config.maxlength"
      :placeholder="config.placeholder"
      :type="config.type"
      style=""
    />
  </el-form-item>
</template>

<script>
export default {
  name: 'Input',
  props: {
    // el-form-item的class值
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    // 是否可编辑
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    // el-input的class值
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    // 组件循环时需要的idx值
    idx: {
      type: Number,
      default: 0
    },
    // 最多字数
    maxlength: {
      type: Number,
      required: false,
      default: 40
    },
    // 参数配置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          type: 'text',
          label: '文本输入',
          name: 'text',
          maxlength: 40,
          placeholder: '请填写文本输入',
          rules: [
            { required: true, message: '请输入文本输入', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          input: ''
        }
      }
    }
  },

  watch: {
    data: {
      handler(val) {
        // debugger
        this.form.data.input = val
      },
      deep: true
    },
    // 监听到form的数据变化则把config.name和form.data.input和idx传到父组件
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.input, this.idx)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">

</style>
