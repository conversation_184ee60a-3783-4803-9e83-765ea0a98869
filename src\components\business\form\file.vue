<!--上传文件组件-->
<template>
  <div class="upload">
    <el-upload
      ref="uploadFile"
      :drag="config.drag"
      :class="childClass"
      :on-preview="preview"
      :on-remove="remove"
      :file-list="file.file.list"
      :multiple="config.multiple"
      :headers="header"
      :data="config.param"
      :limit="config.limit"
      :before-upload="fileBefore"
      :on-success="fileSuccess"
      :on-error="fileError"
      :on-change="onChange"
      :accept="config.accept"
      :action="action"
      :auto-upload="config.autoUpload"
      :name="config.name"
      class="">
      <slot v-if="!config.drag">
        <el-button size="mini" type="primary" > 文件上传</el-button>
      </slot>
      <slot v-if="config.drag">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <!--<div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb</div>-->
      </slot>
      <slot name="tip">
        <div slot="tip" class="el-upload__tip">
          规格说明:支持{{ config.accept }}类型，数量最多{{ config.number }}个，单个不能超过{{ config.size / 1000 }}Mb
        </div>
      </slot>

    </el-upload>
  </div>
</template>
<script>
import {getToken} from '@/utils/auth'

export default {
  name: 'File',
  components: {},
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '文件上传',
          name: 'file',
          limit: 3,
          size: 100000,
          accept: '*/*',
          multiple: false,
          param: {},
          data: [],
          drag: true,
          number: 2,
          autoUpload: true,
          rules: [
            {required: true, message: '请上传文件', trigger: 'blur'}
          ]
        }
      }
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    data: {
      type: String,
      required: false,
      default: ''
    },
    content: {
      type: String,
      default: '点击上传'
    }
  },
  data() {
    return {
      action: this.$env.ctx + '/basics/api/v1/attachment/upload',
      header: {
        Authorization: getToken()
      },
      file: {
        file: {
          list: [],
          data: []
        }
      },
      fileList:[]
    }
  },
  watch: {
    data: {
      handler(val) {
        if (val.length !== 0) {
          const array = val.split(',')
          for (const k of array) {
            const imageData = this.file.file.data
            let isExit = false
            for (const j of imageData) {
              if (k === j.dir) {
                isExit = true
                break
              }
            }
            if (isExit) {
              return
            }
            const obj = {
              name: '',
              url: this.$env.file_ctx + k,
              dir: k
            }

            this.file.file.list.push(obj)
            this.file.file.data.push(obj)
          }
        } else {
          this.file.file.list = []
          this.file.file.data = []
        }
      },
      deep: true
    }
  },
  methods: {
    /**
     * 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
     * @param file
     * @param fileList
     */
    onChange(file, fileList){
      // debugger
      console.log(file)
      // console.log(fileList)
      this.fileList = fileList
    },
    /**
     * 点击上传，当设置为手动上传的时候
     */
    submitUpload() {
      if (this.fileList.length === 0){
        this.$eltool.warnMsg('请选择文件上传')
        return false
      }
      this.$nextTick(() => {
        this.$refs.uploadFile.submit()
      })
    },
    /**
     *  预览
     *  @param file
     */
    preview(file) {
      window.location.href = file.url
    },
    /**
     *  删除图片
     *  @param file
     *  @param fileList
     */
    remove(file, fileList) {
      this.fileList = fileList
      const that = this
      const dir = file.dir
      that.file.file.data = that.file.file.data.filter((item) => {
        return item.dir !== dir
      })
      that.file.file.list = that.file.file.list.filter((item) => {
        return item.dir !== dir
      })
      that.$emit('uploadResult', that.config.name, that.file.file.data)
    },
    /**
     * 文件上传成功时的钩子
     * @param response
     * @param file
     * @param fileList
     */
    fileSuccess(response, file, fileList) {
      const that = this
      const data = response
      // debugger
      if (data.code === 0) {
        for (const v of data.data) {
          const listData = {
            name: v.name,
            url: this.$env.file_ctx + v.dir,
            dir: v.dir
          }
          that.file.file.list.push(listData)
          that.file.file.data.push(v)
        }
        that.$emit('uploadResult', that.config.name, that.file.file.data)
      } else {
        return false
      }
    },
    /**
     * 文件上传失败时的钩子
     * @param err
     * @param file
     * @param fileList
     */
    fileError(err, file, fileList) {
      // console.log(err)
      this.$eltool.errorMsg('上传发生异常，亲重新操作！')
      return false
    },
    /**
     * 上传文件之前的钩子
     * @param file(上传的文件)
     */
    fileBefore(file) {
      if (this.config.number !== 0) {
        if (this.file.file.data.length >= this.config.number) {
          this.$eltool.warnMsg('只能上传' + this.config.number + '个文件')
          return false
        }
      }
      if (this.config.size > 0){
        if (file.size / 1000 > this.config.size) {
          this.$eltool.warnMsg('已超过文件大小限制')
          return false
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
