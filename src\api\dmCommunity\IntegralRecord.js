/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

const prefix = '/dm/api/v1'

/**
 * 积分统计流水
 */


// 积分记录统计 分页
export function pointrecordStatisticPage (data) {
    return requestV1.postJson(`${prefix}/pointrecord/statistic/page`, data)
}
// 积分统计汇总
export function pointrecordStatistic(data){
    return requestV1.postJson(`${prefix}/pointrecord/statistic`, data)
}
//积分统计记录导出
export async function pointrecordStatisticExport(data){
    return requestV1.download(`${env.ctx}${prefix}/pointrecord/statisticExport`, data, `积分统计汇总.xlsx`, 'post',false,{
        'content-type': 'application/json; charset=utf-8'
    })
}

