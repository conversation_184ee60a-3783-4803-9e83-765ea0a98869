/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 祈福许愿数据统计
 */

// 祈福许愿数据统计分页列表查询
export function pointwishbarrageQueryPage (data) {
    return requestV1.postJson(`${prefix}/pointwishbarrage/query/page`, data)
}

// 祈福许愿新增
export function pointwishbarrageInsert (data) {
    return requestV1.postJson(`${prefix}/pointwishbarrage/insert`, data)
}

// 祈愿人数
export function pointwishbarrageCount (data) {
    return requestV1.postJson(`${prefix}/pointwishbarrage/count`, data)
}

