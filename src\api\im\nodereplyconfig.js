import requestV1 from '@/common/utils/modules/request'

const prefix = '/im/api/v1'

/**
 * 咨询节点配置
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/nodereplyconfig/delete/batch/${data.ids}`)
}

// 根据id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/nodereplyconfig/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/nodereplyconfig/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/nodereplyconfig/query/page`, data);
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/nodereplyconfig/query/one`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/nodereplyconfig/update`, data)
}
