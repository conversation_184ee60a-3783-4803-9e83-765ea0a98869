/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

// 项目管理添加地推打卡

// 地推打卡新增
export function pushclockAdd (data) {
  return requestV1.postJson(`${prefix}/pushclock/add`, data)
}

// 地推打卡分页列表查询
export function pushclockQueryPage (data) {
  return requestV1.postJson(`${prefix}/pushclock/query/page`, data)
}

// 地推打卡详情
export function pushclockGet (data) {
  return requestV1.get(`${prefix}/pushclock/get/${data.id}`, data)
}

// 地推打卡编辑
export function pushclockUpdatePushClock (data) {
  return requestV1.postJson(`${prefix}/pushclock/updatePushClock`, data)
}

// 根据任务id查询地推打卡
export function pushclockdetailQuery (data) {
  return requestV1.get(`${prefix}/pushclockdetail/query/${data.taskId}`,data)
}

// 批量更新地推打卡
export function pushclockdetailBatchUpdate (data) {
  return requestV1.postJson(`${prefix}/pushclockdetail/batchUpdate`, data)
}

// 地推打卡根据主键id指定删除
export function pushclockdetailDeleteOne (data) {
  // return requestV1.deleteJson(`${prefix}/pushclock/delete/one${id:data.id}`, data)
  return requestV1.deleteForm(`${prefix}/pushclock/delete/one/${data.id}`, data)
}

// 地推打卡根据主键集合字符串批量删除数据
export function pushclockdetailDeleteBatch (data) {
  // return requestV1.deleteJson(`${prefix}/pushclock/delete/batch/${ids:data.ids}`, data)
  return requestV1.deleteForm(`${prefix}/pushclock/delete/batch/${data.ids}`, data)
}


// 根据项目id获取地推打卡明细
export function pushclockdetailStatistic (data) {
  return requestV1.get(`${prefix}/pushclockdetail/statistic/${data.demandId}`, data)
}

// 地推打卡查看图片
export function pushclockdetailQueryImage (data) {
  return requestV1.postJson(`${prefix}/pushclockdetail/query/image`, data)
}

// 根据项目id获取医院打卡明细
export function pushclockdetailHospitalStatistic (data) {
  return requestV1.get(`${prefix}/pushclockdetail/hospitalStatistic/${data.demandId}`, data)
}

// 地推打卡医院图片
export function pushclockdetailQueryHospitalImage (data) {
  return requestV1.postJson(`${prefix}/pushclockdetail/queryHospital/image`, data)
}

// 地推打卡批量下载图片
export function pushclockdetailQueryHospitalBatchDownload (data) {
  // return requestV1.download(`${prefix}/pushclockdetail/queryHospital/download`, data,data.fileName,'post',true)
  return requestV1.postJson(`${prefix}/pushclockdetail/queryHospital/download`, data)
}

// 地推下载分页
export function pushclockdownloadQueryPage (data) {
  return requestV1.postJson(`${prefix}/pushclockdownload/query/page`, data)
}

// 地推打卡查看视频
export function pushclockdetailQueryVideo (data) {
  return requestV1.postJson(`${prefix}/pushclockdetail/query/video`, data)
}

// 查询地推打卡医院维度明细
export function pushclockdetailQueryTask (data) {
  return requestV1.get(`${prefix}/pushclockdetail/query/task`, data)
}

// 根据项目id获取医院打卡明细-分页 新的----
export function pushclockdetailHospitalStatisticQueryPage (data) {
  return requestV1.postJson(`${prefix}/pushclockdetail/hospitalStatistic/page`, data)
}


// 批量导入
export const applyImport = `${env.ctx}${prefix}/pushclock/import`
