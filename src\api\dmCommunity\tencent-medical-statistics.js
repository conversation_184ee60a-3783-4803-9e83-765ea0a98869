/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

const prefix = '/dm/api/v1'

/**
 * 积分统计流水
 */


// 腾讯医典统计 分页
export function imageoperationrecordPage (data) {
    return requestV1.postJson(`${prefix}/imageoperationrecord/query/page/statistics`, data)
}
// 统计主页 分页
export function imagehomepageQueryPage (data) {
    return requestV1.postJson(`${prefix}/imagehomepage/query/page`, data)
}
// 获取页面名称
export function imagehomepageQueryList (data) {
    return requestV1.get(`${prefix}/imagehomepage/query/list`, data)
}
// 获取腾讯医典数据页面
export function imagehomepageList (data) {
    return requestV1.get(`${prefix}/imagehomepage/list`, data)
}
// 获取总积分
export function pointrecordTotal(data){
    return requestV1.postJson(`${prefix}/pointrecord/total`, data)
}
//腾讯医典导出
export async function imageoperationrecordExport(data){
    debugger
    return requestV1.download(`${prefix}/imageoperationrecord/export/excel`, data, `腾讯医典统计.xlsx`, 'post',{
        'content-type': 'application/json; charset=utf-8'
    })
}

