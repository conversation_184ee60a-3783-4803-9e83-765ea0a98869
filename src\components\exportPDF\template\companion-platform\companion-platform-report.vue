<template>
  <div :id="uuid" class="report-content-box" v-loading="pageLoading">
    <!-- 封面 -->
    <div
      class="settlement-page"
      :style="{
        height: pageSize.height + 'px',
        width: pageSize.width + 'px',
      }"
    >
      <div class="theme-img">
        <img :src="bgImg" class="" alt="" />
      </div>
      <div class="analysis-report-box">
        <div class="activity">关于陪诊平台的技术开发服务外包项目</div>
        <div class="project-report">
          项&nbsp;目&nbsp;服&nbsp;务&nbsp;报&nbsp;告
        </div>
        <div class="execution-time">
          执行时间：{{ fixedFieldObject.startTime }} 至
          {{ fixedFieldObject.endTime }}
        </div>
        <div class="server">服务方：{{ fixedFieldObject.serviceProvider }}</div>
        <div class="project-leader">
          项目方：{{ fixedFieldObject.projectParty }}
        </div>
      </div>
    </div>
    <!-- 目录 -->
    <div
      class="settlement-page"
      :style="{
        height: pageSize.height + 'px',
        width: pageSize.width + 'px',
      }"
    >
      <div class="theme-img">
        <img :src="themeImg" class="" alt="" />
      </div>
    </div>
    <div class="pageContent">
      <template v-for="(page, index) in pageContent">
        <div
          :key="index"
          v-if="page.authHeight"
          :style="{
            width: pageSize.width + 'px',
            height: page.targetHeight ? page.targetHeight + 'px' : 'auto',
          }"
          :id="authHeightResult[index]"
        >
          <div class="everyPageContent">
            <div class="settlement-page" v-if="page.type === 'allContent'">
              <div class="user-content-box">
                <!-- 信息 -->
                <div class="project-info">
                  <div class="title color1">
                    <img :src="productImgUrl" class="title-icon" alt="" />
                    项目基础信息
                  </div>
                  <div class="title-table">
                    <div class="name">
                      项目名称：<span class="general">{{
                        fixedFieldObject.projectName
                      }}</span>
                    </div>
                    <div class="execution">
                      项目执行周期：<span class="general"
                        >{{ fixedFieldObject.startTime }} 至
                        {{ fixedFieldObject.endTime }}</span
                      >
                    </div>
                    <div class="project-bott">
                      <div class="project-bott-l">
                        项目方：
                        <span class="general">{{
                          fixedFieldObject.projectParty
                        }}</span>
                      </div>
                      <div class="project-bott-r">
                        <span class="white-space">服务商：</span>
                        <span class="general">{{
                          fixedFieldObject.serviceProvider
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 介绍 -->
                <div class="project-introduce">
                  <div class="title color1">
                    <img :src="productBgUrl" class="title-icon" alt="" />
                    项目背景介绍
                  </div>
                  <div class="info">
                    <div class="info-t">
                      小葫芦平台，是{{
                        fixedFieldObject.projectParty
                      }}，潜心打造的医患社交平台。旨在打造一个医药大健康信息平台，为用户提供真实可靠的病友互助沟通渠道。该平台日常发布权威的健康科普资讯，为用户提供专业的健康咨询服务。同时，患者也可以在平台上发布健康求助贴子，与其他病友分享治疗经验，共同探索健康的捷径。
                    </div>
                    <br />
                    <div class="info-b">
                      为了进一步提升{{
                        fixedFieldObject.projectParty
                      }}旗下小葫芦平台的活跃度和用户粘性，绿葆公司上线了小葫芦陪诊平台，为患者提供了一个便捷的途径来寻求医疗帮助，使得他们更愿意在小葫芦圈中活跃，分享治疗经验和健康知识。平台的互动性和实用性有助于吸引更多用户加入，形成良性循环，提升整个小葫芦圈的活跃度和用户粘性。
                    </div>
                  </div>
                </div>
                <!-- 结果 -->
                <div class="project-result" style="margin-top: 460px">
                  <div class="title color1">
                    <img :src="productResultUrl" class="title-icon" alt="" />
                    项目验收结果
                  </div>
                  <div class="info">
                    项目“关于陪诊平台的技术开发服务外包项目”由{{
                      fixedFieldObject.serviceProvider
                    }}于 {{ fixedFieldObject.startTime }} 至
                    {{ fixedFieldObject.endTime }} 承接执行。{{
                      fixedFieldObject.projectParty
                    }}于当月最后一天验收项目，本项目由
                    {{ fixedFieldObject.productTaskNumber }}
                    个技术人员执行，验收结果如下。
                  </div>
                  <div class="title-table">
                    <div class="serve">
                      <div class="serve-l">项目方</div>
                      <div class="serve-r">
                        {{ fixedFieldObject.projectParty }}
                      </div>
                    </div>
                    <div class="serve">
                      <div class="serve-l">服务方</div>
                      <div class="serve-r">
                        {{ fixedFieldObject.serviceProvider }}
                      </div>
                    </div>
                    <div class="executions">
                      <div class="executions-l">项目执行时间</div>
                      <div class="executions-r">
                        {{ fixedFieldObject.startTime }} 至
                        {{ fixedFieldObject.endTime }}
                      </div>
                    </div>
                    <div class="team">
                      <div class="team-l">技术人员数量</div>
                      <div class="team-r">
                        {{ fixedFieldObject.productTaskNumber }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <template v-if="page.type === 'productResultShow'">
              <imageContent
                :textObject="textObject"
                :imageList="page.pageContent.imageList"
                :updatecount="productResultShowUpdateCount"
                :pageSize="pageSize"
              ></imageContent>
            </template>
            <!-- 代码量 -->
            <div class="settlement-page" v-if="page.type === 'data-display'">
              <div class="data-display-box">
                <div class="sub-t">6、数据展示</div>
                <div class="data-display-info">
                  界面：270个<br>
                  代码：45w行
                </div>
                <div class="data-display-list">
                  <div class="data-display-item">
                    <div class="data-display-t">6.1、前端代码量如下</div>
                    <div class="image-list">
                      <template v-for="item in page.pageContent.frontEnd">
                        <img :src="item.url" :key="item.url" class="image-item" />
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="settlement-page" v-if="page.type === 'data-display2'">
              <!-- <div class="sub-t">6、数据展示</div> -->
              <div class="data-display-box">
                <div class="data-display-list">
                  <div class="data-display-item">
                    <div class="data-display-t">6.2.{{page.cnum}}、页面ui数量如下</div>
                    <div class="image-list">
                      <template v-for="item in page.pageContent.ui">
                        <img :src="item.url" :key="item.url" class="image-item w100" />
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
             <div class="settlement-page" v-if="page.type === 'data-display3'">
              <!-- <div class="sub-t">6、数据展示</div> -->
              <div class="data-display-box">
                <div class="data-display-list">
                  <div class="data-display-item">
                    <div class="data-display-t">6.3、后端代码量如下</div>
                    <div class="image-list">
                      <template v-for="item in page.pageContent.backend">
                        <img :src="item.url" :key="item.url" class="image-item w100" />
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 地推活动结果分析 -->
            <div class="settlement-page" v-if="page.type === 'topicContent'">
              <div class="reportContent-box">
                <div class="chartItem">
                  <div class="reportContent-lt">
                    {{ page.pageContent.index }}{{ page.pageContent.title }}
                  </div>
                  <div class="flex-center-box">
                    <div class="reportContent-l">
                      <div
                        class="panel barTarget"
                        :style="{
                          height: page.pageContent.echartHeight + 'px',
                        }"
                      >
                        <div
                          class="barChart"
                          :id="uuid + '_' + page.pageContent.uuid"
                        ></div>
                        <div
                          class="buttomCharts"
                          v-if="page.pageContent.showBottom"
                          :style="{
                            'margin-top':
                              page.pageContent.echartMarginTop + 'px',
                          }"
                        ></div>
                      </div>
                    </div>
                    <div class="gender-content">
                      <el-table
                        :data="page.pageContent.tableData"
                        class="gender-table"
                        border
                      >
                        <template v-for="item in page.pageContent.hearders">
                          <el-table-column
                            :key="item.key"
                            :prop="item.key"
                            :label="item.title"
                            :width="item.width"
                          >
                            <template slot-scope="scope">
                              <div
                                class="exporttxt"
                                :style="scope.row[item.key + 'Style'] || ''"
                              >
                                {{ scope.row[item.key] }}
                              </div>
                            </template>
                          </el-table-column>
                        </template>
                      </el-table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="settlement-page"
              v-if="page.type === 'reportContentBooks'"
            >
              <div class="user-execute-box">
                <div class="title color1">
                  <img :src="executiveSummaryUrl" class="title-icon" alt="" />
                  执行总结
                </div>
                <div class="btw" style="margin-top: 30px">
                  <div class="basic-info">
                    <div class="basic-text1">
                      本项目共组织{{
                        fixedFieldObject.productTaskNumber
                      }}个技术人员，运用丰富的应用开发知识和相关领域的开发经验，熟悉陪诊服务平台的业务逻辑和用户需求。利用编程语言和开发工具，搭建起一个功能完善、界面友好的小葫芦陪诊服务平台。这包括用户注册与登录、预约挂号、陪诊服务选择、支付结算等功能的实现。
                    </div>
                    <br />
                    <div class="basic-text1">
                      小葫芦陪诊平台给小葫芦圈带来了诸多积极影响，不仅增强了医患互动与信任，还提升了用户粘性与活跃度，拓展了服务范围与深度，促进了品牌曝光与营销，积累了疾病数据与精准触达，并推动了行业创新与升级。
                    </div>
                    <br />

                    <div class="basic-text1">
                      小葫芦平台致力于打造一个患者交流圈，为患者提供一个交流互动的平台。在平台上，患者可以通过发帖、评论等方式与其他患者进行交流，分享自己的经验和感受。小葫芦平台设有健康科普专区，为用户提供丰富的健康知识和科普信息。在该专区，用户可以浏览各类健康科普文章、提高自身的健康意识和健康素养，以预防疾病并更好地管理自己的健康。
                    </div>
                    <br />

                    <div class="basic-text1">
                      小葫芦平台还提供在线咨询服务，用户可以通过平台与医生进行实时的在线咨询。用户可以通过文字、图片、语音等方式向医生描述自己的病情，医生会根据用户信息提供相关建议。这种在线咨询服务方便快捷，为用户提供了便利的医疗服务，尤其对于一些日常症状和健康问题，用户可以及时获得专业的医生建议。
                    </div>
                    <br />

                    <div class="basic-text1">
                      总之，小葫芦平台通过患者交流圈、健康科普专区和在线咨询服务等功能，为用户提供了一个全面的健康管理平台。通过地推活动的推广，小葫芦平台在全国范围内得到了更多用户的关注和参与，未来，小葫芦平台将继续致力于为用户提供更好的健康服务和互动体验，助力用户更好地管理自己的健康。
                    </div>
                  </div>
                  <div class="imgBox">
                    <img :src="bottomIconUrl" class="bottomIconUrl" alt="" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import companionPlatformReport from "@/components/exportPDF/template/mixins/companion-platform-report.js";
import imageContent from "./components/image-content.vue";
import reportCommonImage from "@/components/exportPDF/template/mixins/report-common-image";
export default {
  mixins: [companionPlatformReport, reportCommonImage],
  components: {
    imageContent,
  },
  data() {
    return {
      // 固定字段
      fixedFieldObject: {
        projectName: "关于陪诊平台的技术开发服务外包项目", // 项目名称
        serviceProvider: "", // 服务方
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己
        startTime: "", // 项目执行开始时间
        endTime: "", // 项目执行结束时间
        productTaskNumber: 0, // 地推团队数量
      },
      requestType: 28, // 新增type,  精准推广 = 3, 线上推广 = 6
      textObject: {
        "bottom-navigation-01": "1、小程序底部导航",
        // "personal-center-04": "2、小程序服务详情页",
        "service-details-page-02": "2、小程序服务详情页",
        "service-appointment-process-03": "3、小程序服务预约流程",
        "accompanying-doctor-end-04": "4、小程序陪诊师端",
        "service-provider-management-05": "5、小程序服务商管理",
      },
    };
  },
};
</script>
<style lang="scss" scoped>
@import "../css/accurate-report.scss";
.data-display-box {
  padding: 20px;
  overflow: hidden;
  background: #fff;
}

.sub-t {
  line-height: 2;
  font-size: 18px;
  display: flex;
  font-weight: 550;
  // color: #1f6ee1;
}
.data-display-info {
  font-size: 16px;
  padding-left: 2em;
  line-height: 2;
  // color: #1f6ee1;
  // padding-top: 20px;
}
.data-display-item {
  .data-display-t {
    // padding-left: 28px;
    margin-top: 20px;
    font-size: 15px;
    line-height: 2;
    display: flex;
    font-weight: 550;
    margin-bottom: 5px;
    // color: #1f6ee1;
    text-indent: 2em;
    margin-bottom: 20px;
  }
  .image-list {
    display: flex;
    flex-wrap: wrap;
    padding-left: 2em;
    box-sizing: border-box;
    .image-item {
      max-width: 100%;
      // width: 100%;
      padding-left: 5px;
      padding-bottom: 20px;
      object-fit: contain;
    }
    // .w100 {
    //   min-width: 60%;
    //   width: 60%;
    //   margin: 0 auto;
    // }
  }
}
</style>

<style lang="scss">
.el-table.ue-table {
  background: #edf3ff;
  th,
  td {
    background: #edf3ff;
  }
}
</style>