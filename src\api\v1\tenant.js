/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
const domain = window.location.hostname
let tenantGroupId = ''
// if(domain == 'saas.greenboniot.cn'){
//   tenantGroupId = '2247271971981414402' //绿葆自营分组
// } else 
if(domain == 'miaofu.greenboniot.cn'){
  tenantGroupId = '2247272103221186561' //秒付第三方分组
} else if(domain == 'yunxiang.greenboniot.cn'){
  tenantGroupId = '2247272212898476036' //云翔联盟
}

const prefix = '/auth/api/v1'

//租户查询表格分页数据
export function queryList(data) {
  let params = {...data,tenantGroupId}
  if(!params.tenantGroupId){
    delete params.tenantGroupId
  }
  return requestV1.get(prefix + '/tenant/query/list', params);
}

//根据系统查询租户列表
export function recordBySystemid(data) {
    let params = {...data,tenantGroupId}
    if(!params.tenantGroupId){
      delete params.tenantGroupId
    }  
    return requestV1.get(prefix + '/centeruser/get/tenant/record', params);
}

//添加租户
export function addTenant(data) {
    return requestV1.postJson(prefix + '/tenant/add/tenant', data);
}

//删除租户
export function deleteTenant(data) {
    return requestV1.deleteForm(prefix + '/tenant/delete/tenant', data);
}

//编辑租户
export function editTenant(data) {
    return requestV1.putJson(prefix + '/tenant/edit/tenant', data);
}

//查询表格分页数据
export function queryPage(data) {
  let params = {...data,condition:{...data.condition,tenantGroupId}}
  if(!params?.condition?.tenantGroupId){
    delete params?.condition?.tenantGroupId
  }  
  return requestV1.postJson(prefix + '/tenant/query/page', params);
}

// 获取租户列表
export function tenantQueryList (data) {
  let params = {...data,tenantGroupId}
  if(!params.tenantGroupId){
    delete params.tenantGroupId
  }
  return requestV1.get(prefix + '/tenant/query/list', params)
}
// 批量绑定分组
export function tenantBatchBindGroup (data) {
    return requestV1.postForm(prefix + '/tenant/batch/bind/group', data)
}
// 租户分组新增
export function tenantgroupInsert (data) {
    return requestV1.postJson(prefix + '/tenantgroup/insert', data)
}
// 租户分组-单一查询
export function tenantgroupQueryOne (data) {
    return requestV1.get(prefix + '/tenantgroup/query/one', data)
}
// 租户分组更新
export function tenantgroupUpdate (data) {
    return requestV1.putJson(prefix + '/tenantgroup/update', data)
}
// 租户分组单一删除
export function tenantgroupDeleteOne (data) {
  return requestV1.deleteForm(`${prefix}/tenantgroup/delete/one/${data.id}`)
}
// 租户分组列表查询
export function tenantgroupQueryList (data) {
  return requestV1.get(prefix + '/tenantgroup/query/list', data)
}
// 租户分组分页列表
export function tenantgroupQueryPage (data) {
  return requestV1.postJson(prefix + '/tenantgroup/query/page', data)
}
// 租户分组一键启用禁用
export function tenantgroupBatchDisable (data) {
  return requestV1.postForm(prefix + '/tenantgroup/batch/disable', data)
}