/**
 * Created by leill on 09/12/20.
 * 获取枚举列表工具
 */
/* eslint-disable */

//处理并组成需要的key value 的枚举数组
function getResult(key = 'label', value = 'value', list) {
  const result = []
  for (const i in list) {
    const obj = {}
    obj[key] = list[i].label
    obj[value] = list[i].value
    obj.default = list[i].default
    obj.permission = list[i].permission
    if (list[i].style) {
      obj.style = list[i].style
    }
    result.push(obj)
  }
  return result
}

export function commonList(key, value) {
  const arr = [
    { value: 1, label: '是' },
    { value: 2, label: '否' },
  ]
  return getResult(key, value, arr)
}

// 性别
export function getGender(key, value) {
  const arr = [
    { value: 1, label: '男' },
    { value: 2, label: '女' },
    { value: 3, label: '未知' },
  ]
  return getResult(key, value, arr)
}

// 启用状态
export function getCommonOpenStatus(key, value) {
  const arr = [
    { value: 1, label: '启用' },
    { value: 2, label: '禁用' },
  ]
  return getResult(key, value, arr)
}

// 获取二维码类型
export function outPackType(key, value) {
  const arr = [
    { label: '关注', value: 1 },
    { label: '免费出袋', value: 2 },
    { label: '支付出袋', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 获取二维码类型
export function codeType(key, value) {
  const arr = [
    { label: '大公众号', value: 1 },
    { label: 'H5', value: 2 },
    { label: '微信小程序', value: 3 },
    { label: '公锤大号', value: 4 },
    { label: '聚合码', value: 5 },
    { label: '支付宝小程序', value: 6 },

  ]
  return getResult(key, value, arr)
}

// 获取用户类型
export function getUserType(key, value) {
  const arr = [
    { label: '平台', value: 0 },
    { label: '租户内部人员', value: 1 },
    { label: '经销商', value: 2 },
    { label: '维护人员', value: 3 },
    { label: '渠道商', value: 4 },
    { label: '财务', value: 5 },
    // { label: '运维', value: 6 },
    { label: '运营', value: 6 },
    { label: '广告商', value: 7 },
    { label: '法务', value: 8 },
    { label: '采购', value: 9 },
    { label: '仓管', value: 10 },
    { label: '商务', value: 11 },
    { label: '媒体广告主', value: 12 },
    { label: '小葫芦运营', value: 14 },
  ]
  return getResult(key, value, arr)
}

// 获取消息类型
export function getNewsType(key, value) {
  const arr = [
    { label: '设备故障通知', value: 'A1' },
    { label: '出袋日报通知', value: 'B1' },
    { label: '经销商审核提现', value: 'B2' },
    { label: '补发袋子审核结果', value: 'B3' },
    { label: '提现审核结果', value: 'B4' },
    { label: '财务审核提现', value: 'C1' },
    { label: '运营审核提现', value: 'C2' },
    { label: '财务审核补发袋', value: 'C3' },
    { label: '运营审核补发袋', value: 'C4' },
    { label: '吸粉预警', value: 'C5' },
    { label: '用户反馈', value: 'C6' },
    { label: '袋子订单', value: 'D1' },
    { label: '库存预警', value: 'D2' },
    { label: '公告类消息', value: 'E1' },
    { label: '公共消息处理结果', value: 'E2' },


  ]
  return getResult(key, value, arr)
}

// 获取设备口类型  default 默认选中
export function getPlanStatus(key, value) {
  const arr = [
    { label: '未启动', value: 1 },
    { label: '已启动', value: 2, default: true },
    { label: '已停止', value: 3 },
    { label: '已暂停', value: 4 }
  ]
  return getResult(key, value, arr)
}

// 获取设备口类型
export function getDeviceOutPackType(key, value) {
  const arr = [
    { label: '上单', value: 1 },
    { label: '双口', value: 2 },
    { label: '下单', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 获取经销商类型
export function getAttribute(key, value) {
  const arr = [
    { label: '省代', value: 1 },
    { label: '市代', value: 2 },
    { label: '普通经销商', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 获取渠道线类型
export function getChannelType(key, value) {
  const arr = [
    { label: '医院', value: 1 },
    { label: '药店', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 获取客户类型
export function getCustomerType(key, value) {
  const arr = [
    { label: '经销商', value: 1 },
    { label: '渠道商', value: 2 },
    { label: '非渠道商', value: 3 }

  ]
  return getResult(key, value, arr)
}

// 获取合作阶段
export function getStageType(key, value) {
  const arr = [
    { label: '接洽', value: 1 },
    { label: '已合作', value: 2 },
    { label: '已终止', value: 3 }


  ]
  return getResult(key, value, arr)
}

// 获取费用状态
export function getCostStateType(key, value) {
  const arr = [
    { label: '预垫付', value: 1 },
    { label: '已支付', value: 2 },

  ]
  return getResult(key, value, arr)
}

// 获取附加费用类型
export function getCostType(key, value) {
  const arr = [
    { label: '客户', value: 1 },
    { label: '平台', value: 2 },

  ]
  return getResult(key, value, arr)
}


// 获取广告主类型列表
export function getWxAuthType(key, value) {
  const arr = [
    { label: '企业号', value: 1 },
    { label: '服务号', value: 2 },
    { label: '订阅号', value: 3 },
    { label: '小程序', value: 4 },
    { label: '公锤', value: 5 },
    { label: '壹道', value: 6 },
    { label: '视频号', value: 7 },
    // { label: '个人微信号', value: 8 },
    { label: '个微-第三方', value: 8 },
    { label: '微信群', value: 9 },
    { label: '梦墨', value: 10 },
    { label: '视频号直播预约', value: 11 },
    { label: '袋拉拉微信服务号', value: 12 },
    { label: '个微-自营', value: 13 },
    { label: '会员卡', value: 14 },
    { label: '公众号-非授权', value: 15 },
    { label: '视频号分组', value: 16 },
    { label: '御航-服务号', value: 17 },
    { label: '中科在线', value: 18 },
    { label: '企微获客链接', value: 19 }

  ]
  return getResult(key, value, arr)
}

// 获取自动回复消息类型
export function getMsgReplyType(key, value) {
  const arr = [
    { label: '文本', value: 1 },
    { label: '卡片', value: 2 },
    { label: '图片', value: 3 },
    { label: '视频', value: 4 },
    { label: '语音', value: 5 },
    { label: '小程序', value: 7 },

  ]
  return getResult(key, value, arr)
}

// 日对账表异常类型字典
export function getOperatorDailyReportType(key, value) {
  const arr = [
    { label: '免费未生成明细', value: 1 },
    { label: '付费未生成明细', value: 2 },
    { label: '反馈退款（付费）', value: 3 },
    { label: '反馈失败（免费）', value: 4 },
    { label: '支付失败，没退款记录', value: 5 },
    { label: '退款失败的数据', value: 6 },
    { label: '校验分账数据失败', value: 7 },


  ]
  return getResult(key, value, arr)
}

// 备案类型
export function getInstallReportType(key, value) {
  const arr = [
    { label: '备案锁定', value: 1 },
    { label: '已上线', value: 2 },
    { label: '备案释放', value: 3 },
    { label: '未绑定', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 工单状态
export function getWorkOrderStatus(key, value) {
  const arr = [
    { label: '待接单', value: 1 },
    { label: '已接单', value: 2 },
    { label: '执行完成', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 仓库类型
export function getWarehouseType(key, value) {
  const arr = [
    { label: '平台自营库', value: 1 },
    { label: '客户共享仓', value: 2 },
    { label: '客户公司仓', value: 3 },
    { label: '安装单位仓', value: 4 }
  ]
  return getResult(key, value, arr)
}

// 库存类型
export function getProcessType(key, value) {
  const arr = [
    { label: '手动', value: 3 },
    { label: '换货', value: 4 },
    { label: '退款', value: 5 },
    { label: '销售', value: 6 },
    { label: '补库', value: 7 },
    { label: '其他', value: 8 },


  ]
  return getResult(key, value, arr)
}

// 订单状态
export function getOrderStatusType(key, value) {
  const arr = [
    { label: '新建发货需求', value: 0 },
    { label: '申请货物发货', value: 1 },
    { label: '运营提交发货信息', value: 2 },
    { label: '已发货', value: 3 },
    { label: '物流已签收', value: 4 },
    { label: '收货方已签收', value: 5 },
  ]
  return getResult(key, value, arr)
}
export function getOrderStatusTypeV2(bizOrderNo) {
  let arr = [];
  if (bizOrderNo && bizOrderNo.startsWith('XQAPPLET')) {
    arr = [
      { label: '新建发货需求', value: 0 },
      { label: '申请货物发货', value: 1 },
      { label: '小程序申请发货', value: 2 },
      { label: '已发货', value: 3 },
      { label: '物流已签收', value: 4 },
      { label: '收货方已签收', value: 5 },
      { label: '签字签收', value: 6 },
    ]
  } else {
    arr = [
      { label: '新建发货需求', value: 0 },
      { label: '申请货物发货', value: 1 },
      { label: '运营提交发货信息', value: 2 },
      { label: '已发货', value: 3 },
      { label: '物流已签收', value: 4 },
      { label: '收货方已签收', value: 5 },
      { label: '签字签收', value: 6 },
    ]
  }

  return getResult('label', 'value', arr)
}
// 物料物流订单状态
export function getMaterialLogisticsOrderStatus(key, value) {
  const arr = [
    { label: '待发货', value: 2 },
    { label: '已发货', value: 3 },
    { label: '物流已签收', value: 4 },
    { label: '收货方已签收', value: 5 },

  ]
  return getResult(key, value, arr)
}

// 待办类型
export function getWorkItemType(key, value) {
  const arr = [
    { label: '财务待认证-款项', value: 1 },
    { label: '财务待认证-附加费用', value: 2 },
    { label: '财务已认证-款项', value: 3 },
    { label: '财务已认证-附加费用', value: 4 },
    { label: '法务待认证', value: 5 },
    { label: '法务已认证', value: 6 },
    { label: '客户资质运营待认证', value: 7 },
    { label: '客户资质运营已认证', value: 8 },
    { label: '待接工单', value: 9 },
    { label: '已接工单', value: 10 },
    { label: '已完成工单', value: 11 },
    { label: '工单评价', value: 12 },
    { label: '发货待运营审核', value: 13 },
    { label: '发货运营已审核', value: 14 },
    { label: '待仓管员发货', value: 15 },
    { label: '仓管员已发货', value: 16 },
    { label: '物流已签收', value: 17 },
    { label: '入库通知', value: 18 },
    { label: '物料已发货', value: 19 },
    { label: '物料物流已签收', value: 20 },
    { label: '客户资质法务待认证', value: 21 },
    { label: '客户资质财务待认证', value: 22 },
    { label: '推广款项财务待认证', value: 26 },
    { label: '推广款项财务已认证', value: 27 },
    { label: '推广法务待认证', value: 28 },
    { label: '推广法务已认证', value: 29 },
    { label: '法务审核结果', value: 30 },
    { label: '财务审核结果', value: 31 },
    { label: '推广合同法务审核结果', value: 32 },
    { label: '推广款项财务审核结果', value: 33 },




  ]
  return getResult(key, value, arr)
}

// 物料物流订单状态
export function getSoftType(key, value) {
  //1 广告机 2 广告机出袋版 3 平板
  const arr = [
    { label: '广告机', value: 1 },
    { label: '广告机出袋版', value: 2 },
    { label: '平板', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 商务部门ID
export function getDepartmentId(key, value) {
  const arr = [
    { label: '商务部', value: 1 },
  ]
  return getResult(key, value, arr)
}

// 合作商设备类型
export function getgoDeviceType(key, value) {
  //1 广告机 2 广告机出袋版 3 平板
  const arr = [
    { label: '小号设备', value: 1 },
    { label: '大号设备', value: 2 }
  ]
  return getResult(key, value, arr)
}

//收支类型
export function getIncomeExpendType(key, value) {
  const arr = [
    { label: '收款', value: 1 },
    { label: '退款', value: 2 },
    // { label: '不需付款', value: 3 }
  ]
  return getResult(key, value, arr)
}

//收支类型
export function storeProcessType(key, value) {
  const arr = [
    { label: '手动入库', value: 1 },
    { label: '换货入库', value: 2 },
    { label: '手动', value: 3 },
    { label: '换货', value: 4 },
    { label: '退款', value: 5 },
    { label: '销售', value: 6 },
    { label: '补库', value: 7 },
    { label: '其他', value: 7 },
  ]
  return getResult(key, value, arr)
}

//运营状态
export function getDeviceStatusType(key, value) {
  const arr = [
    { label: '正常运营', value: 1 },
    { label: '暂停运营', value: 2 },
    { label: '停用', value: 3 },
    { label: '出厂', value: 4 },
    { label: '待激活', value: 5 },
    { label: '停机', value: 6 },
    { label: '暂停使用（免扣）', value: 7 },
    { label: '删除', value: -1 },
  ]
  return getResult(key, value, arr)
}

//推送类型
export function getSendType(key, value) {
  const arr = [
    { label: 'H5', value: 1 },
    { label: '小程序', value: 2 },
    { label: 'H5标签领袋', value: 3 },
    { label: '其他', value: 4 },
  ]
  return getResult(key, value, arr)
}

//出袋业务类型
export function getPacketBusinnessType(key, value) {
  const arr = [
    { label: 'H5', value: 1 },
    { label: '小程序标签评价出袋', value: 2 },
    { label: 'H5标签评价领袋', value: 3 },
    { label: '活动出袋', value: 4 },
    { label: '支付宝保险领袋', value: 5 },
    { label: '支付宝不准入领袋', value: 6 },
    { label: '灯火广告领袋', value: 7 },
    { label: '灯火无广告领袋', value: 8 },
    { label: '灯火放弃广告领袋', value: 9 },
    { label: '支付宝放弃保险领袋', value: 10 },
    { label: '第三方企微群领袋', value: 12 },
    { label: '自营个微领袋', value: 13 },
  ]
  return getResult(key, value, arr)
}

//推送类型
export function getActivityPutPlanType(key, value) {
  const arr = [
    { label: '赠险', value: 1 },
    { label: '万丈赠险', value: 2 },
    { label: '赠送电信手机卡', value: 3 },
    { label: '京东', value: 4 },
    { label: '马科技赠险', value: 5 },
  ]
  return getResult(key, value, arr)
}

//出袋类型
export function getPacketType(key, value) {
  const arr = [
    { label: '关注出袋', value: 1 },
    { label: '免费出袋', value: 2 },
    { label: '支付出袋', value: 3 },
    { label: '首次关注免费出袋', value: 4 },
    { label: '活动出袋', value: 5 },

  ]
  return getResult(key, value, arr)
}

//财务明细科目类型
export function getStatusType(key, value) {
  const arr = [
    { label: '商品销售', value: 1 },
    { label: '商品补贴', value: 2 },
    { label: '分账收入', value: 3 },
    { label: '分账支出', value: 4 },
    { label: '机器使用费-通联', value: 5 },
    { label: '退款', value: 6 },
    { label: '手动平账-通联余额-收入', value: 7 },
    { label: '手动平账-通联余额-支出', value: 8 },
    { label: '手动平账-补贴余额-收入', value: 9 },
    { label: '手动平账-补贴余额-支出', value: 10 },
    { label: '通联提现', value: 11 },
    { label: '补贴退款', value: 12 },
    { label: '补贴提现', value: 13 },
    { label: '次品退款', value: 14 },
    { label: '机器使用费-补贴', value: 15 },
    { label: '活动补贴', value: 16 },
    { label: '活动补贴退款', value: 17 },
    { label: '渠道商商品销售收入', value: 18 },
    { label: '渠道商商品销售支出', value: 19 },
    { label: '余额转移-收入', value: 20 },
    { label: '余额转移-支出', value: 21 },
    { label: '渠道商商品销售收入(绿葆直营代收)', value: 22 },
    { label: '渠道商商品销售收入（绿葆直营代收）-退款', value: 23 },
    { label: '分账支出-退款', value: 24 },
    { label: '渠道商商品销售收入-退款', value: 25 },
    { label: '支付宝额外补贴', value: 26 },
    { label: '支付宝额外补贴退款', value: 27 }
  ]
  return getResult(key, value, arr)
}

//认证状态
export function getVerifyStatus(key, value) {
  const arr = [
    { label: '已认证', value: 1 },
    { label: '待认证', value: 2 },
    { label: '认证中', value: 3 },
    { label: '认证撤回', value: 4 },
    { label: '认证驳回', value: 5 },
  ]
  return getResult(key, value, arr)
}

export function getMqttStatus(key, value) {
  const arr = [
    { value: null, label: '全部' },
    { value: 1001, label: '出袋指令(1001)' },
    { value: 1002, label: '查询设备版本号(1002)' },
    { value: 1003, label: '更新版本，适用软件包，奔想APP(1003)' },
    { value: 1004, label: '设备重启初始化上报系统状态(1004)' },
    { value: 2004, label: '上报系统状态(2004)' },
    { value: 1005, label: '上传日志信息(1005)' },
    { value: 2005, label: '固件上传日志信息(2005)' },
    { value: 1006, label: '固件升级，适用于固件包，自研下位机固件(1006)' },
    { value: 1007, label: '升级包传输(1007)' },
    { value: 1008, label: '换码(1008)' },
    { value: 1009, label: '更新广告模板消息(1009)' },
    { value: 1010, label: '重启(1010)' },
    { value: 1011, label: '截屏(1011)' },
    { value: 1012, label: '更新二维码(1012)' },
    { value: 1013, label: '更新模板分组的素材(1013)' },
    { value: 1014, label: '设置声音(1014)' },
    { value: 1015, label: '是否定时开关机(1015)' },
    { value: 1016, label: '绑定广告设备号(1016)' },
    { value: 1017, label: 'apk语音包(1017)' },
    { value: 1018, label: '固件升级(1018)' },
    { value: 1019, label: 'app系统信息(1019)' },
    { value: 1020, label: '卡袋缺袋逻辑指令(1020)' },
    { value: 1021, label: '安卓广告机上报状态(1021)' },
    { value: 1022, label: '获取传感器状态(1022)' },
    { value: 1023, label: '设置自动开关机时间(1023)' },
    { value: 1024, label: '获取设备日志(1024)' },
    { value: 1025, label: '远程复位状态(1025)' },
  ]
  return getResult(key, value, arr)
}


//出袋类型
export function getOutPacketErrStatus(key, value) {
  const arr = [
    { label: '缺袋(100)', value: '100' },
    { label: '卡袋(101)', value: '101' },
    { label: '偏移(102)', value: '102' },
    { label: '离线(103)', value: '103' },
    { label: '队列阻塞，超时下发不出袋(104)', value: '104' },
    { label: '信号阻塞，超时下发不出袋(105)', value: '105' },
    { label: '系统超时(106)', value: '106' },
    { label: '客户端发送消息失败(107)', value: '107' },
    { label: '设备不存在(108)', value: '108' },
    { label: '设备默认上报失败(109)', value: '109' },
    { label: '手工补账，超时下发不出袋(110)', value: '110' },
    { label: '出袋正常(0)', value: '0' }
  ]
  return getResult(key, value, arr)
}

//医院等级
export function getLevel(key, value) {
  const arr = [
    { label: '一甲', value: 100 },
    { label: '二甲', value: 101 },
    { label: '三甲', value: 102 },
    { label: '未定级', value: -1 },

  ]
  return getResult(key, value, arr)
}

//月度粉丝对账渠道
export function getFansChannelType(key, value) {
  const arr = [
    { label: '自营', value: 1 },
    { label: '合作商', value: 2 },
  ]
  return getResult(key, value, arr)
}

//经销商分账模板信息修改记录操作类型
export function getSeparateaccountOperationType(key, value) {
  const arr = [
    { label: '新增', value: 1 },
    { label: '编辑', value: 2 },
  ]
  return getResult(key, value, arr)
}

//设备使用费是否优先扣除
export function getDeviceSubsidyPriorityType(key, value) {
  const arr = [
    { label: '关', value: 0 },
    { label: '开', value: 1 },
  ]
  return getResult(key, value, arr)
}

//小程序计费核定项
export function getAppletBillingItem(key, value) {
  const arr = [
    { label: '用户信息授权', value: 1 },
    { label: '手机号授权', value: 2 },
    { label: '通用领取', value: 3 },
    { label: '访问曝光', value: 4 },
    { label: '首次跳转', value: 6 },
  ]
  return getResult(key, value, arr)
}

//出袋状态
export function getOutPacketStatus(key, value) {
  const arr = [
    { label: '出袋成功', value: 1 },
    { label: '出袋失败', value: 2 },
    { label: '处理中', value: 3 },
  ]
  return getResult(key, value, arr)
}

//小程序访问类型
export function getMiniAppVisitType(key, value) {
  const arr = [
    { label: '用户信息授权', value: 1 },
    { label: '手机号码授权', value: 2 },
    { label: '通用领取', value: 3 },
    { label: '访问曝光', value: 4 },
    { label: '推送', value: 5 },
    { label: '首次跳转', value: 6 },
  ]
  return getResult(key, value, arr)
}

// es数据类型
export function getEsDataType(key, value) {
  const arr = [
    { label: '领袋记录', value: 1 },
    { label: '财务明细记录', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 投放计划日志数据状态
export function getPlanLogType(key, value) {
  const arr = [
    { label: '初始化', value: 1 },
    { label: '启动', value: 2 },
    { label: '停止', value: 3 },
    { label: '暂停', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 设备使用费扣款类型
export function getDeductionsType(key, value) {
  const arr = [
    { label: '正常扣款', value: 1 },
    { label: '免开机费', value: 2 },
    { label: '提现时线下支付使用费', value: 4 },
    { label: '线下按期支付', value: 5 },
  ]
  return getResult(key, value, arr)
}

// 表单模板题目类型
export function getTopicType(key, value) {
  const arr = [
    { label: '单选题', value: 1 },
    { label: '多选题', value: 2 },
    { label: '文本题', value: 3 },
    { label: '数值题', value: 4 },
    { label: '文件上传', value: 5 },
    { label: '共用题干题（嵌套题）', value: 6 },
    { label: '手机号码', value: 7 },
    { label: '邮寄地址', value: 8 },
    { label: '条形滑块类型', value: 9 },
    { label: '评星组件(仅诊后点评有效)', value: 10 },
    { label: '微信昵称(弹框授权)', value: 14 },
    { label: '性别(男/女)', value: 15 },
    { label: '年龄(1-100)', value: 16 },
    { label: '科室标签', value: 17 },
    { label: '省市', value: 18 },
    { label: '疾病', value: 19 },
    { label: '姓名', value: 20 },
    { label: '日期（YYYY-MM-DD）', value: 21 },
    { label: '日期（YYYY-MM-DD HH:mm:ss）', value: 22 },
    { label: '科室和疾病', value: 23 },

  ]
  return getResult(key, value, arr)
}

// 表单模板文本类型
export function getTextType(key, value) {
  const arr = [
    { label: '普通文本', value: 1 },
    // { label: '日期', value: 2 },
    // { label: '时间', value: 3 },
    // { label: '日期时间', value: 4 },
    { label: '多行文本', value: 5 },
  ]
  return getResult(key, value, arr)
}

// dm 人员类型
export function getPersonnelType(key, value) {
  const arr = [
    { label: '员工', value: 1 },
    { label: '用工', value: 2 },
    { label: '专员', value: 3 },
    { label: '患者', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 优先级
export function getPriority(key, value) {
  const arr = [
    { label: '较低', value: 1, style: "color:rgb(64, 158, 255)" },
    { label: '普通', value: 2, style: "color:rgb(235, 181, 99)" },
    { label: '紧急', value: 3, style: "color:rgb(247, 137, 137)" },
    { label: '非常紧急', value: 4, style: "color:rgb(176,7,142)" },
  ]
  return getResult(key, value, arr)
}

// 任务类型
export function getTaskType(key, value) {
  const arr = [
    // { label: '推广类', value: 1 },
    { label: '维护类', value: 2 },
    { label: '问卷类', value: 3 },
    { label: '征集类', value: 4 },
    { label: '知识付费类', value: 5 },
    { label: '拜访类', value: 6 },
    { label: '自营地推类（旧）', value: 7 },
    { label: '推广类', value: 8 },
    { label: '提现', value: 9 },
    { label: '平账', value: 10 },
    { label: '提现退回', value: 11 },
    { label: '自营地推类（新）', value: 12 },
    { label: '营销策划', value: 14 },
    { label: '社群顾问类型', value: 15 },
    { label: '科普活动（旧）', value: 16 },
    
    { label: '现场指引', value: 17 },
    { label: '项目准入', value: 18 },
    { label: '业务开拓', value: 19 },
    { label: '音视频制作', value: 20 },
    { label: '内容策划', value: 21 },
    { label: '产品测试', value: 22 },
    { label: '产品运维', value: 23 },
    { label: '终端拜访', value: 24 },
    { label: '地推打卡', value: 25 },
    { label: '科普活动', value: 26 },
    { label: '线上推广', value: 27 },
    { label: '技术服务', value: 28 },
    { label: '科普帖子', value: 29 },
    { label: '线上用户活动', value: 30 },
    { label: '科普笔记', value: 31 },
    { label: '线下用户活动', value: 32 },
  ]
  return getResult(key, value, arr)
}

// 接收对象类型
export function getReceiveType(key, value) {
  const arr = [
    { value: 1, label: '全部' },
    { value: 2, label: '定向' },
  ]
  return getResult(key, value, arr)
}

// 横幅使用类型
export function getBannerUseType(key, value) {
  const arr = [
    { value: 1, label: '社区端首页' },
    { value: 2, label: '医师端知识页' },
    { value: 3, label: '其他业务员类型拓展' },
    { value: 4, label: '众包联盟' },
    { value: 5, label: '社区端-个人中心' },
    { value: 6, label: '用药说明书-首页' },
    { value: 7, label: '社区端交流页' },
    { value: 8, label: '陪诊-首页' },
    { value: 9, label: '社区端-文章详情页尾部' },
    { value: 10, label: '健康自测-顶部' },
    { value: 11, label: '签到-顶部' },
  ]
  return getResult(key, value, arr)
}

// 横幅跳转类型
export function getBannerSkipType(key, value) {
  const arr = [
    { value: 1, label: '静态' },
    { value: 2, label: '跳内部页面' },
    { value: 3, label: '跳Web地址' },
    { value: 4, label: '跳外部小程序' },
  ]
  return getResult(key, value, arr)
}

// 任务奖励费类型
export function getIncentivefeeType(key, value) {
  const arr = [
    { value: 1, label: '自营' },
    { value: 2, label: '第三方' },
  ]
  return getResult(key, value, arr)
}

// 任务奖励费启用状态
export function getIncentivefeeEnableStatus(key, value) {
  const arr = [
    { value: 1, label: '是' },
    { value: 2, label: '否' },
  ]
  return getResult(key, value, arr)
}

// 文档视频资料公开状态
export function getDocumentOpenStatus(key, value) {
  const arr = [
    { value: 1, label: '是' },
    { value: 2, label: '否' },
  ]
  return getResult(key, value, arr)
}

// 文档视频资料类型
export function getDocumentType(key, value) {
  const arr = [
    { value: 1, label: '文档' },
    { value: 2, label: '视频' },
  ]
  return getResult(key, value, arr)
}

// 文档视频资料来源类型
export function getDocumentSourceType(key, value) {
  const arr = [
    { value: 1, label: '任务' },
    { value: 2, label: '其他' },
  ]
  return getResult(key, value, arr)
}

// 文档视频资料发起类型
export function getDocumentInitiatorType(key, value) {
  const arr = [
    { value: 1, label: '人工' },
    { value: 2, label: '系统' },
  ]
  return getResult(key, value, arr)
}

// 需求状态
export function getDemandStatus(key, value) {
  const arr = [
    { value: 1, label: '未开始' },
    { value: 2, label: '进行中' },
    { value: 3, label: '已完成' },
    { value: 4, label: '已取消' },
  ]
  return getResult(key, value, arr)
}

// 问卷活动运行状态
export function getRunStatus(key, value) {
  const arr = [
    { value: 1, label: '草稿' },
    { value: 2, label: '回收中' },
    { value: 3, label: '停止' },
  ]
  return getResult(key, value, arr)
}

// 内容审阅状态
export function getReviewStatus(key, value) {
  const arr = [
    { value: 1, label: '草稿' },
    { value: 2, label: '审阅中' },
    { value: 3, label: '已结束' },
    { value: 4, label: '已取消' },
  ]
  return getResult(key, value, arr)
}

// 通知-置顶状态
export function getTopStatus(key, value) {
  const arr = [
    { value: 1, label: '是' },
    { value: 2, label: '否' }
  ]
  return getResult(key, value, arr)
}

// 入库类型
export function getInStoreProcessType(key, value) {
  const arr = [
    { label: '销售入库', value: 230011 },
    { label: '免费入库', value: 230012 },
    { label: '采购入库', value: 230013 },
    { label: '样品入库', value: 11 },
    { label: '租凭入库', value: 14 },
    { label: '报废入库', value: 15 },
    { label: '押金入库', value: 16 },
    { label: '退货入库', value: 19 },
  ]
  return getResult(key, value, arr)
}

// 通知-触发类型
export function getTouchType(key, value) {
  const arr = [
    { value: 1, label: '人工发送' },
    { value: 2, label: '系统发送' }
  ]
  return getResult(key, value, arr)
}

// 出库类型
export function getOutStoreProcessType(key, value) {
  const arr = [
    { label: '销售出库', value: 230021 },
    { label: '免费出库', value: 230022 },
    { label: '报废出库', value: 230023 },
    { label: '押金出库', value: 230024 },
    { label: '样品出库', value: 230025 },
    { label: '赠送出库', value: 230026 },
    { label: '测试出库', value: 230027 },
    { label: '盘亏', value: 230028 },
    { label: '盘盈', value: 230029 },
    { label: '采购出库', value: 230013 },

    { label: '手动', value: 3 },
    { label: '换货', value: 4 },
    { label: '退款', value: 5 },
    { label: '销售', value: 6 },
    { label: '补库', value: 7 },
    { label: '其他', value: 8 },
    { label: '调库', value: 9 },
    { label: '租赁出库', value: 10 },
    { label: '样品入库', value: 11 },
    // { label: '盘盈入库', value: 12 },
    // { label: '盘亏入库', value: 13 },
    { label: '报废出库', value: 14 },
  ]
  return getResult(key, value, arr)
}

// 通知-发布状态
export function getPushStatus(key, value) {
  const arr = [
    { value: 1, label: '待发布' },
    { value: 2, label: '已发布' },
    { value: 3, label: '已下架' },
  ]
  return getResult(key, value, arr)
}

// 通知-阅读状态
export function getReadStatus(key, value) {
  const arr = [
    { value: 1, label: '已读' },
    { value: 2, label: '未读' },
  ]
  return getResult(key, value, arr)
}

// 结转类型
export function getSharecarryforwardType(key, value) {
  const arr = [
    { label: '销售结转', value: 1 },
    { label: '免费结转', value: 2 },
  ]
  return getResult(key, value, arr)
}

// dm 终端类型
export function getTerminalType(key, value) {
  const arr = [
    { label: '社区端', value: 1 },
    { label: '医师端', value: 2 },
    { label: '专员端', value: 3 },
  ]
  return getResult(key, value, arr)
}

// dm 资讯审核状态
export function getProcessStatus(key, value) {
  const arr = [
    { label: '未审核', value: 1 },
    { label: '通过', value: 2 },
    { label: '退回', value: 3 },
    { label: '待提审', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 资讯内容类型
export function getInformationType(key, value) {
  const arr = [
    { label: '文章', value: 1 },
    { label: '视频', value: 2 },
    { label: '课件展示', value: 3 },
    { label: '链接', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 用工认证状态
export function getAuthStatus(key, value) {
  const arr = [
    { label: '未认证', value: 1 },
    { label: '认证成功', value: 2, default: true },
    { label: '认证失败', value: 3 },
  ]
  return getResult(key, value, arr)
}

// 用工注册来源
export function getRegisterSource(key, value) {
  const arr = [
    { label: '自主注册', value: 1 },
    { label: '邀约注册', value: 2 },
    { label: '管理员添加', value: 3 },
    { label: '活动添加', value: 4 }
  ]
  return getResult(key, value, arr)
}

// 用工档案职业类型
export function getDmJobType(key, value) {
  const arr = [
    { label: '自然人', value: 1 },
    { label: '医师', value: 2 },
    { label: '代理商代表', value: 3 },
    { label: '地推打卡人员', value: 4 },
  ]
  return getResult(key, value, arr)
}


// 患者家庭关系
export function getFamilyTies(key, value) {
  const arr = [
    { label: '配偶', value: 1 },
    { label: '子女', value: 2 },
    { label: '父母', value: 3 },
    { label: '兄弟', value: 4 },
    { label: '姐妹', value: 5 },
    { label: '其他', value: 6 },
  ]
  return getResult(key, value, arr)
}

// 患者成员名称
export function getMemberType(key, value) {
  const arr = [
    { label: '普通成员', value: 1 },
    { label: '新生儿', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 模板类型
export function getTemplateType(key, value) {
  const arr = [
    { label: '文档模板', value: 1 },
    { label: '表单模板', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 审核类型
export function getAuditType(key, value) {
  const arr = [
    { label: '普通审核', value: 1 },
    { label: '自动审核', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 病例征集运行状态
export function getCaseCollectRunStatus(key, value) {
  const arr = [
    { label: '草稿', value: 1 },
    { label: '进行中', value: 2 },
    { label: '已结束', value: 3 },
    // { label: '已取消', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 审核记录 审核状态
export function getAuditlogStatus(key, value) {
  const arr = [
    { label: '待审核', value: 1 },
    { label: '审核通过', value: 2 },
    { label: '审核驳回', value: 3 },
    { label: '待提审', value: 4 },
    { label: '待建多级审核', value: 5 },
  ]
  return getResult(key, value, arr)
}

// 任务管理最后执行状态
export function getTasksLastDisposeType(key, value) {
  const arr = [
    { label: '发起', value: 1 },
    { label: '待处理', value: 2, style: "color:rgb(207,207,196)" },
    { label: '处理中', value: 3, style: "color:rgb(102, 177, 255)" },
    { label: '已处理', value: 4, style: "color:rgb(103, 194, 58)" },
    { label: '已关闭', value: 5, style: "color:#056608" },
    { label: '已撤销', value: 6, style: "color:rgb(169,169,169)" },
    { label: '已转办', value: 7 },
    { label: '已拒绝', value: 8 },
    { label: '已确认完成', value: 9 },
  ]
  return getResult(key, value, arr)
}

// 任务分配来源
export function getTasksSourceType(key, value) {
  const arr = [
    { label: '直派', value: 1 },
    { label: '转派', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 财务管理用户类型
export function getFinanceUserType(key, value) {
  const arr = [
    { label: '企业', value: 1 },
    { label: '患者', value: 2 },
    { label: '医师', value: 3 },
    { label: '专员', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 科目类型
export function getBusinessType() {
  return getTaskType.apply(this, arguments)
}

// 结算状态
export function getSettleStatus(key, value) {
  const arr = [
    { label: '未结算', value: 1 },
    { label: '已结算', value: 2 },
    { label: '撤回', value: 3 },
  ]
  return getResult(key, value, arr)
}

// 交易类型
export function getDealType(key, value) {
  const arr = [
    { label: '收入', value: 1 },
    { label: '支出', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 提现审核状态
export function getWithdrawalAuditStatus(key, value) {
  const arr = [
    { label: '待审核', value: 1 },
    { label: '审核通过', value: 2 },
    { label: '审核驳回', value: 3 },
  ]
  return getResult(key, value, arr)
}

// 提现支付渠道
export function getPayChannel(key, value) {
  const arr = [
    { label: '线下转账', value: 1 },
    { label: '大熊', value: 2 },
    { label: '融华云-万古', value: 3 },
    { label: '河南银景', value: 4 },
    { label: '海南鑫税通', value: 5 },
    { label: '慧用工', value: 6 },
    { label: '焕强信息', value: 7 },
    { label: '奔奔', value: 8 },
    // { label: '金财宽裕', value: 9 },
    { label: '天津添跃', value: 10 },
    { label: '契约锁', value: 11 },
    { label: '天津持辉', value: 12 },// 天津添跃
    { label: '天津添顺', value: 13 },
    { label: '天津晨景', value: 14 },
    { label: '天津悦智', value: 15 },
    { label: '天津翔禾', value: 16 },
    { label: '天津雅钧', value: 17 },
    { label: '天津博晟', value: 18 },
    { label: '云账户', value: 19 },
    { label: '深圳之光', value: 20 }
    
  ]
  return getResult(key, value, arr)
}

// 提现支付状态
export function getTransactionStatus(key, value) {
  const arr = [
    { label: '待回调', value: 1 },
    { label: '交易成功', value: 2 },
    { label: '交易失败', value: 3 },
  ]
  return getResult(key, value, arr)
}


//系统类型
export function getSystemType(key, value) {
  const arr = [
    { label: '业务', value: 1 },
    { label: '系统', value: 2 },


  ]
  return getResult(key, value, arr)
}

// dm社区端 操作类型
export function getOperationType(key, value) {
  const arr = [
    { label: '访问', value: 1 },
    { label: '点击', value: 2 },
    { label: '长按', value: 3 },
    { label: '关注', value: 4 },
    { label: '取关', value: 5 },
    { label: '点赞', value: 6 },
    { label: '收藏', value: 7 },
    { label: '删除评论', value: 8 },
    { label: '取消点赞', value: 9 },
    { label: '取消收藏', value: 10 },
    { label: '阅读', value: 11 },
  ]
  return getResult(key, value, arr)
}

// dm社区业务类型
export function getDmCommunityBusinessType(key, value) {
  const arr = [
    { label: '横幅', value: 1 },
    { label: '外部服务应用', value: 2 },
    { label: '企业微信群', value: 3 },
    { label: '圈子', value: 4 },
    { label: '帖子', value: 5 },
    { label: '评论', value: 6 }
  ]
  return getResult(key, value, arr)
}

// 应用服务类型
export function getAppServeType(key, value) {
  const arr = [
    { label: '小程序', value: 1 },
    { label: 'H5', value: 2 }
  ]
  return getResult(key, value, arr)
}

// 应用服务显示位置
export function getIndexPathType(key, value) {
  const arr = [
    { label: '首页', value: 1 },
    { label: '圈子', value: 2 },
  ]
  return getResult(key, value, arr)
}

// dm社区端用户类型
export function getDmCommunityPostsSourceType(key, value) {
  const arr = [
    { label: '活跃用户', value: 1 },
    { label: '用户', value: 2 },
    { label: '平台', value: 3 }
  ]
  return getResult(key, value, arr)
}

// dm社区端帖子类型
export function getDmCommunityPostsType(key, value) {
  const arr = [
    { label: 'PGC', value: 1 },
    { label: 'UGC', value: 2 },
  ]
  return getResult(key, value, arr)
}

// dm社区粉丝新增渠道
export function getDmCommunityFansAddSource(key, value) {
  const arr = [
    { label: '小程序', value: 1 },
    { label: '后台手动新增', value: 2 },
    { label: '后台批量生成', value: 3 },
  ]
  return getResult(key, value, arr)
}

// dm社区粉丝新增渠道
export function getDmCommunityOpenStatus(key, value) {
  const arr = [
    { value: 1, label: '启用' },
    { value: 2, label: '禁用' }
  ]
  return getResult(key, value, arr)
}

// dm社区马甲规则类型
export function getDmCommunitySockpuppetruleType(key, value) {
  const arr = [
    { value: 1, label: '评论', permission:'dm_community_sockpuppet_rule_reviews' },
    { value: 2, label: '点赞', permission:'dm_community_sockpuppet_rule_praise' },
    { value: 3, label: '收藏', permission:'dm_community_sockpuppet_rule_collection' },
    { value: 4, label: '阅读', permission:'dm_community_sockpuppet_rule_read' },
    { value: 5, label: '分享', permission: '' },
  ]
  return getResult(key, value, arr)
}

// dm社区马甲规则触发类型
export function getDmCommunityFrequencyConfig(key, value) {
  const arr = [
    { value: 1, label: '发表首次' },
    { value: 2, label: '人工触发' },
  ]
  return getResult(key, value, arr)
}

// dm社区评论审核状态
export function getDmCommunityProcessStatus(key, value) {
  const arr = [
    { value: 1, label: '未审核', default: true },
    { value: 2, label: '通过' },
    { value: 3, label: '退回' },
    { value: 4, label: '提审' },
  ]
  return getResult(key, value, arr)
}

// dm社区评论发布状态
export function getDmCommunityPutawayStatus(key, value) {
  const arr = [
    { value: 1, label: '上架' },
    { value: 2, label: '下架' },
    { value: 3, label: '限流' },
    { value: 4, label: '删除' },
    { value: 5, label: '待上架' },
  ]
  return getResult(key, value, arr)
}

// dm社区评论点赞流水操作类型
export function getDmCommunityCommentLikeFlowType(key, value) {
  const arr = [
    { value: 1, label: '访问' },
    { value: 2, label: '点击' },
    { value: 3, label: '长按' },
    { value: 6, label: '点赞' },
    { value: 7, label: '收藏' },
    { value: 8, label: '删除评论' },
    { value: 9, label: '取消点赞' },
    { value: 10, label: '取消收藏' },
    { value: 12, label: '新增评论' },
    { value: 13, label: '分享' },
    // { value: 14, label: '许愿池点亮' },
    // { value: 15, label: '许愿池许愿' },
    { value: 16, label: '删除帖子' },
  ]
  return getResult(key, value, arr)
}

// dm社区敏感词级别
export function getDmCommunityLevel(key, value) {
  const arr = [
    { value: 1, label: '替换成***' },
    { value: 2, label: '提交拦截' },
  ]
  return getResult(key, value, arr)
}

// dm社区运营配置热度配置执行状态
export function getDmCommunityExecuteStatus(key, value) {
  const arr = [
    { value: 1, label: '待执行' },
    { value: 2, label: '执行中' },
    { value: 3, label: '执行完成' },
    { value: 4, label: '取消执行' },
  ]
  return getResult(key, value, arr)
}

// dm社区敏感词触发位置
export function getDmCommunityTriggerType(key, value) {
  const arr = [
    { value: 1, label: '帖子正文' },
    { value: 2, label: '帖子标题' },
    { value: 3, label: '评论' },
    { value: 4, label: '昵称' },
    { value: 5, label: '许愿' },
    { value: 6, label: '其他' },
  ]
  return getResult(key, value, arr)
}

// dm社区帖子发布状态
export function getDmCommunityPostsPutawayStatus(key, value) {
  const arr = [
    { value: 1, label: '上架' },
    { value: 2, label: '下架' },
    { value: 3, label: '限流' },
    { value: 4, label: '删除' },
    { value: 5, label: '待上架' },
    { value: 6, label: '已取消' }
  ]
  return getResult(key, value, arr)
}

// dm社区帖子审核状态
export function getDmCommunityPostsProcessStatus(key, value) {
  const arr = [
    { value: 1, label: '未审核', default: true },
    { value: 2, label: '通过' },
    { value: 3, label: '退回' },
    { value: 4, label: '提审' },
  ]
  return getResult(key, value, arr)
}

// dm社区小程序渠道链访问类型
export function getDmCommunityChannelUrlVisitType(key, value) {
  const arr = [
    { value: 1, label: '进入' },
    { value: 2, label: '离开' },
    { value: 3, label: '显示' },
  ]
  return getResult(key, value, arr)
}

// dm云课堂直播状态
export function getDmMettingActivityStatus(key, value) {
  const arr = [
    { value: 1, label: '草稿' },
    { value: 2, label: '预告' },
    { value: 3, label: '直播中' },
    { value: 4, label: '直播结束' },
    { value: 5, label: '回放' },
    { value: 6, label: '下架' },
  ]
  return getResult(key, value, arr)
}

// dm云课堂互动状态
export function getDmMettingInteractType(key, value) {
  const arr = [
    { value: 1, label: '图文介绍' },
    { value: 2, label: '附件' },
    { value: 3, label: '聊天室' },
    { value: 4, label: '视频回放' },
    { value: 5, label: '问卷活动' },
    { value: 6, label: '病例征集' },
  ]
  return getResult(key, value, arr)
}

// dm云课堂观看来源
export function getDmMettingSource(key, value) {
  const arr = [
    { value: 1, label: 'PC' },
    { value: 2, label: '移动端' },
  ]
  return getResult(key, value, arr)
}

// dm企业门户栏目内容类型
export function getDmEipCloumnContentType(key, value) {
  const arr = [
    { value: 1, label: '横幅' },
    { value: 2, label: '图文' },
    { value: 3, label: '云课堂' },
    { value: 4, label: '云课堂回放' },
    { value: 5, label: '问卷活动' },
    { value: 6, label: '病例征集' },
  ]
  return getResult(key, value, arr)
}

// dm社区帖子发布状态
export function getDmCommunityTimingStatus(key, value) {
  const arr = [
    { value: 1, label: '未开始' },
    { value: 2, label: '定时中' },
    { value: 3, label: '定时结束' }
  ]
  return getResult(key, value, arr)
}


// 马甲规则管理触发类型 frequencyConfig 触发类型:1-发表首次，2-人工触发
export function getFrequencyConfig(key, value) {
  const arr = [
    { value: 1, label: '发表首次' },
    { value: 2, label: '人工触发' },
    // { value: 3, label: '定时结束' }
  ]
  return getResult(key, value, arr)
}

// dm病例征集流程类型
export function getDmCaseFlowType(key, value) {
  const arr = [
    { value: 1, label: '文档模板' },
    { value: 2, label: '表单模板' },
    { value: 3, label: '患者登记' }
  ]
  return getResult(key, value, arr)
}

// 帖子审批业务类型
export function getauditLogBusinessType(key, value) {
  const arr = [
    { value: 1, label: '帖子审核' },
    { value: 2, label: '评论审核' },
    // { value: 3, label: '患者登记' }
  ]
  return getResult(key, value, arr)
}

// 帖子审批类型列表
export function getpostsListType(key, value) {
  const arr = [
    { value: 1, label: '未审核' },
    { value: 2, label: '通过', default: true },
    { value: 3, label: '退回' },
    // { value: 4, label: '提审' },
  ]
  return getResult(key, value, arr)
}

// 云课堂直播类型 :新增3云课堂、7直播活动” businessType   tinyint(1) comment '业务类型:新增云课堂、直播活动',
export function getCommonBusinessType(key, value) {
  const arr = [
    { value: 3, label: '云课堂' },
    { value: 7, label: '直播活动', },
    { value: 8, label: '药品说明书-名医直播', },
    // { value: 3, label: '退回' },
    // { value: 4, label: '提审' },
  ]
  return getResult(key, value, arr)
}

// 马甲规则任务执行状态 执行状态，1-开启，2-取消
export function getExecuteStatus(key, value) {
  const arr = [
    { value: 1, label: '待执行', },
    { value: 2, label: '执行中', },
    { value: 3, label: '执行成功', },
    { value: 4, label: '取消执行', },
    // { value: 3, label: '退回' },
    // { value: 4, label: '提审' },
  ]
  return getResult(key, value, arr)
}
// 马甲规则任务问卷类型 执行状态，3 精准地推，6 线上推广
export function getCollectionType(key, value) {
  const arr = [
    { value: 3, label: '精准地推', },
    { value: 6, label: '线上推广', },
  ]
  return getResult(key, value, arr)
}

// 用工档案签约状态
export function getSigning(key, value) {
  const arr = [
    { label: '未签约', value: 1 },
    { label: '已签约', value: 2 }
  ]
  return getResult(key, value, arr)
}

// 用工档案认证状态
export function getJobAuthStatusList(key, value) {
  const arr = [
    { label: '已认证', value: 1 },
    { label: '未认证', value: 2 },
    { label: '认证中', value: 3 },
    { label: '认证撤回', value: 4 },
    { label: '认证驳回', value: 5 },
  ]
  return getResult(key, value, arr)
}


// 获取直播类型配置
export function getscreenDirectionType(key, value) {
  const arr = [
    { label: '竖屏', value: 1 },
    { label: '横屏', value: 2 }
  ]
  return getResult(key, value, arr)
}


// 云课堂添加事件类型
export function getEnventType(key, value) {
  const arr = [
    { label: '点赞', value: 2 },
    { label: '收藏', value: 3 },
    { label: '阅读', value: 4 },
    { label: '分享', value: 5 },
    { label: '预约', value: 6 },
  ]
  return getResult(key, value, arr)
}

// 云课堂添加事件来源
export function getEnventSource(key, value) {
  const arr = [
    { label: '活跃用户', value: 1 },
    { label: '用户', value: 2 },
    // { label: '阅读', value: 4 },
    // { label: '分享', value: 5 },
    // { label: '预约', value: 6 },
  ]
  return getResult(key, value, arr)
}




// 云课堂直播活动马甲生成规则梯度类型
export function getLiveStepType(key, value) {
  const arr = [
    { label: '排队', value: 1 },
    { label: '随机', value: 2 },
    // { label: '阅读', value: 4 },
    // { label: '分享', value: 5 },
    // { label: '预约', value: 6 },
  ]
  return getResult(key, value, arr)
}


// 云课堂直播活动马甲生成规则枚举类型
export function getLiveType(key, value) {
  const arr = [
    { label: '点赞', value: 2 },
    // { label: '分享', value: 3 },
    { label: '预约', value: 6 },
    { label: '访问', value: 7 },
    // { label: '阅读', value: 4 },
    // { label: '分享', value: 5 },
    // { label: '预约', value: 6 },
  ]
  return getResult(key, value, arr)
}



// 马甲类型 2点赞3分享6预约7问
export function getMJType(key, value) {
  const arr = [
    { label: '点赞', value: 2 },
    { label: '分享', value: 3 },
    { label: '预约', value: 6 },
    { label: '访问', value: 7 },
    { label: '粉丝', value: 5 },
    // { label: '阅读', value: 4 },
    // { label: '分享', value: 5 },
    // { label: '预约', value: 6 },
  ]
  return getResult(key, value, arr)
}

// 设备二维码结构
export function getQrCodeStructureType(key, value) {
  const arr = [
    { value: 1, label: '堆叠' },
    { value: 2, label: '并列' },
    { value: 3, label: '同码关联' },
  ]
  return getResult(key, value, arr)
}
// 拜访计划完成状态
export function getVisitingProjectStatus(key, value) {
  const arr = [
    { value: 1, label: '未开始' },
    { value: 2, label: '进行中' },
    { value: 3, label: '已完成' },
    { value: 4, label: '已取消' }
  ]
  return getResult(key, value, arr)
}

// 拜访计划发布状态
export function getVisitingPushStatus(key, value) {
  const arr = [
    { value: 1, label: '草稿' },
    { value: 2, label: '已发布' },
    { value: 3, label: '已撤销' }
  ]
  return getResult(key, value, arr)
}

// 通用签到类型
export function getSigninlogType(key, value) {
  const arr = [
    { value: 1, label: '签入' },
    { value: 2, label: '签出' },
    { value: 4, label: '接到客户' },
  ]
  return getResult(key, value, arr)
}
// 设备二维码样式
export function getCodeStyle(key, value) {
  const arr = [
    { value: 1, label: '非合成码' },
    { value: 2, label: '合成双码' },
    { value: 3, label: '灯火合成码' },
  ]
  return getResult(key, value, arr)
}

// 领袋流程模式
export function getPacketAuthType(key, value) {
  const arr = [
    { value: 1, label: '先授权' },
    { value: 2, label: '后授权' },
  ]
  return getResult(key, value, arr)
}

// dm定向属性枚举
export function getOrientParams(key, value) {
  const arr = [
    { value: 1, label: '内部运营' },
    { value: 2, label: '广告' },
    { value: 3, label: '企业' },
    { value: 4, label: '真实用户' },
  ]
  return getResult(key, value, arr)
}

// dm社区粉丝加v类型
export function getFansVType(key, value) {
  const arr = [
    { value: 1, label: '医生' },
    { value: 2, label: '企业' },
    { value: 3, label: 'KOC' },
    { value: 4, label: '服务商' }
  ]
  return getResult(key, value, arr)
}

// 灯火投放计划舆情等级
export function getDhPublicOpinionLevel(key, value) {
  const arr = [
    { value: 'low_risk', label: '低风险' },
    { value: 'middle_risk', label: '中风险' },
    { value: 'high_risk', label: '高风险' },
  ]
  return getResult(key, value, arr)
}

// 灯火投放计划医院类型
export function getDhOrganizationSubType(key, value) {
  const arr = [
    { value: 'general_hospital', label: '综合型医院' },
    { value: 'women_hospital', label: '妇产科医院' },
    { value: 'children_hospital', label: '儿童医院' },
    { value: 'cardiovascular_hospital', label: '心血管医院' },
  ]
  return getResult(key, value, arr)
}

// 灯火投放计划设备位置
export function getDhDeviceLocation(key, value) {
  const arr = [
    { value: 'inpatient_department', label: '住院部' },
    { value: 'outpatient_department', label: '门诊部' },
  ]
  return getResult(key, value, arr)
}

// 灯火投放计划活动类目
export function getDhActivityType(key, value) {
  const arr = [
    { value: 1, label: '赠险' },
    { value: 2, label: '小荷包' },
  ]
  return getResult(key, value, arr)
}

// 云课堂聊天室聊天定时状态
export function getDmChatTimingStatus(key, value) {
  const arr = [
    { value: 1, label: '未开始' },
    { value: 2, label: '定时中' },
    { value: 3, label: '定时结束' },
    { value: 4, label: '取消执行' },
  ]
  return getResult(key, value, arr)
}

// 云课堂聊天室聊天上下架状态
export function getDmChatPutawayStatus(key, value) {
  const arr = [
    { value: 1, label: '上架' },
    { value: 2, label: '下架' },
    { value: 5, label: '待上架' },
    { value: 6, label: '取消' },
  ]
  return getResult(key, value, arr)
}



// 引流号成员管理成员类型
export function getPlatFormPersonMemberType(key, value) {
  const arr = [
    { value: 1, label: '企业号成员' },
    { value: 2, label: '个微成员' },
    { value: 3, label: '个微群成员' },
  ]
  return getResult(key, value, arr)
}


// 引流号成员管理对接类型
export function getPlatFormPersonMemberExternalType(key, value) {
  const arr = [
    { value: 1, label: '非外部对接' },
    { value: 2, label: '外部对接' },
  ]
  return getResult(key, value, arr)
}

// 引流号成员管理启动状态(1待启动,2启动,3停止)
export function getPlatFormPersonMemberStartStatus(key, value) {
  const arr = [
    { value: 1, label: '待启动' },
    { value: 2, label: '启动' },
    { value: 3, label: '停止' },
  ]
  return getResult(key, value, arr)
}

// 引流号成员管理状态 1=正常 2=停用 -1=软删除
export function getPlatFormPersonMemberStatus(key, value) {
  const arr = [
    { value: 1, label: '正常' },
    { value: 2, label: '停用' },
    { value: -1, label: '软删除' },
  ]
  return getResult(key, value, arr)
}

// 领袋广告页客户端类型
export function getAdvertisePlanClientType(key, value) {
  const arr = [
    { label: 'H5', value: 1 },
    { label: '自营小程序', value: 2 },
    { label: '第三方小程序', value: 3 },
    { label: '接口链接', value: 4 },
  ]
  return getResult(key, value, arr)
}


// 引流号管理添加订阅消息管理 - 推送结果状态
export function getSubscribePushResultStatus(key, value) {
  // 1-成功，2-，3-待发起"
  const arr = [
    { label: '成功', value: 1 },
    { label: '失败', value: 2 },
    { label: '待发起', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 引流号管理添加订阅消息管理 - 订阅状态
export function getSubscribeStatus(key, value) {
  const arr = [
    { label: '用户同意订阅该条id对应的模板消息', value: 'accept' },
    { label: '用户拒绝订阅该条id对应的模板消息', value: 'reject' },
    { label: '已被后台封禁', value: 'ban' },
    { label: '用户接收订阅消息并开启了语音提醒', value: 'acceptWithAudio' },
    { label: '模板因为模板标题同名被后台过滤', value: 'filter' },
  ]
  return getResult(key, value, arr)
}

// 企微许可证-账号状态
export function getWxActiveInfoStatus(key, value) {
  const arr = [
    { label: '未绑定', value: 1 },
    { label: '已绑定且有效', value: 2 },
    { label: '已过期', value: 3 },
    { label: '待转移', value: 4 },
    { label: '已合并', value: 5 },
    { label: '已分配给下游', value: 6 },
  ]
  return getResult(key, value, arr)
}

// 企微许可证-账号类型
export function getWxActiveInfoType(key, value) {
  const arr = [
    { label: '基础帐号', value: 1 },
    { label: '互通帐号', value: 2 }
  ]
  return getResult(key, value, arr)
}

// dm 项目列表项目类型 getDmProjessTypeArr
export function getDmProjessTypeArr(key, value) {
  const arr = [
    { label: '默认', value: 1 },
    { label: '小葫芦', value: 2 },
    { label: '地推打卡', value: 3}
  ]
  return getResult(key, value, arr)
}

// 财务明细表、提现列表商务类型
export function getOperatorType(key, value) {
  const arr = [
    { label: '经销商', value: 1 },
    { label: '渠道商', value: 2 }
  ]
  return getResult(key, value, arr)
}

// 用工类型入口类型
export function getUserInType(key, value) {
  const arr = [
    { label: '用户', value: 1 },
    { label: '马甲', value: 2 }
  ]
  return getResult(key, value, arr)
}

// 帖子邀请评论回复状态
export function getInviteReplyStatus(key, value) {
  const arr = [
    { label: '已回复', value: 1 },
    { label: '待回复', value: 2 }
  ]
  return getResult(key, value, arr)
}

// 诊后点评
export function getHospitalOpenStatusList(key, value) {
  const arr = [
    {
      label: '开',
      value: 1
    }, {
      label: '关',
      value: 2
    }
  ]
  return getResult(key, value, arr)

}


// 引流号列表群管理
// 运营状态:1-正常，2-解散
export function getOperationalStatus(key, value) {
  const arr = [
    {
      label: '正常',
      value: 1
    }, {
      label: '解散',
      value: 2
    }
  ]
  return getResult(key, value, arr)

}

// 引流号企微新增分组类型
export function getWeChatGroupType(key, value) {
  const arr = [
    {
      label: '第三方-企微成员',
      value: 1
    }, {
      label: '第三方-企微群',
      value: 2
    }, {
      label: '自营-个微',
      value: 3
    }, {
      label: '自营-视频号',
      value: 5
    }
  ]
  return getResult(key, value, arr)

}


// 引流号订阅消息 业务类型
export function getSubscribeBusinessTypeType(key, value) {
  const arr = [
    {
      label: '小葫芦直播活动 - 预约',
      value: 1
    }, {
      label: '小葫芦帖子 - 点赞',
      value: 2
    }, {
      label: '小葫芦帖子 - 评论',
      value: 3
    }, {
      label: '小葫芦帖子 - 回复评论',
      value: 4
    }, {
      label: '小葫芦诊后 - 订阅反馈',
      value: 5
    }, {
      label: '小葫芦小程序出袋-（一次性订阅）',
      value: 6
    }, {
      label: '健康自测',
      value: 7
    }, {
      label: '每日辟谣',
      value: 8
    }, {
      label: '取袋H5-（服务号长期订阅）',
      value: 9
    }, {
      label: '小葫芦小程序出袋-（长期订阅）',
      value: 10
    }
  ]
  return getResult(key, value, arr)

}

// 创作者计划申请审核状态
export function getFramerplanAuditStatus(key, value) {
  const arr = [
    { label: '待审核', value: 1 },
    { label: '审核通过', value: 2 },
    { label: '审核驳回', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 合同状态
export function getDmContractStatus(key, value) {
  const arr = [
    { value: 'DRAFT', label: '草稿' },
    { value: 'RECALLED', label: '已撤回' },
    { value: 'SIGNING', label: '签署中' },
    { value: 'REJECTED', label: '已退回' },
    { value: 'COMPLETE', label: '已完成' },
    { value: 'EXPIRED', label: '已过期' },
    { value: 'FILLING', label: '拟定中' },
    { value: 'INVALIDING', label: '作废中' },
    { value: 'INVALIDED', label: '已作废' },
  ]
  return getResult(key, value, arr)
}

// 合同签约状态
export function getDmSignedStatus(key, value) {
  const arr = [
    { value: 0, label: '等待回调' },
    { value: 1, label: '成功' },
    { value: 2, label: '失败' },
  ]
  return getResult(key, value, arr)
}

// 合同类型
export function getDmContractType(key, value) {
  const arr = [
    { value: 1, label: '首次签约' },
  ]
  return getResult(key, value, arr)
}

// sop退货流水审核状态
export function getReturngoodLogAuditStatus(key, value) {
  const arr = [
    { label: '待审核', value: 1 },
    { label: '审核通过', value: 2 },
    { label: '审核驳回', value: 3 }
  ]
  return getResult(key, value, arr)
}


// 计费流水类型
export function getTurnOverTypeList(key, value) {
  const arr = [
    { value: 1, label: '粉丝引流' },
    { value: 2, label: '广告' },
    { value: 3, label: '群引流' },
    { value: 4, label: '合作商大号引流' },
  ]
  return getResult(key, value, arr)

}


// 引流号客户群列表渠道类型
export function getCustomChannelType(key, value) {
  const arr = [
    { value: 1, label: '绿葆领袋', style: "color:#f56c6c" },
    { value: 2, label: '其他未知' },
  ]
  return getResult(key, value, arr)

}


// 群成员成员类型
export function getCrowdMemberType(key, value) {
  const arr = [
    { value: 1, label: '企业成员' },
    { value: 2, label: '外部联系人' },
  ]
  return getResult(key, value, arr)

}

// 群成员入群方式 joinSceneText
export function getCrowdJoinSceneType(key, value) {
  const arr = [
    { value: 1, label: '由群成员邀请入群（直接邀请入群）' },
    { value: 2, label: '由群成员邀请入群（通过邀请链接入群）' },
    { value: 3, label: '通过扫描群二维码入群' },
  ]
  return getResult(key, value, arr)

}


// 客户群 投放状态
export function getCustomPutStatusList(key, value) {
  const arr = [
    { value: 1, label: '开' },
    { value: 2, label: '关' },
  ]
  return getResult(key, value, arr)

}

// 马甲规则模板类型
export function getDmSockpuppetRuleTemplateType(key, value) {
  const arr = [
    { value: 1, label: '通用' },
    { value: 2, label: '定向' },
  ]
  return getResult(key, value, arr)

}

// 马甲规则-规则类型
export function getDmSockpuppetRuleRuleType(key, value) {
  const arr = [
    { value: 1, label: '帖子' },
    { value: 2, label: '评论点赞' },
  ]
  return getResult(key, value, arr)
}

// 粉丝关注马甲规则-规则类型
export function getDmAttentionFansSockpuppetRuleType(key, value) {
  const arr = [
    { value: 1, label: '关注' },
    { value: 2, label: '粉丝' },
  ]
  return getResult(key, value, arr)
}

// 自营-个微成员管理-在线状态
export function getIndividualOnlineStatusList(key, value) {
  const arr = [
    { value: 1, label: '在线' },
    { value: 2, label: '离线' }
  ]
  return getResult(key, value, arr)
}

// 排放计划-拜访类型
export function getVisitingProjectType(key, value) {
  const arr = [
    { value: 1, label: '默认' },
    { value: 2, label: '人物' },
    { value: 3, label: '药店' },
  ]
  return getResult(key, value, arr)
}


// SOP-款项信息查询-审批状态 
export function getSopExamineResultList(key, value) {
  const arr = [
    { value: 3, label: '审核中' },
    { value: 1, label: '已通过' },
    { value: 5, label: '已驳回' },
    { label: '认证撤回', value: 4 },
    { label: '待审核', value: 2 },

  ]
  return getResult(key, value, arr)
}


// DM 管理-企业端 - 审批任务
export function getDmExamineResultList(key, value) {
  const arr = [
    { value: 1, label: '待执行' },
    { value: 2, label: '执行中' },
    { value: 3, label: '执行成功' },
    { value: 4, label: '取消执行' },
    { value: 5, label: '候补执行' },
    { value: 6, label: '暂停执行' },
    // { label: '执行取消', value: 4 },
    // { label: '待审核', value: 2 },

  ]
  return getResult(key, value, arr)
}

// DM 众包广场 需求合作模式
export function getCaCooperationStatus(key, value) {
  const arr = [
    { value: 1, label: '协商' },
    { value: 2, label: '招标' },
  ]
  return getResult(key, value, arr)
}

// DM 通用执行状态枚举
export function getDmCommonExecuteStatus(key, value) {
  const arr = [
    { value: 1, label: '待执行' },
    { value: 2, label: '执行中' },
    { value: 3, label: '执行完成' },
    { value: 4, label: '取消执行' },
  ]
  return getResult(key, value, arr)
}

// DM 通用审核状态枚举
export function getDmCommonAuditStatus(key, value) {
  const arr = [
    { label: '待审核', value: 1 },
    { label: '审核通过', value: 2 },
    { label: '审核驳回', value: 3 }
  ]
  return getResult(key, value, arr)
}

// DM 众包广场 需求承接状态
export function getCaReceptStatus(key, value) {
  const arr = [
    { label: '已承接', value: 1 },
    { label: '未承接', value: 2 }
  ]
  return getResult(key, value, arr)
}

// DM 众包广场 需求发布状态
export function getCaPushStatus(key, value) {
  const arr = [
    { label: '待发布', value: 1 },
    { label: '已发布', value: 2 },
    { label: '已下架', value: 3 }
  ]
  return getResult(key, value, arr)
}

// DM 众包广场 项目申请签约状态
export function getCaApplySignStatus(key, value) {
  const arr = [
    { label: '待签约', value: 1 },
    { label: '签约中', value: 2 },
    { label: '签约完成', value: 3 }
  ]
  return getResult(key, value, arr)
}

// DM 管理-企业端 - 审批任务 - 审批条件
export function getDmExamineConditionList(key, value) {
  const arr = [
    { value: 1, label: '一级审核' },
    { value: 2, label: '二级审核' },
    { value: 3, label: '三级审核' },
  ]
  return getResult(key, value, arr)
}

// DM 众包广场 项目申请同步状态
export function getCaApplySyncStatus(key, value) {
  const arr = [
    { label: '已同步', value: 1 },
    { label: '未同步', value: 2 }
  ]
  return getResult(key, value, arr)
}

// DM 众包广场 项目申请类型
export function getCaApplyType(key, value) {
  const arr = [
    { label: '默认', value: 1 },
    { label: '指派', value: 2 }
  ]
  return getResult(key, value, arr)
}

// DM 审核管理 - 业务类型
export function getDmExamineBusinessList(key, value) {
  const arr = [
    { value: 2, label: '学术调研' },
    { value: 1, label: '病例征集' },
    { value: 3, label: '拜访打卡' },
  ]
  return getResult(key, value, arr)
}


// DM 审核管理 - 审核类型
export function getDmExamineAuditTypeResult(key, value) {
  const arr = [
    { value: 1, label: '默认' },
    { value: 2, label: '多级' },
  ]
  return getResult(key, value, arr)
}


// DM DM 问卷 采集类型
export function getDmcollectionTypeResult(key, value) {
  const arr = [
    { value: 1, label: '个人' },
    { value: 2, label: '患者推广' },
    { value: 3, label: '小葫芦精准推广' },
    { value: 4, label: '企业推广' },
    { value: 6, label: '线上推广' },
  ]
  return getResult(key, value, arr)
}


// 流量引擎-平台端-投放计划管理-订阅消息投放计划-执行状态
export function getSubscriptionMessageDeliveryPlanStatus(key, value) {
  const arr = [
    { value: 1, label: '待执行' },
    { value: 2, label: '执行中' },
    { value: 3, label: '执行成功' },
    { value: 4, label: '取消执行' },
    { value: 5, label: '候补执行' },
    { value: 6, label: '暂停执行' },
  ]
  return getResult(key, value, arr)
}

// 流量引擎-平台端-投放计划管理-订阅消息投放计划-定向条件
export function getSubscriptionMessageDeliveryPlanPointCondition(key, value) {
  const arr = [
    { value: 1, label: '指定时间范围' },
    { value: 2, label: '当天采集' },
    { value: 3, label: '定向推送' }
    // { value: 4, label: '执行失败' },
  ]
  return getResult(key, value, arr)
}

// 流量引擎-平台端-投放计划管理-订阅消息投放计划-发送方式
export function getSubscriptionMessageDeliveryPlanSendType(key, value) {
  const arr = [
    { value: 1, label: '立即发送' },
    { value: 2, label: '定时发送' },
    { value: 3, label: '循环发送' },
    { value: 4, label: '延迟发送' },
    // { value: 4, label: '执行失败' },
  ]
  return getResult(key, value, arr)
}


// 流量引擎-平台端-投放计划管理-订阅消息投放计划-发送方式
export function getSubscribeMessagesTepType(key, value) {
  const arr = [
    // { value: 1, label: '立即发送' },zzzzz
    { value: 2, label: '一次性订阅' },
    { value: 3, label: '长期订阅' },
    // { value: 4, label: '执行失败' },
  ]
  return getResult(key, value, arr)
}

// Dm- 基础设置 短信发送记录 业务状态
export function getDmSmsLogBusinessType(key, value) {
  const arr = [
    { value: 1, label: '验证码' },
    { value: 2, label: '电子签约' },
    { value: 3, label: '任务催办' }
  ]
  return getResult(key, value, arr)
}

// Dm- 基础设置 短信发送记录 业务状态
export function getDmLinkMeetingRangeTypeList(key, value) {
  // 定向范围：1-全部，2-关联
  const arr = [
    { value: 1, label: '全部' },
    { value: 2, label: '关联' },
  ]
  return getResult(key, value, arr)
}

// 综合管理 - 财务管理-媒体广告投放结算申请记录 - 结算状态
export function getAccountSettleStatusList(key, value) {
  const arr = [
    { value: 1, label: '待结算' },
    { value: 2, label: '已结算' },
    { value: 3, label: '结算中' },
  ]
  return getResult(key, value, arr)
}


// 拜访计划 - 客户角色:1-店员，2-医生
export function getdrugstoreRoleTypeTextList(key, value) {
  const arr = [
    { value: 1, label: '店员' },
    { value: 2, label: '医生' },
  ]
  return getResult(key, value, arr)
}

// 
// //客户反馈,1-消极，2-一般，3-积极
export function getdrugstoreFeedbackList(key, value) {
  const arr = [
    { value: 1, label: '消极' },
    { value: 2, label: '一般' },
    { value: 3, label: '积极' },
  ]
  return getResult(key, value, arr)
}

//诊后点评 - 活动列表

export function getDiagnoseRemarkList(key, value) {
  const arr = [
    { value: 1, label: '诊后点评' },
    { value: 2, label: '用药评价' },
  ]
  return getResult(key, value, arr)
}


// DZ-项目管理-报告导出-执行状态 
export function getDmReportExecuteStatus(key, value) {
  const arr = [
    { value: 1, label: '待执行' },
    { value: 2, label: '执行中' },
    { value: 3, label: '执行成功' },
    { value: 4, label: '执行失败' }

  ]
  return getResult(key, value, arr)
}

// DM 项目报告-报告导出类型
export function getDmReportExportBusinessType(key, value) {
  const arr = [
    { value: 1, label: '项目报告' },
    { value: 2, label: '个人结算报告' },
    { value: 3, label: '问卷调查分析报告' },
    { value: 4, label: '拜访计划分析报告' },
    { value: 5, label: '问卷数据报告' },
    { value: 6, label: '药店拜访数据报告' },
    { value: 7, label: '问卷报告' },
    { value: 8, label: '征集报告' },
    { value: 9, label: '拜访报告' },
    { value: 10, label: '项目报告(精准地推)' },
    { value: 11, label: '项目报告(普通地推)' },
    { value: 12, label: '小葫芦精准地推个人报告' },
    { value: 13, label: '小葫芦线上推广报告' },
    { value: 14, label: '小葫芦线上推广个人报告' },
    { value: 15, label: '关于陪诊平台的技术开发服务外包项目报告' },
    { value: 16, label: '线上用户活动个人报告' },
    { value: 17, label: '线下用户活动个人报告' },
    { value: 18, label: '线上用户活动项目报告' },
    { value: 19, label: '线下用户活动项目报告' },
    { value: 20, label: '科普笔记个人报告' },
    { value: 21, label: '科普笔记项目报告' },
    { value: 22, label: '药店拜访计划个人报告' },
    // { value: 100, label: '排查接口返回数据报告-测试用' },

  ]
  return getResult(key, value, arr)
}

// 获取广告主类型列表
export function getWxAuthTypes(key, value) {
  const arr = [
    { label: '企业号', value: 1 },
    { label: '个微-自营', value: 13 },

  ]
  return getResult(key, value, arr)
}

// 获取设备袋子类型
export function getDevicePacketTypeList(key, value) {
  const arr = [
    { label: '小号袋', value: 1 },
    { label: '中号袋', value: 2 },
    { label: '大号袋', value: 3 },
    { label: 'CT袋', value: 4 },

  ]
  return getResult(key, value, arr)
}

// 帖子列表-创建类型
export function getDmPostListCreateTypeList(key, value) {
  const arr = [
    { label: '手动', value: 1 },
    { label: 'qa任务', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 安装单位类型
export function getDmInstallTypeList(key, value) {
  const arr = [
    { label: '医院', value: 1 },
    { label: '市场', value: 2 },
    { label: '药店', value: 3 },
  ]
  return getResult(key, value, arr)
}


// 帖子模块 —— 留言互动
export function getLeaveMessageType(key, value) {
  const arr = [
    { label: '按最新留言时间', value: 'a.lastCommentTime' },
    { label: '按帖子最新时间', value: 'a.createTime' },
    { label: '按帖子未读数优先', value: 'a.readStatus' },
    { label: '按评论未读优先', value: 'a.unreadCommentsNum' },
  ]
  return getResult(key, value, arr)
}

// 渠道链 —— 业务类型
export function getChannelUrlBusinessType(key, value) {
  const arr = [
    { label: '渠道链', value: 1 },
    { label: '用药说明书', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 帖子列表 —— 入口类型
export function getDmPostListEntranceTypeList(key, value) {
  const arr = [
    { label: '默认', value: 1 },
    { label: '药品说明书-病友分享', value: 2 },
    { label: '药品说明书-健康科普', value: 3 },
    { label: '药品说明书-企业动态', value: 4 },
    { label: '陪诊平台', value: 5 },
    { label: '绿葆联盟-科普帖子', value: 6 },
    { label: '绿葆联盟-科普笔记', value: 7 },
  ]
  return getResult(key, value, arr)
}

// 药品说明书-产品管理 —— 上架状态
export function getDrugProductStatus(key, value) {
  const arr = [
    { label: '上架', value: 1 },
    { label: '下架', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 项目报告-报告类型
export function getProductReportTypeList(key, value) {
  const arr = [
    { label: '精准地推', value: 10 },
    { label: '普通地推', value: 11 },
    { label: '线上推广', value: 13 },
    { label: '陪诊项目', value: 14 },
    { label: '线上用户活动', value: 15 },
    { label: '线下用户活动', value: 16 },
    { label: '科普笔记', value: 17 }
  ]
  return getResult(key, value, arr)
}

// 小葫芦社区帖子素材类型
export function getCommunityPostsMaterialType(key, value) {
  const arr = [
    { label: '图文', value: 1 },
    { label: '视频', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 小葫芦栏目管理内容类型
export function getCommunityColumnContentType(key, value) {
  const arr = [
    { label: '帖子', value: 1 },
    { label: '直播', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 小葫芦栏目管理帖子类型
export function getCommunityColumnPostType(key, value) {
  const arr = [
    { label: '图文', value: 1 },
    { label: '短视频', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 小葫芦栏目管理栏目区域
export function getCommunityColumnPostArea(key, value) {
  const arr = [
    { label: '首页', value: 1 },
    { label: '交流', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 小葫芦栏目管理默认范围
export function getCommunityColumnDefaultRange(key, value) {
  const arr = [
    { label: '全部', value: 1 },
    { label: '定向', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 小葫芦栏目管理入口类型
export function getCommunityColumnInType(key, value) {
  const arr = [
    { label: '默认', value: 1 },
    { label: '药品说明书-企业动态', value: 2 },
    { label: '药品说明书-健康科普', value: 3 },
    { label: '药品说明书-病友分享', value: 4 },
    { label: '药品说明书-名医直播', value: 8 },
  ]
  return getResult(key, value, arr)
}

// 附近药店数据显示类型
export function getPharmacyDataType(key, value) {
  const arr = [
    { label: '显示', value: 1 },
    { label: '不显示', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 用药提醒用药人显示类型
export function getPharmacyPersonType(key, value) {
  const arr = [
    { label: '自己', value: 1 },
    { label: '他人', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 应用服务列表-面板区域
export function getAppServePanelAreaType(key, value) {
  const arr = [
    { label: '区域一', value: 1 },
    { label: '区域二', value: 2 },
    { label: '区域三', value: 3 },
    { label: '专区一', value: 4 },
  ]
  return getResult(key, value, arr)
}

// sop 袋子类型 
export function getSopGoodPacketTypeList(key,value) {
  const arr = [
    { label: '小号袋', value: 1 },
    { label: '中号袋', value: 2 },
    { label: '大号袋', value: 3 },
    { label: 'CT袋', value: 4 },
    { label: '单口CT机', value: 5 },
    { label: '单口垂直机', value: 6 },
    { label: '单口垂直机（带药台）', value: 7 },
    { label: '单口单卷', value: 8 },
    { label: '单口平面机', value: 9 },
    { label: '单口平面大号机', value: 10 },
    { label: '单口平面机（美保）', value: 11 },
    { label: '双口平面机', value: 12 },
    { label: '30粒/盒', value: 13 },
    { label: '7袋*10g/盒', value: 14 },
    { label: '双口CT机', value: 15 },
    { label: '样机', value: 16 },
    { label: '衣服包装袋', value: 17 },
    { label: '单口平面机(小)', value: 18 },
    { label: '单口平面机', value: 19 },
    { label: '单口垂直CT机（带药台）', value: 20 },
    { label: '背心袋', value: 21 },
    { label: '手挽袋', value: 22 },
    { label: "/", value:23 },
    { label: "CT机", value:24 },
    { label: "挂壁机", value:25 },
  ]
  return getResult(key, value, arr)
}

// 首页事项列表状态 Event status
export function getHomeEventStatus(key, value) {
  const arr = [
    { label: '待办事项', value: 1 },
    { label: '已办事项', value: 2 },
  ]
  return getResult(key, value, arr)
}

// PC端-合作商管理-添加场景值列表
export function getPlatformPartnerList(key,value) {
  const arr = [
    { label: '医院', value: '101' },
    { label: '学校', value: '102' },
    { label: '药店', value: '103' },
    { label: '餐饮', value: '104' },
    { label: '影院', value: '105' },
    { label: '商场', value: '106' },
    { label: '超市', value: '107' },
    { label: '便利店', value: '108' },
    { label: '农贸市场', value: '109' },
    { label: '机场', value: '110' },
    { label: '高铁', value: '111' },
    { label: '休闲娱乐', value: '112' },
    { label: '生活服务', value: '113' },
    { label: '公共场所', value: '114' },
    { label: '交通出行', value: '115' },
    { label: '其他', value: '100' },
    { label: '不限', value: '0' },
  ]
  return getResult(key, value, arr)
}

// 流量引擎-投放平台
export function getDmCommunityAdvertiseplanlinksPlatformList(key,value) {
  const arr = [
    { label: '微信', value: 1 },
    { label: '支付宝', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 流量引擎-广告投放计划类型
export function getDmCommunityPlanTypeList(key,value) {
  const arr = [
    { label: '直接跳转', value: 1 },
    { label: '间接跳转', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 设备管理-卡片推送权重 
export function getCardPushWeightList(key,value) {
  const arr = [
    { label: '默认优先', value: 1 },
    { label: '活动优先', value: 2 },
    { label: '同时推送', value: 3 },
  ]
  return getResult(key, value, arr)
}

// 健康自测-日统计列表-操作类型
export function getHealthOperatorType(key, value) {
  const arr = [
    { label: '自测', value: 1 },
    { label: '分享', value: 2 },
    { label: '订阅', value: 3 },
    { label: '浏览', value: 4 },
  ]
  return getResult(key, value, arr)
}
// 健康自测-用户类型
export function getHealthUserType(key, value) {
  const arr = [
    { label: '活跃用户', value: 1 },
    { label: '用户', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 健康自测-检测-选择类型
export function getHealthTestingTopType(key, value) {
  const arr = [
    { label: '按访问人数', value: 1 },
    { label: '按自测人数', value: 2 },
    { label: '按自测次数', value: 3 },
    { label: '按重新测试人数', value: 4 },
    { label: '按分享人数', value: 5 },
    { label: '按订阅人数', value: 6 },
  ]
  return getResult(key, value, arr)
}

// 医院管理-医院等级类型
export function getHospitalLevel(key, value) {
  const arr = [
    { label: '三甲医院', value: 1 },
    { label: '三乙医院', value: 2 },
    { label: '三丙医院', value: 3 },
    { label: '二甲医院', value: 4 },
    { label: '二乙医院', value: 5 },
    { label: '二丙医院', value: 6 },
    { label: '一甲医院', value: 7 },
    { label: '一乙医院', value: 8 },
    { label: '一丙医院', value: 9 },
  ]
  return getResult(key, value, arr)
}
// 引流号类型列表
export function getPlanWxAuthIdTypeList(key,value) {
  const arr = [
    { label: '默认', value: 1 },
    { label: '营销', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 药师问答业务类型
export function getDoctorQuestionsTypeList(key,value) {
  const arr = [
    { label: 'QA问答', value: 1 },
    { label: '医生问答', value: 2 },
    { label: '自定义问答', value: 3 },
  ]
  return getResult(key, value, arr)
}
// 小葫芦积分入口类型
export function getIntegrationPlatformList(){
  const arr = [
    {label:'小葫芦',value:1}
  ]
  return arr
}
// 小葫芦积分统计流水筛选类型
export function getIntegrationFlowScreenList(){
  const arr = [
    {label:'获取',value:1},
    {label:'消耗',value:2},
    {label:'回收',value:3}
  ]
  return arr
}

// 领红包订单状态
export function getRedPacketOrderStatus(key, value) {
  const arr = [
    { label: '发放中', value: 1 },
    { label: '已发放', value: 2 },
    { label: '发放失败', value: 3 },
    { label: '已领取', value: 4 },
    { label: '退款中', value: 5 },
    { label: '已退款', value: 6 },
  ]
  return getResult(key, value, arr)
}

// 用药说明书-产品类型
export function getPharmacyProductType(key,value) {
  const arr = [
    { label: '药品', value: 1 },
    { label: '非药品', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 用药说明书-产品类型添加业务类型
export function getPharmacyProductBusinessType(key,value) {
  const arr = [
    { label: '药品说明书', value: 1 },
    { label: '外链跳转', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 地推打卡-打卡状态
export function getGroundCardStepState(key,value) {
  const arr = [
    { label: '未打卡', value: 0 },
    { label: '已打卡，待上传照片', value: 1 },
    { label: '已完成', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 套餐订单管理-套餐状态
export function getPackageOrderState(key,value) {
  const arr = [
    { label: '待支付', value: 0 },
    { label: '生效中', value: 1 },
    { label: '已完成', value: 2 },
    { label: '已取消', value: 3 },
    { label: '已失效', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 医师类型-合作状态
export function getCooperationStatus(key,value) {
  const arr = [
    { label: '已确认', value: 1 },
    { label: '未确认', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 陪诊支付方式
export function getPayTypeStatus(key,value) {
  const arr = [
    { label: '微信支付', value: 1 },
    { label: '支付宝支付', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 设备管理-落地类型 
export function getLandingTypeList(key,value) {
  const arr = [
    { label: '线下', value: 1 },
    { label: '虚拟', value: 2 },
  ]
  return getResult(key, value, arr)
}
// 操作类型方式
export function getPharmacyOperationType(key,value) {
  const arr = [
    { label: '分享按钮', value: 1 },
    { label: '保存海报按钮', value: 2 },
    { label: '微信好友分享按钮', value: 3 },
  ]
  return getResult(key, value, arr)
}
// 分享类型方式
export function getPharmacyShareType(key,value) {
  const arr = [
    { label: '海报图片', value: 1 },
    { label: '海报弹窗', value: 2 },
    { label: '微信好友分享', value: 3 },
  ]
  return getResult(key, value, arr)
}

// 使用有效期类型
export function getExpiredType(key,value) {
  const arr = [
    { label: '3个月', value: 1 },
    { label: '6个月', value: 2 },
    { label: '12个月', value: 3 },
    { label: '24个月', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 使用有效期类型
export function getSourceType(key,value) {
  const arr = [
    { label: '平台', value: 1 },
    { label: '自营', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 分销类型
export function getSaleTypeList(key, value) {
  const arr = [
    { label: '平台', value: 5 },
    { label: '企业', value: 6 },
    { label: '个人（2级）分销商', value: 7 },
    { label: '个人（3级）分销商', value: 8 },
  ]
  return getResult(key, value, arr)
}

// 开关
export function getOnOffList(key, value) {
  const arr = [
    { label: '开启', value: 1 },
    { label: '关闭', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 分销级别
export function getMoerDistributionLevel(key,value) {
  const arr = [
    { label: '二级分销', value: 2 },
    { label: '三级分销', value: 3 }
  ]
  return getResult(key, value, arr)
}

// moer 帖子状态
export function getMoerCommunityPostsAuditStatus(key, value) {
  const arr = [
    { label: '待审核', value: 1 },
    { label: '审核通过', value: 2 },
    { label: '审核驳回', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 项目报告配置数据类型枚举 
export function getDmDataFormTypeList(key, value) {
  const arr = [
    { label: '性别', value: 15 },
    { label: '年龄段', value: 16 },
    { label: '科室/疾病', value: 17 }
  ]
  return getResult(key, value, arr)
}

// 小号内控细分枚举
export function getWxSubdivisionAuthTypeList(key,value) {
  const arr = [
    {
      label: "第三方",
      value: '140001'
    },{
      label: "自营",
      value: '140002'
    }
  ]
  return getResult(key, value, arr)
}

// 获取新的对应小号内控枚举
export function getNewAuthTypesOptions(key, value) {
  const authTypesOptions = [];
  const wxAuthTypeArr = getWxAuthType();
  const wxSubdivisionAuthTypeListArr = getWxSubdivisionAuthTypeList();
  const sortObect = {};
  let sortCount = 1000;
  // 排序分组
  for(let i=0;i<wxSubdivisionAuthTypeListArr.length;i++) {
    for(let j=0;j<wxAuthTypeArr.length;j++) {
      if(!sortObect[wxAuthTypeArr[j].value]) {
        sortCount += wxSubdivisionAuthTypeListArr.length * 2 * j;
        sortObect[wxAuthTypeArr[j].value] = sortCount;
      } else {
        sortObect[wxAuthTypeArr[j].value] += 1;
      }
      authTypesOptions.push({
        label: wxSubdivisionAuthTypeListArr[i].label + '-' + wxAuthTypeArr[j].label,
        value: wxSubdivisionAuthTypeListArr[i].value + '-' + wxAuthTypeArr[j].value,
        sort: sortObect[wxAuthTypeArr[j].value]
      })
    }
  }
  authTypesOptions.sort((a,b) => a.sort - b.sort)
  return authTypesOptions;
}

// 领袋授权手机号类型
export function getPacketMobileAuthorizationType(key, value) {
  const arr = [
    { label: '自定义授权窗口-曝光', value: 1 },
    { label: '自定义授权窗口-确认授权', value: 2 },
    { label: '自定义授权窗口-取消', value: 3 },
    { label: '官方授权窗口-允许', value: 4 },
    { label: '官方授权窗口-取消', value: 5 },
    { label: '成功授权手机', value: 6 },
  ]
  return getResult(key, value, arr)
}

// 弹窗显示规则
export function getShowPopupRuleType(key,value) {
  const arr = [
    { label: '每天首次显示', value: 2 },
    { label: '每天每次打开显示', value: 1 },
    { label: '每天每次打开显示，点跳转、关闭按钮后不再显示', value: 3 },
  ]
  return getResult(key, value, arr)
}

// 弹窗使用类型
export function getShowPopupUseType(key,value) {
  const arr = [
    { label: '首页', value: 1 },
    { label: '辟谣页-完成每日辟谣', value: 2 },
    { label: '祈福页-完成祈福', value: 3 },
    { label: '健康自测-完成答题', value: 4 },
    { label: '启动页', value: 5 },
  ]
  return getResult(key, value, arr)
}

// 弹窗小程序使用页面复制
export function getPopupUsePageType(key,value) {
  const arr = [
    { label: 'pages/index/index', value: 1 },
    { label: 'modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index', value: 2 },
    { label: 'modules/activity/calabash/webview', value: 3 },
    { label: 'modules/activity/health-testing/testing-detail', value: 4 },
    { label: 'pages/index/index', value: 5 },
  ]
  return getResult(key, value, arr)
}

// 马甲来源 sourceType
export function getVestManagementTasksSourceType(key,value) {
  const arr = [
    { label: '人工', value: 1 },
    { label: '系统', value: 2 }
  ]
  return getResult(key, value, arr)
}
// 陪诊城市定价类型
export function getCityPricingType(key,value) {
  const arr = [
    { label: '服务', value: 1 },
    { label: '套餐', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 服务号推送类型
export function getfwhNotifyType(key,value) {
  const arr = [
    { label: '公众号卡片', value: 1 },
    { label: '小程序卡片', value: 2 }
  ]
  return getResult(key, value, arr)
}
// h5页面名称类型
export function getH5PageNameType(key,value) {
  const arr = [
    { label: 'h5健康自测', value: 15 },
    { label: 'h5每日辟谣', value: 16 },
    { label: 'h5祈福许愿', value: 17 },
    { label: 'h5祈福许愿H5数字人说明书-红卡学术会议-中国专家共...（视频）', value: 18 },
    { label: '墨角藻免费领取', value: 19 },
  ]
  return getResult(key, value, arr)
}

// 签到修复
export function getSignFixV2Result(key,value) {
  const arr = [
    { label: '全部', value: 3 },
    { label: '签入', value: 1 },
    { label: '签出', value: 2 }
  ]
  return getResult(key, value, arr)
}

// 陪诊平台端-订单类型
export function getAccompanyDoctorList(key,value) {
  const arr = [
    { label: '平台订单', value: 1 },
    { label: '自营订单', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 陪诊平台端-支付方式 
export function getAccompanyDoctorPaymentList(key,value) {
  const arr = [
    {label:'微信支付',value:0},
    {label:'套餐支付',value:1},
  ]
  return getResult(key, value, arr)
}
// 横幅关联类型
export function getBannerPostAssociationType(key, value) {
  const arr = [
    { label: '全部帖子', value: 1 },
    { label: '关联项目', value: 2 }
  ]
  return getResult(key, value, arr)
}

// qa内容来源类型
export function getFramerSourceTypeList(key, value) {
  const arr = [
    { label: '爬虫', value: 1 },
    { label: '人工录入', value: 2 },
    { label: 'AI 生成', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 帖子QA 马甲库 审核状态
export function getFramerAuditStatusList(key, value) {
  const arr = [
    { label: '待审核', value: 1 },
    { label: '审核通过', value: 2 },
    { label: '审核驳回', value: 3 }
  ]
  return getResult(key, value, arr)
}

// 交互入口 
export function getDmEntryPointList(key, value) {
  const arr = [
    { label: '内部运营', value: 1 },
    { label: '任务操作', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 用户活动运行状态 1-草稿，2-进行中，3-已结束,4-已取消	
export function getDmUserActivityRunStatusList(key, value) {
  const arr = [
    { label: '草稿', value: 1 },
    { label: '进行中', value: 2 },
    { label: '已结束', value: 3 },
    { label: '已取消', value: 4 }
  ]
  return getResult(key, value, arr)
}

// 法人证件类型
// 17 身份证，18 护照，19 港澳居民来往内地通行证 20 台湾居民来往内地通行证
export function getLegalPersonCardType(key, value) {
  const arr = [
    { label: '身份证', value: 17 },
    { label: '护照', value: 18 },
    { label: '港澳居民来往内地通行证', value: 19 },
    { label: '台湾居民来往内地通行证', value: 20 },
  ]
  return getResult(key, value, arr)
}

// 任务 KPI 完成状态
export function getActivityInviteFinishStatusList(key, value) {
  const arr = [
    { label: '已完成', value: 1 },
    { label: '未完成', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 精准项目+个人报告/线上项目+个人报告风格选择
export function getReportStyleEnum(key, value) {
  const arr = [
    { label: '默认风格', value: 1 },
    { label: '新UI风格一', value: 2 },
  ]
  return getResult(key, value, arr)
}
// 小葫芦客服设置 类型集合
export function getCustomerServiceConfig(key, value) {
  const arr = [
    { label: '首页', value: 1 },
    { label: '交流', value: 2 },
    { label: '消息', value: 3 },
    { label: '我的', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 问卷报告图片事务枚举
export function getEventReportImageEnum(key, value) {
  const arr = [
    {
      label: "（项目执行流程）图片上传",
      value: 1,
    },
    {
      label: "（累计完成问卷）图片上传",
      value: 2,
    },
    {
      label: "（依据项目需求定制专属问卷题目）图片上传",
      value: 3,
    },
    {
      label: "（部分问卷填写内容展示）图片上传",
      value: 4,
    },
    {
      label: "（执行人操作流程）图片上传",
      value: 5,
    }
  ]
  return getResult(key, value, arr)
}