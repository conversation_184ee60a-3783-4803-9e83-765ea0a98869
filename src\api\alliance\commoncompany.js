import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 通用企业表
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/commoncompany/delete/batch/${data.ids}`)
}

// 根据id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/commoncompany/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/commoncompany/insert`, data)
}

// 分页查询
export function queryList(data) {
  return requestV1.get(`${prefix}/commoncompany/query/list`, data);
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/commoncompany/query/page`, data);
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/commoncompany/query/one`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/commoncompany/update`, data)
}

// 更新启动状态
export function updateOpenStatus(data = {}) {
  return requestV1.get(`${prefix}/commoncompany/update/open/status/${data.id}`, data)
}
