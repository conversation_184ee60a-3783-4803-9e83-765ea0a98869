/**
 * 存储字段名词，用于系统FINAL_NAME
 */
import env from '@/config/env'
export default {
  packagingEnvironment: 'test', // 打包环境(test-测试/pro-投产)
  // 资源类型
  permissionType: [
    {
      key: 1,
      value: '系统'
    },
    {
      key: 2,
      value: '模块'
    },
    {
      key: 3,
      value: '菜单'
    },
    {
      key: 4,
      value: '资源'
    }
  ],
  // 终端类型
  clientType: [
    {
      key: 1,
      value: '桌面端'
    },
    {
      key: 2,
      value: '小程序端'
    },
  ],
  // 是否类型
  statusType: [
    {
      key: 1,
      value: '是'
    },
    {
      key: 2,
      value: '否'
    },
  ],
  // 系统类型
  systemInfoType: [
    {
      key: 1,
      value: '业务'
    },
    {
      key: 2,
      value: '系统'
    },
  ]
}
