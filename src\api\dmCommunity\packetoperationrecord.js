/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 * 领袋授权手机号
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/packetoperationrecord/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/packetoperationrecord/query/page`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/packetoperationrecord/update`, data)
}

// 根据主键单一查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/packetoperationrecord/query/one`, data)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/packetoperationrecord/delete/one/${data.id}`)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/packetoperationrecord/query/list`, data)
}

// uv统计
export function operationTypeUvCount (data) {
  return requestV1.postJson(`${prefix}/packetoperationrecord/operationType/uv/count`, data)
}
