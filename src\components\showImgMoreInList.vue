<template slot-scope="scope">
  <div class="image-main">

    <el-image
      v-for="(item,index) in srcList"
      :key="index"
      :style="{width:'40px',height:'40px',flexShrink: '0',marginRight:'2px'}"
      :src="item.bigImageSrc"
      lazy
      @click="clickImage(item)"
    >
      <div slot="placeholder" class="image-slot">
        加载中<span class="dot">...</span>
      </div>
    </el-image>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      center
      top="3vh"
      :before-close="handleClose"
      style="text-align: center"
    >
      <div class="img">
        <el-image :style="{maxWidth:'450px'}" :src="imageSrc"/>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import { logImageServer } from '@/api/config'
export default {
  name: 'ShowImgMoreInList',
  props: {
    imgName: Array,
    imgHeight: {
      type: String,
      default: '20px'
    }

  },
  data() {
    return {
      title: '',
      imgServer: '',
      srcList: [],
      dialogVisible: false,
      imageSrc: ''
    }
  },
  watch: {
    imgName() {
      this.loadData()
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    clickImage(item) {
      this.imageSrc = item.bigImageSrc
      const imageSrc = item.imageSrc
      const imageSrcString = imageSrc.split('/')
      const newImageSrc = imageSrcString[2].split('.')[0]
      this.title = newImageSrc.substring(0, 4) + '-' + newImageSrc.substring(4, 6) + '-' + newImageSrc.substring(6, 8) +
        ' ' + newImageSrc.substring(8, 10) + ':' + newImageSrc.substring(10, 12) + ':' + newImageSrc.substring(12, 14)
      this.dialogVisible = true
    },
    handleClose(done) {
      done()
    },
    loadData() {
      this.imgServer = logImageServer
      this.srcList = []
      this.imgName.forEach(res => {
        const temp = {
          bigImageSrc: this.imgServer + res,
          imageSrc: res
        }
        // console.log(temp);
        this.srcList.push(temp)
      })
    }
  }
}
</script>

<style scoped>
.image-main {
  display: flex;
  width: 100%;

}

.img {
  margin: 0 auto;
  text-align: center;
}
</style>
