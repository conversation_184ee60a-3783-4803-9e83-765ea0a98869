<template>
  <div class="files-box">
    <el-upload
      ref="uploadCom"
      :action="doUpload"
      list-type="text"
      :before-upload="beforeAvatarUpload"
      :before-remove="handleRemove"
      :on-success="handleSuccess"
      :headers="uploadHeaders"
      :multiple="multiple"
      :disabled="disabled"
      :on-preview="handlePreview"
      name="files"
      :limit="limit"
      :data="{ groupId, relId }"
      :file-list="uploadFileArr"
      :accept="accept"
      :on-progress="onProgress"
      :show-file-list="false"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-button
        size="mini"
        type="primary"
        :disabled="disabled || (!multiple && this.picture)"
        :title="!disabled && !multiple && this.picture ? '移除后可点击' : ''"
        >点击上传</el-button
      >
    </el-upload>

    <div class="files-list">
      <template v-for="(item, index) in uploadFileArr">
        <div class="files-item" :key="item.url" v-if="isImage(item.url)">
          <template>
            <img :src="item.url" class="files-img" alt="" />
          </template>
          <div class="mask">
            <div class="masktools">
              <i
                class="closeico el-icon-zoom-in"
                @click="zoom(item.url, index)"
              ></i>
              <!-- 禁用状态无法移除 -->
              <i
                class="el-icon-delete closeico"
                v-if="!disabled"
                @click="remove(index)"
              ></i>
            </div>
          </div>
        </div>
        <div class="files-other" :key="item.url" v-else>
          {{item.url}}
          <div class="file-mask">
            <div class="masktools">
              <i
                class="closeico el-icon-zoom-in"
                @click="zoom2(item.url, index)"
              ></i>
              <i
                class="el-icon-delete closeico"
                v-if="!disabled"
                @click="remove(index)"
              ></i>
            </div>
          </div>
        </div>
      </template>
    </div>

    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="srcList"
      :zIndex="zIndex"
      :append-to-body="false"
      :initialIndex="imgIndex"
    ></el-image-viewer>
  </div>
</template>

<script>
import { doUpload, deleteByPath } from "@/api/index";
import { getToken } from "@/utils/auth";
import { imgServer } from "@/api/config";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { isImage } from "@/utils/index";

export default {
  components: {
    ElImageViewer,
  },
  props: {
    accept: {
      type: String,
      default: () => {
        return ".mp3,.mp4,.pdf,.doc,.docx,.png,jpg,PNG,JPG";
      },
    },
    groupId: {
      type: Number,
      default: 1000,
    },
    relId: {
      type: String,
      default: function () {
        return "";
      },
    },
    img: {
      type: [String, Array],
      default: null,
    },
    memberData: {
      type: Object,
      default: null,
    },
    // 1 营业执照 2组织机构代码 3税务登记证 4银行开户证明 5机构信用代码 6icp备案许可 7行业许可证 8身份证正面 9身份证反面
    type: {
      type: Number,
      default: 1,
    },
    // 是否提示删除成功
    isNotMsg: {
      type: Boolean,
      default: false,
    },
    limit: {
      type: Number,
      default: 1,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: null,
    },
    size: {
      type: Number,
      default: 100,
    },
    // 是否显示文件列表
    showFileMapFlag:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      imgIndex: 0,
      zIndex: 2000,
      srcList: [],
      showViewer: false, // 显示查看器

      doUpload,
      uploadHeaders: {
        Authorization: getToken(),
      },
      picture: null,
      imgServer,
      uploadFileArr: [],
      memberId: null,
      pictureList: [],
      fileDetailList: [],
    };
  },
  watch: {
    img(n) {
      console.log("nnn", n);
      if (n) {
        this.picture = n;
        this.initFileList();
      } else {
        this.uploadFileArr = [];
      }
    },
    memberData(n) {
      if (n) {
        this.memberId = n.id;
      }
    },
  },
  mounted() {
    if (this.formData) {
      this.picture = this.formData.value;
      this.initFileList();
    } else if (this.img) {
      this.picture = this.img;
      this.initFileList();
    }
    if (this.memberData) {
      this.memberId = this.memberData.id;
    }
  },
  methods: {
    isImage,
    remove(index) {
      let file = this.uploadFileArr[index];
      this.handleRemove(file);
    },
    zoom(url, idx) {
      const domArr = document.body.children;
      let zIndex = this.zIndex;
      for (let i = 0; i < domArr.length; i++) {
        console.log(domArr[i].style.zIndex);
        let t = domArr[i].style.zIndex;
        if (t !== "" && t > zIndex) {
          zIndex = t;
        }
      }
      this.zIndex = zIndex - 0;
      this.srcList = this.uploadFileArr.map((item) => {
        return item.url;
      });
      this.imgIndex = idx;
      this.$nextTick(() => {
        this.showViewer = true;
      });
    },
    zoom2(url,idx) {
      window.open(url);
    },
    closeViewer() {
      this.showViewer = false;
    },
    handlePreview(file) {
      window.open(file.url);
    },
    abort(list) {
      list = list || this.fileDetailList;
      this.$refs.uploadCom.abort(list);
    },
    onProgress(e) {
      this.$emit("onProgress", e);
    },
    beforeAvatarUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      let typeList = this.accept.split(",").map((item) => item.substr(1));
      let allType = false;
      if(this.accept[0] && this.accept[0] === '*') {
        allType = true
      }
      const extension = typeList.includes(testmsg);
      // testmsg === 'mp3' ||
      // testmsg === 'mp4' ||
      // testmsg === 'pdf' ||
      // testmsg === 'doc' ||
      // testmsg === 'docx'
      const isLt50M = file.size / 1024 / 1024 < this.size;

      // const isLt50M = file.size / 1024 / 1024 < 100
      if (!extension && !allType) {
        this.$message({
          message: `上传文件只能是${typeList.join("/")} 格式!`,
          type: "error",
        });
        return false; // 必须加上return false; 才能阻止
      }
      console.log(file);
      if (!isLt50M) {
        this.$message({
          message: `上传文件大小不能超过 ${this.size}MB!`,
          type: "error",
        });
        return false;
      }
      return extension || isLt50M;
    },
    initFileList() {
      if (this.img) {
        if (this.multiple) {
          const arr = [];
          this.img.map((i) => {
            if (i.indexOf("http") === -1) {
              arr.push({ url: imgServer + i, name: imgServer + i });
            } else {
              arr.push({ url: i, name: i });
            }
          });
          this.picture = arr;
          this.uploadFileArr = arr;
          console.log("this.uploadFileArr", this.uploadFileArr);
        } else {
          if (this.img.indexOf("http") === -1) {
            this.picture = imgServer + this.img;
            this.uploadFileArr = [
              { url: imgServer + this.img, name: imgServer + this.img },
            ];
          } else {
            this.picture = this.img;
            this.uploadFileArr = [{ url: this.img, name: this.img }];
          }
        }
      }
    },

    async handleRemove(file) {
      console.log(file);
      if (file.status === "success") {
        return this.$confirm(`此操作将永久删除该文件, 是否继续?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then((res) => {
          let path = null;
          if (this.multiple) {
            try {
              path = file.response.data[0].fileUrl;
            } catch {
              path = file.url;
            }
          } else {
            path = this.img;
          }

          path = path || file?.response?.data?.[0]?.dir || file.url;
          if (path.indexOf(this.$env.file_ctx) !== -1) {
            path = path.replace(this.$env.file_ctx, "");
          }
          console.log(path);
          deleteByPath({ path }).then((res) => {
            if (!this.isNotMsg) {
              this.$message({
                message: res.msg,
                type: "success",
              });
            }
            this.picture = null;
            if (this.multiple) {
              const list = JSON.parse(JSON.stringify(this.pictureList));
              let index = 0;
              for (let i = 0; i < list.length; i++) {
                let item = list[i];
                if (item.indexOf(this.$env.file_ctx) === -1) {
                  item = this.$env.file_ctx + item;
                }
                if (path.indexOf(this.$env.file_ctx) === -1) {
                  path = this.$env.file_ctx + path;
                }
                if (path === item) {
                  this.pictureList.splice(index, 1);
                } else {
                  index++;
                }
              }
              this.$emit("getUrl", this.pictureList);
              this.$emit("getFileDetail", this.pictureList);
              if (this.formData) {
                this.$emit("getUrlObj", {
                  url: this.pictureList,
                  formData: this.formData,
                });
              }
            } else {
              this.$emit("getUrl", this.picture);
              this.$emit("getFileDetail", this.picture);
              this.uploadFileArr = [];
              if (this.formData) {
                this.$emit("getUrlObj", {
                  url: this.picture,
                  formData: this.formData,
                });
              }
            }
          });
        });
      }
    },
    handleSuccess(response, file, fileList) {
      console.log(response, file, fileList);
      if(this.showFileMapFlag){
        let data = JSON.parse(JSON.stringify(response.data[0]));
        this.uploadFileArr.push({name:data.name,url:imgServer + data.dir});
      }
      if (this.multiple) {
        this.fileDetailList.push(response.data[0]);
        this.pictureList.push(response.data[0].dir);
        if (this.pictureList.length === fileList.length) {
          this.$emit("getUrl", this.pictureList);
          this.$emit("getFileDetail", this.fileDetailList);
          if (this.formData) {
            this.$emit("getUrlObj", {
              url: this.pictureList,
              formData: this.formData,
            });
          }
        }
      } else {
        this.picture = response.data[0].dir;
        this.$emit("getUrl", this.picture);
        this.$emit("getFileDetail", response.data[0]);
        const memberId = this.memberId;
        const type = this.type;
        const picture = this.picture;
        this.$emit("memberData", { picture, memberId, type });
        if (this.formData) {
          this.$emit("getUrlObj", {
            url: this.picture,
            formData: this.formData,
          });
        }
      }
    },
    handleBeforeUpload() {},
  },
};
</script>

<style lang="scss" scoped>
$gridWidth: 375px / 2;
$gridHeight: 667px / 2;
$gridAllWidth: 375px + 10;
.files-box {
}
.files-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: $gridAllWidth;
  margin-top: 10px;
  // align-items: center;
  .files-item {
    width: $gridWidth;
    height: $gridHeight;
    position: relative;
    margin-bottom: 10px;
  }
  .files-other {
    // min-width: 375px;
    padding: 0 10px;
    box-sizing: border-box;
    background: #e9e9f4;
    margin-bottom: 10px;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
    .closeico {
      font-size: 28px;
    }
  }
  .files-img {
    width: 100%;
    height: 100%;
    vertical-align: middle;
    object-fit: contain;
  }
  .mask,.file-mask {
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
  }
  .checkbox {
    position: absolute;
    right: 5px;
    top: 5px;
  }

  .masktools {
    // cursor: pointer;
    // display: flex;
    display: flex;
    position: absolute;
    /* top: 50%; */
    color: #fff;
    /* left: 50%; */
    width: 100%;
    height: 30px;
    top: 50%;
    display: flex;
    justify-content: space-around;
    padding: 0 30px;
    box-sizing: border-box;
    transform: translateY(-50%);
  }
  .files-item:hover .mask {
    display: flex;
  }
  .files-other:hover .file-mask {
    display: flex;
  }
  .closeico {
    font-size: 36px;
    cursor: pointer;
  }
}
</style>

