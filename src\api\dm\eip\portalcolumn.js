/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 企业门户栏目配置
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/portalcolumn/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/portalcolumn/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/portalcolumn/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/portalcolumn/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/portalcolumn/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/portalcolumn/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/portalcolumn/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/portalcolumn/update`, data)
}

// 获取树结构
export function getPortalColumnTree (data) {
    return requestV1.get(`${prefix}/portalcolumn/get/portal/column/tree`, data)
}
