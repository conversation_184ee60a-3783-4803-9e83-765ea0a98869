<template>
  <div style="height: 100%">
    <el-table
    size="mini"
    ref="multipleTable"
    v-loading="loading"
    :data="tableData"
    tooltip-effect="dark"
    style="width: 100%"
    border
    stripe
    class="lvTable"
    @selection-change="handleSelectionChange"
    >
    <el-table-column type="selection" width="55" />
    <el-table-column
      v-for="(item, index) in titleList"
      :key="index"
      :label="item.label"
      :prop="item.prop"
      :min-width="item.width"
      :show-overflow-tooltip="true"
      :fixed="item.fixed"
    >
        <template slot-scope="scope">
          <div v-if="item.type">
            <template v-if="item.type === 'emit'">
              <el-button size="mini" type="primary" @click="editor(scope.row,'editor')">编辑</el-button>
              <el-button size="mini" type="primary" @click="editor(scope.row,'bindStaff')">绑定员工</el-button>
              <el-button size="mini" type="primary" @click="gotoOrder(scope.row)">已完成订单</el-button>
              <el-button size="mini" type="primary" @click="gotoOrder(scope.row)">关联陪诊师</el-button>
              <el-button size="mini" type="danger"  @click="deleteRule(scope.row.id)">删除</el-button>
            </template>
            <template v-if="item.type === 'image'">
              <img class="logoIcon" :src="file_ctx + scope.row[item.prop]" alt="">
            </template>
            <template v-if="item.type === 'provinces'">
              {{getProvinces(scope.row[item.prop])}}
            </template>
            <template v-if="item.type === 'employees'">
              <el-popover
                placement="top-start"
                title="绑定员工列表"
                width="300"
                trigger="hover"
                v-if="scope.row.accountIdList && scope.row.accountIdList.length > 0"
              >
                <div class="employee-list">
                  <div v-for="(employee, empIndex) in scope.row.accountIdList" :key="empIndex" class="employee-item">
                    <span>
                      {{ getEmployeeName(employee) }}
                      {{ employee.phone ? `(${employee.phone})` : '' }}
                    </span>
                  </div>
                </div>
                <div slot="reference" class="employee-info">
                  已绑定 {{scope.row.accountIdList ? scope.row.accountIdList.length : 0}} 名员工
                </div>
              </el-popover>
              <span v-else class="no-employee">未绑定员工</span>
            </template>
          </div>
          <div v-else-if="item.prop === 'codeUrl'">
            {{ file_ctx + scope.row.codeUrl }}
            <template v-if="scope.row.codeButton === 1">
              <img class="logoIcon" :src="file_ctx + scope.row.codeUrl" alt="客服二维码">
            </template>
            <span v-else style="color: #909399;">未开启</span>
          </div>
          <template v-else>
            <div v-if="item.options">
            {{ getLabel(item.options,scope.row[item.prop]) }}
          </div>
          <div v-else>{{ scope.row[item.prop] }}</div>
          </template>
        </template>
    </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    props: {
      tableData: {
        type: Array,
        default: function () {
          return [];
        },
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    data(){
      return{
        file_ctx:this.$env.file_ctx,
        titleList: [
          { prop: "logo", label: "服务商logo",type:'image', width: "120px" ,options:null},
          { prop: "backLogo", label: "后台菜单logo", type: 'image'},
          { prop: "codeUrl", label: "客服二维码",type: 'image', width: "120px",formatter: (row) => row.codeButton ? '开启' : '关闭' },
          { prop: "providerName", label: "服务商名称", width: "120px" ,options:null},
          { prop: "rate", label: "分账比例", width: "120px" ,options:null},
          { prop: "provinces", label: "服务地区", width: "120px" ,type:'provinces'},
          { prop: "employeeCount", label: "陪诊师数量", width: "120px" },
          { prop: "accountIdList", label: "绑定员工", width: "120px", type: 'employees' },
          { prop: "balance", label: "服务商余额", width: "120px" },
          { prop: "withdrawnAmount", label: "已提现金额", width: "120px" },
          { prop: "contactPhone", label: "客服手机", width: "120px" },
          { prop: "employeeButton", label: "本地陪诊师模块", width: "120px", formatter: (row) => row.employeeButton ? '开启' : '关闭' },
          { prop: "doctorButton", label: "本地名医模块", width: "120px", formatter: (row) => row.doctorButton ? '开启' : '关闭' },
          { prop: "hotHospitalButton", label: "热门医院模块", width: "120px", formatter: (row) => row.hotHospitalButton ? '开启' : '关闭' },
          { prop: "orderCount", label: "已完成订单", width: "250px" ,type:'time'},
          { prop: "createTime", label: "入驻时间", width: "250px" ,type:'time'},
          { prop: "", label: "操作", width: "480px" ,type:'emit'},
        ],
      }
    },
    created(){
      this.initTitleList()
    },
    mounted(){},
    inject:['getOptions'],
    methods:{
      getProvinces(map){
        return map.map(e=>e.join('-')).join(',')
      },
      editor(row,type){
        this.$emit('editor',{row,type})
      },
      gotoOrder(row){
        this.$router.push({
          path:'/dmCommunity/accompany-doctor/calabash-accompany-doctor/dmCommunity/accompany-doctor/calabash-accompany-doctor/order-management/dmCommunity/accompany-doctor/calabash-accompany-doctor/order-management/order-list/dmCommunity/accompany-doctor/calabash-accompany-doctor/order-management/order-list',
          query:{
            type:2,
            id:row.id
          }
        })
      },
      deleteRule(id){
        this.$emit('delete',id)
      },
      getLabel(options,value){
        return options.find(e=>value == e.value)?.label
      },
      async initTitleList(){
        let options = await this.getOptions()
        this.titleList.map(async (e,i)=>{
          if(e.prop === 'eventType'){
            this.$set(this.titleList[i],'options',await options.geteventTypeOptions())
          }
          if(options[e.prop]){
            this.$set(this.titleList[i],'options',options[e.prop])
          }
        })
      },
      handleSelectionChange(val){
        this.$emit('select',val)
      },
      getEmployeeName(employee) {
        return employee.nickname || employee.userName || employee.username || '未知用户';
      },
    },
 }
</script>

<style lang='scss' scoped>
.logoIcon{
  width: 100px;
  height: 100px;
}
.employee-list {
  max-height: 300px;
  overflow-y: auto;

  .employee-item {
    padding: 5px 0;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }
  }
}
.employee-info {
  color: #409EFF;
  cursor: pointer;
}
.no-employee {
  color: #909399;
}
</style>
