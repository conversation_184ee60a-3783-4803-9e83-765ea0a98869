<!--form表单的开关组件-->
<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">
    <el-switch
      :class="childClass"
      v-model="form.data.switch"
      active-color="#13ce66"
      inactive-color="#ff4949"/>
  </el-form-item>
</template>

<script>

export default {
  name: 'Switched',
  props: {
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '开关',
          name: 'switch',
          data: {
            true: '1',
            false: '2'
          }
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          switch: true
        }
      }
    }
  },

  watch: {
    data: {
      immediate: true,
      handler(val) {
        if (this.config.data.true === val) {
          this.form.data.switch = true
        } else {
          this.form.data.switch = false
        }
      },
      deep: true
    },
    form: {
      handler(val) {
        let value = ''
        if (this.form.data.switch) {
          value = this.config.data.true
        } else {
          value = this.config.data.false
        }
        this.$emit('updateForm', '' + this.config.name, value)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">

</style>
