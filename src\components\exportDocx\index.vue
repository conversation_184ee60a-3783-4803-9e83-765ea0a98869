<template>
  <!-- 手动调用exportData 方法  this.$ref.exportDocxRef.exportData() -->
  <!-- <export-docx
    :filename="title + '-小葫芦市场推广执行明细.docx'"
    doc-url="/templateFile/small-groud-market-template.docx"
    :doc-data="docData"
    ref="exportDocxRef"
  >
    
  </export-docx> -->
  <!-- <el-button @click="exportData" size="small" type="success"> -->
  <div>
    <template v-if="type === 'button'">
      <el-button size="small" type="success" @click="exportData">
        <slot></slot>
      </el-button>
    </template>
    <template v-else-if="type === 'user'"></template>
  </div>
</template>

<script>
import JSZipUtils from "jszip-utils";
import Docxtemplater from "docxtemplater";
import PizZip from "pizzip";
import { saveAs } from "file-saver";
import ImageModule from "docxtemplater-image-module-free";
// const HTMLModule = require("docxtemplater-html-module");
// const imageOptions = {
//   centered: false,
//   getImage: function (tagValue, tagName) {
//     return new Promise(function (resolve, reject) {
//       PizZipUtils.getBinaryContent(tagValue, function (error, content) {
//         if (error) {
//           return reject(error);
//         }
//         return resolve(content);
//       });
//     });
//   },

//   getSize() {
//     // it also is possible to return a size in centimeters, like this : return [ "2cm", "3cm" ];
//     return [150, 150];
//   },
// };
const imageFormat = [
  "png",
  "jpg",
  "BMP",
  "DIB",
  "PCP",
  "DIF",
  "WMF",
  "GIF",
  "JPG",
  "TIF",
  "EPS",
  "PSD",
  "CDR",
  "IFF",
  "TGA",
  "PCD",
  "MPT",
  "PNG",
  "gif",
];
export default {
  name: "exportDocx",
  props: {
    type: {
      type: String,
      default: "user",
    },
    // 模板路径
    docUrl: {
      type: String,
      default: "",
    },
    // 模板数据
    docData: {
      type: Object,
      default: function () {
        return {};
      },
    },

    // 输出文件名，请自己接好后缀 abc.docx
    filename: {
      type: String,
      default: "默认.docx",
    },
    // 拼接域名 lvbao不支持本地调式图片
    domainUrl: {
      type: String,
      default: "https://lvbao-saas.oss-cn-shenzhen.aliyuncs.com/",
    },
    isNextOss: {
      type: Boolean,
      default: false
    }

    // 按钮加载中
    // loading: {
    //   type: Boolean,
    //   default: false,
    // },
  },
  data() {
    return {
      // docUrl: "/templateFile/small-groud-market-template.docx",
      // docData: {
      //   users: [
      //     {
      //       name: "1234",
      //       city: "深藏在",
      //       phone:"14323232323"
      //     },
      //     {
      //       name: "1234",
      //       city: "深藏在",
      //       phone:"14323232323"
      //     },
      //   ],
      // },
    };
  },
  methods: {
    clickFn() {
      this.$emit("toggle");
    },
    // 获取处理文档对象数据 target is Object
    async initDocData(target) {
      for (let key in target) {
        if (typeof target[key] == "string") {
          let arr = target[key].split(".");
          let suffix = arr[arr.length - 1];
          // console.log(suffix);
          // 字符串
          if (imageFormat.indexOf(suffix) !== -1) {
            // 图片
            target[key] = await this.getBase64Sync(target[key]);
          }
        } else if (Array.isArray(target[key])) {
          // 数组
          for (let i = 0; i < target[key].length; i++) {
            if (target[key] instanceof Object) {
              target[key] = await this.initDocData(target[key]);
            } else if (target[key] instanceof String) {
              target[key] = await this.getBase64Sync(target[key]);
            }
          }
        } else if (typeof target[key] == "object") {
          // 对象
          target[key] = await this.initDocData(target[key]);
        } else {
          // return ta
        }
      }

      return target;
    },

    loadFile(url, callback) {
      JSZipUtils.getBinaryContent(url, callback);
    },
    getBase64Sync(imgUrl = '') {
      if (imgUrl.startsWith("https://") || imgUrl.startsWith("http://")) {
        // return url;
      } else {
        imgUrl = this.domainUrl + imgUrl;
      }
      if(this.isNextOss) {
        imgUrl += '?x-oss-process=image/resize,w_750,m_lfit'
      }

      return new Promise((resolve, reject) => {
        var xhr = new XMLHttpRequest();

        xhr.open("get", imgUrl, true);
        // 至关重要
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (this.status == 200) {
            //得到一个blob对象
            var blob = this.response;
            // console.log("blob", blob);
            // 至关重要
            let oFileReader = new FileReader();
            oFileReader.onloadend = function (e) {
              // 此处拿到的已经是 base64的图片了
              let base64 = e.target.result;
              let image = new Image();
              image.src = base64;
              let canvas = document.createElement("canvas");
              canvas.width = image.width;
              canvas.height = image.height;
              let context = canvas.getContext("2d");
              context.drawImage(image, 0, 0, image.width, image.height);
              //返回
              resolve(base64);
            };
            oFileReader.readAsDataURL(blob);
          } else {
            console.log("this.status", this.status);
          }
        };
        xhr.send();
      });
    },
    async exportData() {
      let targetDocData = JSON.parse(JSON.stringify(this.docData));
      const filename = this.filename;

      targetDocData = await this.initDocData(targetDocData);
      // this.docData.imageUrl = await this.getBase64Sync(
      //   "https://file.greenboniot.cn/1/im-service/856534230000508935.jpg"
      // ); //图片转base64需要解决异步 async await
      // this.transferData();
      let that = this;
      this.loadFile(
        /*获取doc模版*/
        that.docUrl,
        // "./template.docx", //"https://docxtemplater.com/tag-example.docx"
        function (error, content) {
          if (error) {
            throw error;
          }
          // 图片处理开始
          let opts = {};
          opts = {
            //图像是否居中
            centered: false,
          };
          opts.getImage = (chartId) => {
            return that.base64DataURLToArrayBuffer(chartId);
          };
          opts.getSize = function (img, tagValue, tagName) {
            //自定义指定图像大小
            // return [200, 150];
            return [375,667];
          };
          //图片处理结束    没有图片可去掉这一段
          var zip = new PizZip(content);
          var doc = new Docxtemplater();
          doc.attachModule(new ImageModule(opts)); //没有图片可以去掉这一段
          doc.loadZip(zip);

          // console.log("targetDocData", targetDocData);
          //获取数据
          doc.setData({
            ...targetDocData,
          });

          try {
            doc.render();
          } catch (error) {
            var e = {
              message: error.message,
              name: error.name,
              stack: error.stack,
              properties: error.properties,
            };
            console.log(
              JSON.stringify({
                error: e,
              })
            );
            throw error;
          }

          that.$eltool.successMsg("已开始下载，具体自行点击浏览器查看下载内容");

          /*输出文档*/
          const out = doc.getZip().generate({
            type: "blob",
            mimeType:
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          });
          // console.log('out',out)
          // Output the document using Data-URI
          saveAs(out, filename);
        }
      );
    },
    base64DataURLToArrayBuffer(dataURL) {
      const base64Regex = /^data:image\/(png|jpg|jpeg|svg|svg\+xml);base64,/;
      if (!base64Regex.test(dataURL)) {
        return false;
      }
      const stringBase64 = dataURL.replace(base64Regex, "");
      let binaryString;
      if (typeof window !== "undefined") {
        binaryString = window.atob(stringBase64);
      } else {
        binaryString = new Buffer(stringBase64, "base64").toString("binary");
      }
      const len = binaryString.length;
      const bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        const ascii = binaryString.charCodeAt(i);
        bytes[i] = ascii;
      }
      return bytes.buffer;
    },
  },
};
</script>

