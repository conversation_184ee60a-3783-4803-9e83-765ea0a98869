/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/auth/api/v1'

//分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/centeruser/query/page', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/centeruser/insert', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/centeruser/update', data);
}

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/centeruser/query/one', data);
}

//根据主键集合字符串批量删除数据
export function deleteBatch(data) {
    return requestV1.deleteForm(`${prefix}/centeruser/delete/batch/${data}`);
}

//管理员重置密码
export function resetPassword(data) {
    return requestV1.postForm(prefix + '/centeruser/reset/password', data);
}

//更新密码
export function updatePassword(data) {
    return requestV1.putJson(prefix + '/centeruser/update/password', data);
}

//批量修改用户账号状态，冻结
export function updateUserAccountStatus(data) {
    return requestV1.postForm(prefix + '/centeruser/batch/update/user/account/status', data);
}

// 修改密码
export function updateUserPassword (data) {
    return requestV1.postForm(`${prefix}/centeruser/update/user/password`, data)
}

// 获取当前登录用户信息
export function getCurrentUser(data) {
  return requestV1.get(prefix + '/centeruser/get/current/user', data);
}

//用工档案重置密码
export function resetRecordPassword(data) {
  return requestV1.postForm(prefix + '/centeruser/reset/record/password', data);
}
