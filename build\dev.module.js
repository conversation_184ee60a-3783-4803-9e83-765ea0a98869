// 来自官方文档
const inquirer = require('inquirer');
const fs = require('fs')
const path = require('path')
const { run } = require('runjs')
const main = async () => {
  const modules = ['dm', 'dmCommunity', 'pharmacyCyclopedia', 'ca', 'user', 'system', 'sop', 'im', 'device']
  const otherModules = fs.readdirSync(path.join(__dirname, '../src/views'), { withFileTypes: false }).filter(item => {
    return !modules.includes(item)
  })
  const modulesChoices = fs.readdirSync(path.join(__dirname, '../src/views'), { withFileTypes: false }).map(item => {
    return {
      name: item,
      value: item
    }
  })

  const answers = await inquirer.prompt([
    {
      type: 'list',
      name: 'mode',
      message: '请选择运行的方式',
      default: 'modules',
      choices: [
        { name: '选择划分的模块', value: 'modules' },
        { name: '全部模块', value: 'all' },
        { name: '自定义', value: 'custom' }
      ]
    },
    {
      type: 'checkbox',
      name: 'modules',
      message: '请选择需要的模块（至少选择一个）',
      when: (result) => {
        return result.mode === 'modules'
      },
      validate: (result) => {
        if (Array.isArray(result)) {
          return result.length !== 0
        } else {
          return !!result
        }
      },
      choices: [
        ...modules.map(item => {
          return {
            name: item,
            value: item
          }
        }), {
          name: '其他',
          value: '其他'
        }
      ]
    },
    {
      type: 'checkbox',
      name: 'modules',
      message: '请自定义选择需要的模块',
      when: (result) => {
        return result.mode === 'custom'
      },
      choices: modulesChoices
    }
  ])

  switch (answers.mode) {
    case 'modules':
    case 'custom':
      try {
        const reg = answers.modules.reduce((l, item) => {
          if (item === '其他') {
            const otherStr = otherModules.reduce((ol, oitem) => {
              return ol + `|(views\\\\${oitem}\\\\)`
            }, '')
            return l + `|${otherStr}`
          }
          return l + `|(views\\\\${item}\\\\)`
        }, '(views\\\\index\\\\)|(views\\\\login\\\\)')
        process.env.VUE_APP_EXCLUDE_MODULES = `^(?=.*(\\\\views\\\\))(?!.*(${reg})).*`
      } catch (err) {
        console.log('err-----------', err)
      }
      break
    case 'all':
      break
    default:
  }
  run(`vue-cli-service serve`)
}

main()
