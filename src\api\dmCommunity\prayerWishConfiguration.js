/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 兑换审核
 */

// 查询积分配置
export function pointwishconfigQueryOne (data) {
    return requestV1.get(`${prefix}/pointwishconfig/queryOne`, data)
}

// 积分祈愿快捷语分页列表查询
export function pointwishtemplateQueryPage (data) {
    return requestV1.postJson(`${prefix}/pointwishtemplate/query/page`, data)
}

// 积分祈愿语新增
export function pointwishtemplateInsert (data) {
    return requestV1.postJson(`${prefix}/pointwishtemplate/insert`, data)
}

// 积分祈愿语删除
export function pointwishtemplateDelete (id) {
    return requestV1.deleteJson(`${prefix}/pointwishtemplate/delete/one/${id}`)
}
// 积分祈愿语新增
export function pointwishconfigInsert (data) {
    return requestV1.postJson(`${prefix}/pointwishconfig/insert`, data)
}

// 积分祈愿配置修改
export function pointwishconfigUpdate (data) {
    return requestV1.putJson(`${prefix}/pointwishconfig/update`, data)
}