/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'
const prefix2 = '/dm/api/v2'

/**
 * 小程序渠道链管理-马甲规则任务
 */

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/insert`, data)
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/query/page`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/minichannellinktask/update`, data)
}

// 根据主键单一查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/minichannellinktask/query/one`, data)
}

// 更新用户状态 
export function changestatus(data) {
  return requestV1.get(`${prefix}/minichannellinktask/changestatus`, data)
}


// 删除指定项 {id}
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/dm/api/v1/minichannellinktask/delete/one/${data.id}`)
}


// 任务子项列表查询
export function queryChildPage(data) {
  return requestV1.postJson(`${prefix}/minichannellinktaskitem/query/page`, data)
}

// 任务子项列表全部查询
export function queryChildList(data) {
  return requestV1.get(`${prefix}/minichannellinktaskitem/query/list`, data)
}

// 保存数据
export function insertChild(data) {
  return requestV1.postJson(`${prefix}/minichannellinktaskitem/insert`, data)
}

// 更新数据
export function updateChild(data) {
  return requestV1.putJson(`${prefix}/minichannellinktaskitem/update`, data)
}

// 根据主键单一查询
export function queryChildOne(data) {
  return requestV1.get(`${prefix}/minichannellinktaskitem/query/one`, data)
}


// 
// 更新子项用户状态 
export function changeChildStatus(data) {
  return requestV1.get(`${prefix}/minichannellinktaskitem/changestatus`, data)
}

// 批量更新子项状态 /v1/minichannellinktaskitem/changestatus/batch
export function changeChildStatusBatch(data) {
  return requestV1.postForm(`${prefix}/minichannellinktaskitem/changestatus/batch`, data)
}



// account/miniChannelLinkAccount
export function miniChannelLinkAccount(data) {
  return requestV1.get(`${prefix}/account/miniChannelLinkAccount`, data)
}



// 根据主键id指定删除
// export function deleteOne (data) {
//     return requestV1.deleteForm(`${prefix}/minichannellink/delete/one/${data.id}`)
// }

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/minichannellinktask/query/list`, data)
}


// 删除指定项 {id}
export function deleteChildOne(data) {
  return requestV1.deleteForm(`${prefix}/minichannellinktaskitem/delete/one/${data.id}`)
}

// 修复重复ip /minichannellinktask/ipAmendment
export function switchipAmendment(data) {
  return requestV1.get(`${prefix}/minichannellinktask/ipAmendment`, data)
}

// 设置费用
export function minichannellinktaskupdate(data) {
  return requestV1.putJson(`${prefix}/minichannellinktask/update`, data)
}


// 批量新增任务子项
export function minichannellinktaskbatchitem(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/insert/batch/item`, data)
}


// 获取关联的活动病例征集
export function casecollectactivityUserIdList(data) {
  return requestV1.get(`${prefix}/casecollectactivity/query/list/userId`, data)
}

// 获取关联的活动问卷
export function researchquerylistuserIds(data) {
  return requestV1.get(`${prefix}/research/query/list/userIds`, data)
}

// 获取关联的推广文任务 /dm/api/v1/
export function informationscienceuserId(data) {
  return requestV1.get(`${prefix}/informationscience/by/userId`, data)
}

// 获取关联的推广文任务 /dm/api/v1/
export function informationscienceseoTaskId(data) {
  return requestV1.get(`${prefix}/informationscience/query/list/seoTaskId`, data)
}

// 获取病例征集计划任务 /dm/api/v1/
export function casecollectactivityseoTaskId(data) {
  return requestV1.get(`${prefix}/casecollectactivity/query/list/seoTaskId`, data)
}


// 问卷调查计划
export function researchseoTaskId(data) {
  return requestV1.get(`${prefix}/research/query/list/seoTaskId`, data)
}

// 根据id获取动态表单
export function custompracticeloggetloglist(data) {
  return requestV1.get(`${prefix}/custompracticelog/get/log/list`, data)
}


// http://192.168.3.45:7000/dm/api/v1/casecollectsubmitlog/get/dynamic/data?taskId=909778287496630277
export function casecollectsubmitloggetdynamic(data) {
  return requestV1.get(`${prefix}/casecollectsubmitlog/get/dynamic/data`, data)
}


// 根据任务获取推广人 /v1/
export function seotaskexecutorloggetseotaskexecutorlog(data) {
  return requestV1.get(`${prefix}/seotaskexecutorlog/get/seotaskexecutorlog/list`, data)
}


// 获取具体产品文 /v2/
export function informationsciencequeryone(data) {
  return requestV1.postJson(`${prefix}/informationaccessrecord/query/list`, data)
}