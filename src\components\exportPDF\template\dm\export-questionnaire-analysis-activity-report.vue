<template>
  <div :id="uuid" class="report-content-box" v-loading="pageLoading">
    <!-- 封面 -->
    <div
      class="settlement-page border-l border-r bgMain"
      :style="{
        height: pageSize.height + 'px',
      }"
    >
      <div class="analysis-report-box">
        <div class="bg-top-theme">
          <div class="bg-top-theme-l">
            <div class="bg-top-theme-info-t">
              {{ research.title }}
            </div>
            <div class="bg-top-theme-info">{{ monthText }}月度服务报告</div>
          </div>
          <div class="bg-top-img">
            <img :src="themeImg" class="bgimg" alt="" />
          </div>
        </div>
        <div class="bg-top-border-one">
          <div class="bg-top-border-two">
            <template v-for="item in topResult">
              <div class="bg-top-border-item" :key="item.id" v-if="!item.hidden">
                <div class="bg-top-border-label">
                  <img :src="item.url" class="bg-top-border-ico" alt="" />
                  <span>{{ item.label }}</span>
                </div>
                <div class="bg-top-border-value">
                  {{ item.value }}
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="pageContent">
      <template v-for="(page, index) in pageContent">
        <div
          class="everyPage"
          :key="index"
          :style="{
            width: pageSize.width + 'px',
            height: pageSize.height + 'px',
          }"
          v-if="!page.authHeight"
        >
          <div class="everyPageContent border-l border-r">
            <!-- //  -->
            <div
              class="settlement-page"
              v-if="page.type === 'AnalysisofTaskQuantityOfProjectExecutors'"
            >
              <div class="project-executor-information-box">
                <div class="project-executor-t">项目执行人任务数量分析</div>
                <div class="d-flex">
                  <div class="project-executor-information-l">
                    <div class="panel barTarget">
                      <div class="chart"></div>
                    </div>
                  </div>
                </div>

                <div class="information-border-one">
                  <div class="information-border-two">
                    <div class="information-txt-box">
                      <span class="information-t">所有项目参与中:</span>
                      任务数量为0-18单,总计有{{
                        productAnalysisInfo.num_0_18_count
                      }}人,占比:{{
                        productAnalysisInfo.num_0_18
                      }},任务数量为18-36单,总计有{{
                        productAnalysisInfo.num_18_36_count
                      }}人,占比:{{
                        productAnalysisInfo.num_18_36
                      }},任务数量为36-54单,总计有{{
                        productAnalysisInfo.num_36_54_count
                      }}人,占比:{{
                        productAnalysisInfo.num_36_54
                      }},任务数量为54-72单,总计有{{
                        productAnalysisInfo.num_54_72_count
                      }}人,占比:{{
                        productAnalysisInfo.num_54_72
                      }},任务数量为72-90单,总计有{{
                        productAnalysisInfo.num_72_90_count
                      }}人,占比:{{
                        productAnalysisInfo.num_72_90
                      }},任务数量为90单以上,总计有{{
                        productAnalysisInfo.num_72__count
                      }}人,占比:{{ productAnalysisInfo.num_72_ }},
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          :key="index"
          v-else-if="page.authHeight"
          :style="{
            width: pageSize.width + 'px',
            height: page.targetHeight ? page.targetHeight + 'px' : 'auto',
          }"
          :id="authHeightResult[index]"
        >
          <div class="everyPageContent">
            <div
              class="settlement-page border-l border-r"
              v-if="page.type === 'projectExecutorInformation' && !page.hidden"
            >
              <div class="project-executor-information-box">
                <div class="reportContent-t ps50">
                  <div class="reportContent-tl"></div>
                  项目执行人信息
                  <div class="reportContent-tr"></div>
                </div>
                <div class="d-flex">
                  <div class="project-executor-information-l">
                    <div class="panel leftLine">
                      <!-- <h2>折线图-播放量</h2> -->
                      <div class="chart"></div>
                      <div class="pannel-footer">
                        <div class="age-tabs-t">项目执行人年龄区间</div>
                        <div class="age-tabs">
                          <template v-for="(item, index) in ageResult">
                            <div class="age-tab-item" :key="index">
                              <div
                                class="age-radio"
                                :style="{
                                  background: item.itemStyle.normal.color,
                                }"
                              ></div>
                              {{ item.name }}
                            </div>
                          </template>
                        </div>
                      </div>
                      <!-- <div class="pannel-footer"></div> -->
                    </div>
                  </div>
                  <div class="project-executor-information-r">
                    <!-- <div id="chart20"></div> -->
                    <div class="panel leftLine">
                      <!-- <h2>折线图-播放量</h2> -->
                      <div id="chart20"></div>
                      <div class="pannel-footer">
                        <div class="age-tabs-t">项目执行人性别分布</div>
                        <div class="age-tabs">
                          <template v-for="(item, index) in sexResult">
                            <div class="age-tab-item" :key="index">
                              <div
                                class="age-radio"
                                :style="{
                                  background: item.itemStyle.normal.color,
                                }"
                              ></div>
                              {{ item.name }}
                            </div>
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="information-border-one">
                  <div class="information-border-two">
                    <div class="information-txt-box">
                      <span class="information-t">根据统计信息:</span>
                      项目执行人中
                      <!-- 0-17周岁占比{{
                        projectExecutorInformation.age_0_17
                      }}, -->
                      18-30周岁占比{{
                        projectExecutorInformation.age_18_30
                      }},31-40周岁占比{{
                        projectExecutorInformation.age_31_40
                      }},41-50周岁占比{{
                        projectExecutorInformation.age_41_50
                      }},51-70周岁占比{{
                        projectExecutorInformation.age_51_70
                      }},
                      <!-- 70周岁以上占比{{
                        projectExecutorInformation.age_71_100
                      }}， -->
                      其中女性占比{{
                        projectExecutorInformation.sex_2
                      }},男性占比{{
                        projectExecutorInformation.sex_1
                      }},未知性别占比{{ projectExecutorInformation.sex_0 }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 拜访报告内容 -->
            <div class="settlement-page" v-if="page.type === 'reportContent'">
              <div class="reportContent-box"></div>
            </div>
            <div
              class="settlement-page border-l border-r"
              v-if="page.type === 'taskReviewStatusAnalysis'"
            >
              <div class="project-executor-information-box">
                <!-- <div class="project-executor-t">问卷审核分析</div> -->
                <div class="reportContent-t ps50">
                  <div class="reportContent-tl"></div>
                  问卷审核分析
                  <div class="reportContent-tr"></div>
                </div>
                <div class="d-flex">
                  <div class="project-executor-information-l">
                    <div class="panel rightLine leftLine">
                      <!-- <h2>折线图-播放量</h2> -->
                      <div class="chart4" id="chart4"></div>
                    </div>
                    <div class="pannel-footer3">
                      <div class="age-tabs-t">
                        {{ auditObject.audit_1_text }}
                      </div>
                      <div class="age-tabs-info">{{ auditObject.audit_1 }}</div>
                    </div>
                  </div>
                  <div class="project-executor-information-l">
                    <div class="panel rightLine leftLine">
                      <!-- <h2>折线图-播放量</h2> -->
                      <div class="chart2" id="chart2"></div>
                    </div>
                    <div class="pannel-footer3">
                      <div class="age-tabs-t">
                        {{ auditObject.audit_2_text }}
                      </div>
                      <div class="age-tabs-info">{{ auditObject.audit_2 }}</div>
                    </div>
                  </div>
                  <div class="project-executor-information-l">
                    <div class="panel rightLine leftLine">
                      <!-- <h2>折线图-播放量</h2> -->
                      <div class="chart3" id="chart3"></div>

                      <!-- <div class="pannel-footer"></div> -->
                    </div>
                    <div class="pannel-footer3">
                      <div class="age-tabs-t">
                        {{ auditObject.audit_3_text }}
                      </div>
                      <div class="age-tabs-info">{{ auditObject.audit_3 }}</div>
                    </div>
                  </div>
                </div>

                <div class="information-border-one">
                  <div class="information-border-two">
                    <div class="information-txt-box">
                      <span class="information-t">根据统计信息:</span>
                      本月度任务中，{{ auditObject.audit_1_text }}问卷数{{
                        auditObject.audit_count_1
                      }}条，占比{{ auditObject.audit_1 }}，{{
                        auditObject.audit_2_text
                      }}问卷数{{ auditObject.audit_count_2 }}条，占比{{
                        auditObject.audit_2
                      }}，{{ auditObject.audit_3_text }}问卷数{{
                        auditObject.audit_count_3
                      }}条，占比{{ auditObject.audit_3 }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 问卷报告内容 reportContentBooks -->
            <div
              class="settlement-page border-bottom"
              v-if="page.type === 'reportContentBooks'"
            >
              <div class="reportContent-box">
                <!-- <div class="project-executor-t">报告结束语</div> -->
                <div class="reportContent-t ps50">
                  <div class="reportContent-tl"></div>
                  报告结束语
                  <div class="reportContent-tr"></div>
                </div>

                <div class="information-border-one">
                  <div class="information-border-two">
                    <div class="information-txt-box">
                      <div class="information-t">
                        <div class="information-t-r"></div>
                        免责声明：
                      </div>
                      <div class="information-c">
                        本公司以勤勉的职业态度，独立、客观地出具本报告。报告所列数据及信息是本公司合法、公开采集的，但本公司不保证该等信息的准确性或完整性。本报告所载的资料、信息、意见及推测只提供给客户作参考之用，并非作为或被视为诊断或唯一性判断。分析逻辑基于团队的职业理解，清晰准确地反映了本公司的观点，结论不受任何第三方的授意或影响，特此声明。
                      </div>
                      <div class="information-t">
                        <div class="information-t-r"></div>
                        法律声明：
                      </div>
                      <div class="information-c">
                        本报告所载内容仅反映于本公司截止发布本报告前的判断。在不同时期，本公司可发出与本报告所载数据内容不一致的报告。报告及报告中的所有材料的版权归本公司所有，本公司对本报告保留一切权利，未经本公司事先书面授权，本报告的任何部分均不得以任何方式制作任何形式的拷贝、复印件或复制品，或再次分发给任何其他人，或以任何侵犯本公司版权的其他方式使用。所有本报告中使用的商标、服务标记及标记均为本公司的商标、服务标记及标记。
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 问卷题目 -->
            <div
              class="settlement-page border-l border-r"
              v-if="page.type === 'topicContent'"
            >
              <div class="reportContent-box">
                <div
                  class="reportContent-t ps50"
                  v-if="page.pageContent.isOnly"
                >
                  <div class="reportContent-tl"></div>
                  报告内容
                  <div class="reportContent-tr"></div>
                </div>
                <!-- <template v-for="item in problemData.problem"> -->
                <div class="chartItem">
                  <div class="reportContent-lt">
                    {{ page.pageContent.index }}、{{ page.pageContent.title }}
                  </div>

                  <div style="display: flex">
                    <div class="reportContent-l">
                      <div class="panel barTarget">
                        <!-- <div class="barChart"></div> -->
                        <div
                          class="barChart"
                          :id="uuid + '_' + page.pageContent.uuid"
                        ></div>
                        <!-- 饼图下面的底座 -->
                        <div
                          class="buttomCharts"
                          v-if="page.pageContent.showBottom"
                        ></div>
                      </div>
                    </div>
                    <div class="reportContent-l">
                      <el-table
                        :data="page.pageContent.tableData"
                        border
                        style="width: 100%"
                      >
                        <template v-for="item in page.pageContent.hearders">
                          <el-table-column
                            :key="item.key"
                            :prop="item.key"
                            :label="item.title"
                            :width="item.width"
                          >
                            <template slot-scope="scope">
                              <div
                                class="exporttxt"
                                :style="scope.row[item.key + 'Style'] || ''"
                              >
                                {{ scope.row[item.key] }}
                              </div>
                            </template>
                          </el-table-column>
                        </template>
                      </el-table>
                      <div class="reportContent-tip">
                        实际提交：{{ page.pageContent.totalTargetNumber || 0 }}
                        份问卷
                      </div>
                    </div>
                  </div>
                </div>

                <!-- </template> -->
              </div>
            </div>

            <!-- 调研背景与目的 -->
            <div
              class="settlement-page border-l border-r"
              v-if="page.type === 'researchBackgroundAndPurpose'"
            >
              <div class="reportContent-box">
                <div class="reportContent-t">
                  <div class="reportContent-tl"></div>
                  调研背景与目的
                  <div class="reportContent-tr"></div>
                </div>

                <div class="p20">
                  <div
                    style="text-indent: 2em; line-height: 2"
                    v-html="page.pageContent.contentHtml"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 调研方法与样本 -->
            <div
              class="settlement-page border-l border-r"
              v-if="page.type === 'researchMethodsAndSamples'"
            >
              <div class="reportContent-box">
                <div class="reportContent-t">
                  <div class="reportContent-tl"></div>
                  调研方法与样本
                  <div class="reportContent-tr"></div>
                </div>
                <div class="p20">
                  <div
                    style="text-indent: 2em; line-height: 2"
                    v-html="page.pageContent.contentHtml"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 调研结果分析 -->
            <div
              class="settlement-page border-l border-r"
              v-if="page.type === 'analysisOfResearchResults'"
            >
              <div class="reportContent-box">
                <div class="reportContent-t">
                  <div class="reportContent-tl"></div>
                  调研结果分析
                  <div class="reportContent-tr"></div>
                </div>

                <div class="p20">
                  <div
                    style="text-indent: 2em; line-height: 2"
                    v-html="page.pageContent.contentHtml"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 调研结论或建议 -->
            <div
              class="settlement-page border-l border-r"
              v-if="page.type === 'researchConclusionsOrSuggestions'"
            >
              <div class="reportContent-box">
                <div class="reportContent-t">
                  <div class="reportContent-tl"></div>
                  调研结论或建议
                  <div class="reportContent-tr"></div>
                </div>

                <div class="p20">
                  <div
                    style="text-indent: 2em; line-height: 2"
                    v-html="page.pageContent.contentHtml"
                  ></div>
                </div>
              </div>
            </div>

            <!--  -->
            <div
              class="settlement-page border-top"
              v-if="page.type === 'allContent'"
            >
              <div class="user-content-box">
                <div class="user-d-flex">
                  <div class="user-content-t">调研背景与目的</div>
                </div>
                <div
                  class="user-content-c"
                  v-html="page.pageContent.backgroundContentHtml"
                ></div>

                <div class="user-d-flex">
                  <div class="user-content-t">调研方法与样本</div>
                </div>
                <div
                  class="user-content-c"
                  v-html="page.pageContent.methodSampleContentHtml"
                ></div>

                <div class="user-d-flex">
                  <div class="user-content-t">调研结果分析</div>
                </div>
                <div
                  class="user-content-c"
                  v-html="page.pageContent.resultAnalysisContentHtml"
                ></div>

                <div class="user-d-flex">
                  <div class="user-content-t">调研结论或建议</div>
                </div>
                <div
                  class="user-content-c"
                  v-html="page.pageContent.resultConclusionContentHtml"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import {
  getVisitingplan as queryOne,
  // insert,
  // update,
} from "@/api/dm/visiting/visitingplan";

import { queryList } from "@/api/dm/visiting/visitingplanobjectlist.js";
import { getIOSTime } from "@/utils/index";
import { getdrugstoreFeedbackList } from "@/utils/enumeration.js";
import { getPie3D, getParametricEquation } from "../chart.js"; //工具类js，页面路径自己修改
import { initColumnar,initColumnarTwoChart,initColumnarFourChart } from "../columnar.js";
const color = ["#005aff", "#f8b551"];
import { seximg } from "../images";
import { loadScript, getPosition } from "@/utils/index";
import { researchAnalysisReport as getVisitingPlanAnalysisReport } from "@/api/research";
import { nandingBingEchart } from "../echartColumn.js";
import { getQueryStr } from '@/utils/index'


export default {
  props: {
    taskId: {
      type: [Number, String],
      default: null,
    },
    preview: {
      type: Boolean,
      default: true,
    },
    // 拼接域名 lvbao不支持本地调式图片
    domainUrl: {
      type: String,
      default: "https://lvbao-saas.oss-cn-shenzhen.aliyuncs.com/",
    },
    domainResult: {
      type: Array,
      default: function () {
        return ["https://file.greenboniot.cn/"];
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    filename: {
      type: String,
      default: "",
    },
    uuid: {
      type: String,
      default: "export-visiting-analysis-activity-report",
    },
  },
  data() {
    return {
      domainUrl2:"https://file.greenboniot.cn/",
      saTime:600,

      collectionType: null,
      pageSize: {
        width: 1128,
        height: 1123,
      },
      themeImg:
        process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/0.png",
      research: {
        // title: "关于高血压患者家属需求在全国地区的调研项目",
        // monthText: "2023-11",
      },
      topResult: [
        {
          label: "问卷名称",
          id: "title",
          value: "",
          hidden:false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/1.png",
        },
        {
          label: "项目方",
          value: "江西施美药业股份有限公司",
          id: "projectName",
          hidden:false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/2.png",
        },
        {
          label: "服务商",
          value: "广州兰图科技有限公司",
          id: "serviceName",
          hidden:false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/3.png",
        },
        {
          label: "任务月度",
          value: "",
          id: "monthText",
          hidden:false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/4.png",
        },
        {
          label: "问卷数量",
          id: "taskNumAllCount",
          value: "0",
          hidden:false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/5.png",
        },
        {
          label: "项目执行人数",
          id: "taskUserNum",
          value: "0",
          hidden:false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/6.png",
        },
        {
          label: "报告生成时间",
          id: "createTimeText",
          value: "",
          hidden:false,
          url:
            process.env.VUE_APP_PREFIX_URL + "/templateFile/report-image/7.png",
        },
      ],
      taskNumAllCount: 0,
      createTimeText: "",
      monthText: "",

      chartCount: 0,
      chartCountLength: 0,
      projectExecutorInformation: {
        age_0_17: "0.00%",
        age_18_30: "0.00%",
        age_31_40: "0.00%",
        age_41_50: "0.00%",
        age_51_70: "0.00%",
        age_71_100: "0.00%",
        sex_1: "0.00%", // 男性
        sex_2: "0.00%", //女性
        sex_0: "0.00%", // 未知
      },
      seximg,
      opageContent: [
        // 问卷活动调研
        {
          text: "所有内容",
          type: "allContent",
          authHeight: true,
          pageContent: {
            contentHtml: "",
          },
        },
        // background: 调研背景与目的
        // methodSample: 调研方法与样本
        // resultAnalysis: 调研结果分析
        // resultConclusion: 调研结论或建议
        // 项目执行人信息
        {
          text: "项目执行人信息",
          type: "projectExecutorInformation",
          authHeight: true,
          hidden: false,
          pageContent: {},
        },
        // // 任务审核状态分析
        {
          text: "任务状态分析",
          type: "taskReviewStatusAnalysis",
          authHeight: true,
          pageContent: {},
        },
        // // 项目执行人任务数量分析
        // {
        //   text: "项目执行人任务数量分析",
        //   type: "AnalysisofTaskQuantityOfProjectExecutors",
        //   // authHeight: true,
        //   pageContent: {},
        // },
      ],
      // 导出数据的标题
      pageContent: [],
      reportEnd: {
        text: "报告内容",
        type: "reportContentBooks",
        authHeight: true,
        pageContent: {},
      },
      // 题目
      topicObject: {
        text: "题目内容",
        type: "topicContent",
        authHeight: true,
        pageContent: {
          uuid: "my",
          hearders: [],
          tableData: [],
          isOnly: false,
        },
      },

      pageLoading: false,
      authHeightResult: [],
      initsuccesscount: 0,
      targetCount: 1,
      // 导出类型
      exportVisitType: 3,

      // 年龄段数据
      ageResult: [
        // {
        //   value: 0,
        //   name: "0-17周岁",
        //   oName: "0-17周岁",
        //   itemStyle: {
        //     normal: { color: "#6a9955" },
        //   },
        //   uuid: "age_0_17",
        // },
        {
          value: 0,
          name: "18-30周岁",
          oName: "18-30周岁",
          itemStyle: {
            normal: { color: "#2721ff" },
          },
          uuid: "age_18_30",
        },
        {
          value: 0,
          name: "31-40周岁",
          oName: "31-40周岁",
          itemStyle: {
            normal: { color: "#6763fe" },
          },
          uuid: "age_31_40",
        },
        {
          value: 0,
          name: "41-50周岁",
          oName: "41-50周岁",
          itemStyle: {
            normal: { color: "#cfcdfe" },
          },
          uuid: "age_41_50",
        },
        {
          value: 0,
          name: "51-70周岁",
          oName: "51-70周岁",
          itemStyle: {
            normal: { color: "#5dc0f9" },
          },
          uuid: "age_51_70",
        },
        // {
        //   value: 0,
        //   name: "70周岁以上",
        //   oName: "70周岁以上",
        //   itemStyle: {
        //     normal: { color: "#eab81a" },
        //   },
        //   uuid: "age_71_100",
        // },
      ],
      ageResultCount: 0,
      sexResultCount: 0,
      sexResult: [
        // itemStyle.normal.color
        {
          name: "女性",
          cName: "女性",
          value: 0,
          uuid: 2,
          itemStyle: {
            normal: {
              color: "#e2b62d",
            },
          },
        },
        {
          name: "男性",
          cName: "男性",
          value: 0,
          uuid: 1,
          itemStyle: {
            normal: {
              color: "#264894",
            },
          },
        },
        {
          name: "未知",
          cName: "未知",
          value: 0,
          uuid: 0,
          itemStyle: {
            normal: {
              color: "#ff002d",
            },
          },
        },
      ],
      auditResult: [
        {
          value: 0,
          name: "待处理",
          cName: "待处理",
          uuid: 1,
          itemStyle: {
            color: "#2822ff",
            // normal: { color: "#2822ff" },
          },
        },
        {
          value: 0,
          name: "处理中",
          cName: "处理中",
          uuid: 2,
          itemStyle: {
            color: "#fdc71c",
            // normal: { color: "#fdc71c" },
          },
        },
        {
          value: 0,
          name: "已处理",
          cName: "已处理",
          uuid: 3,
          itemStyle: {
            color: "#42d885",
            // normal: { color: "#2822ff" },
          },
        },
        // {
        //   value: 0,
        //   name: "已关闭",
        //   cName: "已关闭",
        //   uuid: 5,
        //   itemStyle: {
        //     color: "#f56c6c",
        //     // normal: { color: "#fdc71c" },
        //   },
        // },
        // {
        //   value: 0,
        //   name: "已撤销",
        //   cName: "已撤销",
        //   uuid: 6,
        //   itemStyle: {
        //     color: "#ffba00",
        //     // normal: { color: "#2822ff" },
        //   },
        // },
      ],
      auditObject: {
        audit_1_text: "待审核",
        audit_2_text: "审核通过",
        audit_3_text: "审核未通过",

        audit_1: "0.00%",
        audit_2: "0.00%",
        audit_3: "0.00%",
        // audit_5: "0.00%",
        // audit_6: "0.00%",
        audit_count_1: 0,
        audit_count_2: 0,
        audit_count_3: 0,
      },
      auditResultTotal: 0,

      tableData: [
        {
          title: "药店",
          title2: 2,
          title3: 3,
        },
        {
          title: "任务数（单）",
          title2: 2,
          title3: 3,
        },
        {
          title: "百分比（%）",
          title2: 2,
          title3: 3,
        },
      ],
      hearders: [
        {
          key: "title",
          prop: "title",
          title: "001",
        },
        {
          key: "title2",
          prop: "title2",
          title: "001",
        },
        {
          key: "title3",
          prop: "title3",
          title: "001",
        },
      ],
      // 执行人信息
      userReportVo: {},

      // 任务信息
      // taskReportVo: {},

      // 女性占比
      womanText: "0.00%",
      // 男性占比
      manText: "0.00%",

      // 项目信息
      demandInfo: {},

      visitingPlan: {},
      taskAllocationReportVoList: [],

      productAnalysisInfo: {
        num_0_18: "0.00",
        num_18_36: "0.00",
        num_36_54: "0.00",
        num_54_72: "0.00",
        num_72_90: "0.00",
        num_72_: "0.00",

        num_0_18_count: 0,
        num_18_36_count: 0,
        num_36_54_count: 0,
        num_54_72_count: 0,
        num_72_90_count: 0,
        num_72__count: 0,
      },

      // 题目数据
      answerReportVoList: [],
    };
  },
  mounted() {},
  watch: {
    updatecount(n) {
      this.pageLoading = true;
      this.initsuccesscount = 0;
      this.pageContent = [...this.opageContent];
      this.answerReportVoList = [];
      this.chartCountLength = 0;
      this.chartCount = 0;

      const saToken = getQueryStr('satoken')
      if(saToken){
        this.saTime = 300;
      }else{
        // 客户端预览
        this.saTime = 2000;
      }

      this.initEchart();
    },
  },
  methods: {
    addComputeTag() {
      // class=“pdf_finish”
      const dom = document.createElement("div");
      dom.classList = "pdf_finish";
      document.body.append(dom);
    },
    // 初始化label样式
    setLabel() {
      this.optionData.forEach((item, index) => {
        item.itemStyle = {
          color: color[index],
        };
        item.label = {
          normal: {
            show: true,
            color: color[index],
            formatter: ["{b|{b}}", "{c|{c}}{b|台}", "{d|{d}%}"].join(""), // 用来换行
            rich: {
              b: {
                color: "#fff",
                lineHeight: 25,
                align: "left",
              },
              c: {
                fontSize: 22,
                color: "#fff",
                textShadowColor: "#1c90a6",
                textShadowOffsetX: 0,
                textShadowOffsetY: 2,
                textShadowBlur: 5,
              },
              d: {
                color: color[index],
                align: "left",
              },
            },
          },
        };
        item.labelLine = {
          normal: {
            lineStyle: {
              width: 1,
              color: "rgba(255,255,255,0.7)",
            },
          },
        };
      });
    },
    // 图表初始化
    initChart(optionData, statusChart) {
      // let statusChart = this.$echarts.init(this.$refs.chart)
      // 传入数据生成 option, 构建3d饼状图, 参数工具文件已经备注的很详细
      let option = getPie3D(optionData, 0.8, 200, 20, 26, 0.5);
      let x = 250;
      statusChart.setOption(option);
    },
    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquationTwo(startRatio, endRatio, isSelected, isHovered, k) {
      // 计算
      let midRatio = (startRatio + endRatio) / 2;

      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;

      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }

      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== "undefined" ? k : 1 / 3;

      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1;

      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },

        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },

        x: function (u, v) {
          if (u < startRadian) {
            return (
              offsetX +
              Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        y: function (u, v) {
          if (u < startRadian) {
            return (
              offsetY +
              Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u);
          }
          return Math.sin(v) > 0 ? 1 : -1;
        },
      };
    },

    // 生成模拟 3D 饼图的配置项
    getPie3DTwo(pieData, internalDiameterRatio) {
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let legendData = [];
      let k =
        typeof internalDiameterRatio !== "undefined"
          ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
          : 1 / 3;

      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;

        let seriesItem = {
          name:
            typeof pieData[i].name === "undefined"
              ? `series${i}`
              : pieData[i].name,
          type: "surface",
          parametric: true,
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k,
          },
        };

        if (typeof pieData[i].itemStyle != "undefined") {
          let itemStyle = {};

          typeof pieData[i].itemStyle.color != "undefined"
            ? (itemStyle.color = pieData[i].itemStyle.color)
            : null;
          typeof pieData[i].itemStyle.opacity != "undefined"
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null;

          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;

        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = this.getParametricEquationTwo(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          true,
          false,
          1
        );

        startValue = endValue;

        legendData.push(series[i].name);
      }
      console.log("legendData", legendData);

      // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
      // series.push({
      //   name: "mouseoutSeries",
      //   type: "surface",
      //   parametric: true,
      //   wireframe: {
      //     show: false,
      //   },
      //    label: {
      //     color: "#fff",
      //     fontSize: 12,
      //     position: "bottom",
      //     formatter: function (params) {
      //       return `{nameSty|${params.name}:}{valueSty|${params.value}}`;
      //     },
      //     rich: {
      //       nameSty: {
      //         fontSize: 14,
      //         color: "#fff",
      //       },
      //       valueSty: {
      //         fontSize: 14,
      //         color: "#fff",
      //       },
      //     },
      //     // formatter: (item) => {
      //     //   //  console.log(item)
      //     //   return item.data.name + ":" + item.data.value + "";
      //     // },
      //   },
      //   avoidLabelOverlap: false,
      //   labelLine: {
      //     length: 10,
      //     length2: 20,
      //     minTurnAngle: 0,
      //     lineStyle: {
      //       color: "#ffffff",
      //       width: 1,
      //     },
      //   },
      //   itemStyle: {
      //     opacity: 1,
      //     color: "rgba(18,236,252,.5)",
      //   },

      //   center: ["50%", "60%"],
      //   parametricEquation: {
      //     u: {
      //       min: 0,
      //       max: Math.PI * 2,
      //       step: Math.PI / 20,
      //     },
      //     v: {
      //       min: 0,
      //       max: Math.PI,
      //       step: Math.PI / 1.4,
      //     },
      //     x: function (u, v) {
      //       return Math.sin(v) * Math.sin(u) + Math.sin(u);
      //     },
      //     y: function (u, v) {
      //       return Math.sin(v) * Math.cos(u) + Math.cos(u);
      //     },
      //     z: function (u, v) {
      //       return Math.cos(v) > 0 ? 0.1 : -0.1;
      //     },
      //   },
      // });

      // series.push({
      //   name: "pie2d",
      //   type: "pie",
      //   label: {
      //     color: "#000",
      //     fontSize: 12,
      //     position: "bottom",
      //     formatter: function (params) {
      //       return `{nameSty|${params.name}:}{valueSty|${params.value}}`;
      //     },
      //     rich: {
      //       nameSty: {
      //         fontSize: 14,
      //         color: "#000",
      //       },
      //       valueSty: {
      //         fontSize: 14,
      //         color: "#000",
      //       },
      //     },
      //     // formatter: (item) => {
      //     //   //  console.log(item)
      //     //   return item.data.name + ":" + item.data.value + "";
      //     // },
      //   },
      //   avoidLabelOverlap: false,
      //   labelLine: {
      //     length: 10,
      //     length2: 20,
      //     minTurnAngle: 0,
      //     lineStyle: {
      //       color: "#000000",
      //       width: 1,
      //     },
      //   },
      //   startAngle: 340, //起始角度，支持范围[0, 360]。 //重要
      //   clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
      //   radius: ["90%", "100%"],
      //   center: ["50%", "50%"], // 左右,上下
      //   data: pieData,
      //   itemStyle: {
      //     // opacity: 0,
      //   },
      //   selectedOffset: 30, // 分离距离
      //   bottom: "4%",
      //   // avoidLabelOverlap: true, //防止标签重叠
      // });

      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      let option = {
        //animation: false,
        legend: {
          show: true,
          data: legendData,
          type: "plain",
          // orient: "vertical",
          left: 0,
          top: 265,
          // bottom: 6,
          textStyle: {
            // color: "#CAEFFF",
            color: "#000",
          },
          itemGap: 10,
          // pageIconColor: "#CAEFFF", //翻页箭头颜色
          // pageIconInactiveColor: "#a6b4ba", //翻页（即翻页到头时箭头的颜色）
          // pageIconSize: 10, //翻页按钮大小
          // pageTextStyle: {
          //   color: "#CAEFFF", //翻页数字颜色
          // },
        },
        // tooltip: {
        //   formatter: (params) => {
        //     if (
        //       params.seriesName !== "mouseoutSeries" &&
        //       params.seriesName !== "pie2d"
        //     ) {
        //       //  当前饼图中没有展示这个城市 暂时隐藏掉不想展示的城市
        //       let noInChart = ![this.currentCity, "其他"].includes(
        //         params.seriesName
        //       );
        //       if (noInChart) return "";

        //       const { count, percent } = this.dataObj[params.seriesName];
        //       return (
        //         `${params.seriesName}<br/>` +
        //         `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
        //         `${count}场次 ${percent}`
        //       );
        //     }
        //   },
        // },
        // 饼图加背景图 不需要可以注释
        // graphic: [
        //   {
        //     type: "image", // 图形元素类型
        //     id: "logo", // 更新或删除图形元素时指定更新哪个图形元素，如果不需要用可以忽略。
        //     left: "12%", // 根据父元素进行定位 （居中） 10%
        //     bottom: "0", // 根据父元素进行定位   （0%）, 如果bottom的值是 0，也可以删除该bottom属性值。
        //     z: 0, // 层叠
        //     bounding: "all", // 决定此图形元素在定位时，对自身的包围盒计算方式
        //     style: {
        //       // image: require("../assets/image/pie-bg.png"), // 如果线上地址，必须是https开头的图片路径地址
        //       width: 150,
        //       height: 110,
        //     },
        //   },
        // ],
        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1.3,
          max: 1.3,
        },
        // label: {
        //   show: true,
        //   // data: legendData,
        //   position: "outside",
        //   // formatter(param) {
        //   //   // correct the percentage
        //   //   return param.name;
        //   // },
        //   // formatter: '{b} {c} {d}%'
        //   formatter: "{b} {c}人",
        // },
        grid3D: {
          show: false,
          height: 350,
          width: 350,
          boxHeight: 20, // 饼图厚度
          // left: "5%",
          // top: "0%",
          top: "-20%",
          left: "-3%",
          viewControl: {
            // 3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 25,
            distance: 300, //调整视角到主体的距离，类似调整zoom
            rotateSensitivity: 0,
            zoomSensitivity: 0,
            panSensitivity: 0,
            autoRotate: false, // 控制是否自动旋转
            //   autoRotateSpeed: 5,
            //   autoRotateAfterStill: 10
          },
          // axisLabel: {
          //   show: true, //是否显⽰刻度  (刻度上的数字，或者类⽬)
          //   //
          //   interval: 5, //坐标轴刻度标签的显⽰间隔，在类⽬轴中有效。
          //   formatter: function (v) {
          //     return v
          //   },
          // },
          // label: {
          //   show: true,
          //   position:"inside",
          //   formatter: function (params) {
          //     return params.name;
          //   },
          //   distance: 20,
          //   textStyle: {
          //     color: "#000",
          //     fontSize: 14,
          //   },
          // },
        },
        series: series,
      };
      return option;
    },

    getPieDataTwo() {
      let colorList = [
        "#6054FE",
        "#3AFF97",
        "#FF7662",
        "#9793d1",
        "#f1be8b",
        "#eff70f",
        "#3d93f7",
        "#FF5A5A",
        "#23EDFF",
        "#FFA44B",
        "#FAFF6B",
        "#FF8484",
        "#ad5d5d",
        "#04ccdf",
        "#82da4f",
      ];
      fetchChartData().then((res) => {
        if (res.code === 200) {
          const { data } = res.data;
          this.allCityList = data.map((item, i) => {
            let colorIndex = i < colorList.length ? i : i / colorList.length;
            let percent = parseInt(item.percent * 10000) / 100 + "%";
            this.dataObj[item.cityName] = {
              count: item.value,
              percent,
            };

            return {
              name: item.cityName,
              value: item.value,
              itemStyle: {
                color: colorList[colorIndex],
              },
            };
          });

          this.chartDom = this.$window.echarts.init(
            document.getElementById("pieChart")
          );
          this.filterCityData();
          setTimeout(() => {
            // 增加图例点击事件
            this.chartDom.on("legendselectchanged", (params) => {
              console.log("图例点击", params.name);
              this.filterCityData(params.name);
              // this.chartDom.setOption(func.barSelectesShadowOptions(option, true))
              this.chartDom.setOption({
                legend: { selected: { [params.name]: true } },
              });
            });
          }, 200);
        }
      });
    },

    // 同步渲染
    syncStatisisItem(fn) {
      let timer = this.saTime
      return new Promise((resolve, reject) => {
        fn();
        setTimeout(() => {
          resolve(true);
        }, timer);
      });
    },

    initEchart() {
      // this.$nextTick(() => {
      console.log("initEchart");

      return new Promise((resolve, reject) => {
        loadScript(
          `${this.domainUrl2}cdnjs/echarts.js`
        )
          .then(() => {
            loadScript(
              `${this.domainUrl2}cdnjs/echarts-gl.js`
            )
              .then(() => {
                console.log("window", window);

                this.getVisitingPlanAnalysisReport().then(async (res) => {
                  if (this.collectionType != 4) {
                    let itx = this.topResult.findIndex(item => item.id === 'taskUserNum');
                    this.topResult[itx].hidden = false;
                    this.initAgeStatistisc();
                    this.initSexStatisisc();
                  }else{
                    let itx = this.topResult.findIndex(item => item.id === 'taskUserNum');
                    this.topResult[itx].hidden = true;
                  }

                  this.$nextTick(() => {
                    setTimeout(() => {
                      this.updateSuccess();
                      resolve(true);
                    }, 600);
                  });
                  this.initProblemData();
                  // this.initAuditErrorStatisisc();
                  // this.initAuditingStatisisc();
                  // this.initAuditSuccessStatisisc();
                  // this.initProductAnalysis();
                  // this.initAuditCloseStatisisc();
                  // this.initAuditAlwayCloseStatisisc();
                  // this.initPharmacyStatistics();
                  // this.initHospitalStatistics();
                });
              })
              .catch((err) => {
                // console.log(err);
              });
          })
          .catch((err) => {
            console.log(err);
          });
      });

      // });
    },
    //配置构建 pieData 饼图数据 internalDiameterRatio:透明的空心占比
    getPie3D(pieData, internalDiameterRatio) {
      let that = this;
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let legendData = [];
      let legendBfb = [];
      let k = 1 - internalDiameterRatio;
      pieData.sort((a, b) => {
        return b.value - a.value;
      });
      // 为每一个饼图数据，生成一个 series-surface(参数曲面) 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;
        let seriesItem = {
          //系统名称
          name:
            typeof pieData[i].name === "undefined"
              ? `series${i}`
              : pieData[i].name,
          type: "surface",
          //是否为参数曲面（是）
          parametric: true,
          //曲面图网格线（否）上面一根一根的
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k,
          },
          //设置饼图在容器中的位置(目前没发现啥用)
          center: ["80%", "100%"],
          radius: "40%",
        };

        //曲面的颜色、不透明度等样式。
        if (typeof pieData[i].itemStyle != "undefined") {
          let itemStyle = {};
          typeof pieData[i].itemStyle.color != "undefined"
            ? (itemStyle.color = pieData[i].itemStyle.color)
            : null;
          typeof pieData[i].itemStyle.opacity != "undefined"
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null;
          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      legendData = [];
      legendBfb = [];
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;
        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = that.getParametricEquation(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          false,
          false,
          k,
          series[i].pieData.value
        );
        startValue = endValue;
        let bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4);
        legendData.push({
          name: series[i].name,
          value: bfb,
        });
        legendBfb.push({
          name: series[i].name,
          value: bfb,
        });
      }

      //(第二个参数可以设置你这个环形的高低程度)
      // let boxHeight = this.getHeight3D(series, 10); //通过传参设定3d饼/环的高度
      let boxHeight = 30; //通过传参设定3d饼/环的高度
      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      let option = {
        //图例组件
        legend: {
          data: legendData,
          //图例列表的布局朝向。
          orient: "horizontal",
          center: 0,
          top: 300,
          // bottom: 100,
          //图例文字每项之间的间隔
          itemGap: 15,
          textStyle: {
            color: "#A1E2FF",
            fontSize: "12px",
          },
          itemHeight: 10, // 修改icon图形大小
          itemWidth: 10, // 修改icon图形大小
          show: true,
          icon: "circle",
          //格式化图例文本（我是数值什么显示什么）
          formatter: function (name) {
            var target;
            for (var i = 0, l = pieData.length; i < l; i++) {
              if (pieData[i].name == name) {
                target = pieData[i].value;
              }
            }
            return `${name}: ${target}`;
          },
          // 这个可以显示百分比那种（可以根据你想要的来配置）
          formatter: function (param) {
            let item = legendBfb.filter((item) => item.name == param)[0];
            let bfs = that.fomatFloat(item.value * 100, 2) + "%";
            console.log(item.name);
            return `${item.name} :${bfs}`;
          },
        },
        //移动上去提示的文本内容(我没来得及改 你们可以根据需求改)
        tooltip: {
          formatter: (params) => {
            if (
              params.seriesName !== "mouseoutSeries" &&
              params.seriesName !== "pie2d"
            ) {
              let bfb = (
                (option.series[params.seriesIndex].pieData.endRatio -
                  option.series[params.seriesIndex].pieData.startRatio) *
                100
              ).toFixed(2);
              return (
                `${params.seriesName}<br/>` +
                `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
                `${bfb}`
              );
            }
          },
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: "#7BC0CB",
          },
        },
        label: {
          show: true,
          position: "outside",
          formatter: "{b} \n{c} {d}%",
        },
        //这个可以变形
        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1,
          max: 1,
        },
        //此处是修改样式的重点
        grid3D: {
          show: false,
          boxHeight: boxHeight, //圆环的高度
          //这是饼图的位置
          top: "-20.5%",
          left: "-4%",
          viewControl: {
            //3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 25, //角度(这个很重要 调节角度的)
            distance: 200, //调整视角到主体的距离，类似调整zoom(这是整体大小)
            rotateSensitivity: 0, //设置为0无法旋转
            zoomSensitivity: 0, //设置为0无法缩放
            panSensitivity: 0, //设置为0无法平移
            autoRotate: false, //自动旋转
          },
        },
        series: series,
      };
      return option;
    },
    //获取3d丙图的最高扇区的高度
    getHeight3D(series, height) {
      series.sort((a, b) => {
        return b.pieData.value - a.pieData.value;
      });
      return (height * 25) / series[0].pieData.value;
    },
    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
      // 计算
      let midRatio = (startRatio + endRatio) / 2;
      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;
      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }
      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== "undefined" ? k : 1 / 3;
      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1;
      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },
        x: function (u, v) {
          if (u < startRadian) {
            return (
              offsetX +
              Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        y: function (u, v) {
          if (u < startRadian) {
            return (
              offsetY +
              Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1;
          }
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
        },
      };
    },
    //这是一个自定义计算的方法
    fomatFloat(num, n) {
      var f = parseFloat(num);
      if (isNaN(f)) {
        return false;
      }
      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂
      var s = f.toString();
      var rs = s.indexOf(".");
      //判定如果是整数，增加小数点再补0
      if (rs < 0) {
        rs = s.length;
        s += ".";
      }
      while (s.length <= rs + n) {
        s += "0";
      }
      return s;
    },

    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    // 初始化题目分析报告
    initProblemData() {
      // 后端返回数据
      const problemData = this.answerReportVoList;
      let str = "mm";
      let allWriteOptionNum = 0;
      for (let i = 0; i < problemData.length; i++) {
        if (allWriteOptionNum < problemData[i].allWriteOptionNum) {
          allWriteOptionNum = problemData[i].allWriteOptionNum;
        }

        problemData[i].uuid = str + "_" + i;
        this.topicObject.pageContent.title =
          problemData[i].formTemplateTitle +
          "(" +
          (problemData[i].formType === 1 ? "单选" : "多选") +
          ")";

        this.topicObject.pageContent.uuid = problemData[i].uuid;
        this.topicObject.pageContent.showBottom = i % 2 !== 1 && i % 3 !== 1;

        this.topicObject.pageContent.hearders = [
          {
            key: "optionValue",
            prop: "optionValue",
            title: "选项",
            width:180,
          },
          {
            key: "selectOptionNum",
            prop: "selectOptionNum",
            title: "答案人数（人）",
            width:180,
          },
          {
            key: "selectOptionProportionText",
            prop: "selectOptionProportionText",
            title: "百分比（%）",
            // width:180,
          },
        ];
        let totalTargetNumber = 0;
        for (let k = 0; k < problemData[i].answerOptionVoList.length; k++) {
          problemData[i].answerOptionVoList[k].selectOptionProportionText = (
            problemData[i].answerOptionVoList[k].selectOptionProportion - 0
          ).toFixed(2);

          // totalTargetNumber +=
        }
        this.topicObject.pageContent.tableData =
          problemData[i].answerOptionVoList;
        this.topicObject.pageContent.isOnly = i === 0;

        // totalTargetNumber
        this.topicObject.pageContent.totalTargetNumber =
          problemData[i].allWriteOptionNum - 0;

        this.topicObject.pageContent.index = i + 1;

        this.pageContent.push(JSON.parse(JSON.stringify(this.topicObject)));
      }
      this.taskNumAllCount = allWriteOptionNum;

      let idx3 = this.topResult.findIndex(
        (item) => item.id === "taskNumAllCount"
      );
      this.topResult[idx3].value = allWriteOptionNum;

      console.log("this.pageContent", this.pageContent);

      this.pageContent.push(this.reportEnd);

      this.$nextTick(async () => {
        for (let i = 0; i < problemData.length; i++) {
          let item = problemData[i];

          let seriesData = [];
          let textData = [];

          for (let k = 0; k < item.answerOptionVoList.length; k++) {
            textData.push(item.answerOptionVoList[k].optionValue);
            seriesData.push({
              value: item.answerOptionVoList[k].selectOptionNum,
              // name: this.getTargetText(item.answerOptionVoList[k].optionValue),
              name: item.answerOptionVoList[k].optionValue,
            });
          }
          item.seriesData = seriesData;
          item.textData = textData;
          await this.initProbleDataChart(item, i);
        }
      });
    },
    getTargetText(text) {
      let str = text.substr(0, 6);
      let temp = text.substr(6);
      while (temp.length != 0) {
        let t = temp.substr(0, 6);
        temp = temp.substr(6);
        str += "\n" + t;
      }
      return str;
    },
    initProbleDataChart(item, idx) {
      return new Promise((resolve, reject) => {
        let that = this;
        const str = this.uuid;
        let strUUID = "#" + this.uuid + "_" + item.uuid;
        console.log("strUUID", strUUID);
        const dom = document.getElementById(str);
        //1 实例化对象
        let myCharts4 = window.echarts.init(dom.querySelector(strUUID));
        const colorResult = [
          "rgba(127, 181, 255,0.6)",
          "rgba(166, 228, 239,0.6)",
          "rgba(188, 161, 254,0.6)",
          "rgba(103, 96, 254,0.6)",

          "rgba(232, 177, 0,0.6)",
          "rgba(2, 170, 254,0.6)",
          "rgba(129, 78, 204,0.6)",
          "rgba(203, 130, 81,0.6)",
          "rgba(163, 200, 78,0.6)",
          "rgba(23, 179, 103,0.6)",
        ];
        // let tidx = parseInt(Math.random(10));
        let tidx = idx;
        for (let i = 0; i < item.seriesData.length; i++) {
          let color = "";
          if (!colorResult[tidx]) {
            tidx = 0;
          }
          color = colorResult[tidx];
          tidx += 1;
          item.seriesData[i].itemStyle = {
            color: color,
            opacity: 0.6,
          };
        }
        let timer = this.saTime
        //----------------------------------------------
        
        if(idx % 4 === 1) {
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          initColumnarFourChart(myCharts4, optionData, yAxisData);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess(true);
          }, 1000);
        } else if (idx % 2 === 1) {
          // this.initChart(item.seriesData, myCharts4);
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          initColumnar(myCharts4, optionData, yAxisData);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess(true);
          }, timer);
        } else if (idx % 3 === 1) {
          // initFixColumnarChart
          // this.initChart(item.seriesData, myCharts4);
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name);

            // yAxisData.push(text);
            // optionData.push(item.seriesData[k].value);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          initColumnarTwoChart(myCharts4, optionData, yAxisData);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess(true);
          }, timer);
        } else {
          // 传入数据生成 option
          // let option = this.getPie3D(item.seriesData, 0.85);
          let option = this.getPie3DTwo(item.seriesData, 0.59);
          // 绘制图表
          myCharts4.setOption(option);

          setTimeout(() => {
            resolve(true);
            this.chartCount += 1;
            this.updateSuccess(true);
          }, timer);
        }
      });
    },
    // 获取分析报告
    async getVisitingPlanAnalysisReport() {
      const res = await getVisitingPlanAnalysisReport({
        id: this.taskId,
        // id: "924651141724856327",
      });


      const data = res.data;
      // 执行人信息
      if (data.userReportVo instanceof Object) {
        this.userReportVo = data.userReportVo || {};

        // 性别信息
        if (this.userReportVo.genderReportVoList) {
          let genderReportVoList = this.userReportVo.genderReportVoList;

          for (let i = 0; i < genderReportVoList.length; i++) {
            for (let j = 0; j < this.sexResult.length; j++) {
              this.sexResultCount = genderReportVoList[i].allNum;
              if (this.sexResult[j].uuid === genderReportVoList[i].gender) {
                let percent =
                  genderReportVoList[i].num !== 0
                    ? (
                        (genderReportVoList[i].num /
                          genderReportVoList[i].allNum) *
                        100
                      ).toFixed(2)
                    : "0.00";
                if (this.sexResult[j].uuid === 2) {
                  this.womanText = percent + "%";
                } else if (this.sexResult[j].uuid === 1) {
                  this.manText = percent + "%";
                }
                this.sexResult[j].value = genderReportVoList[i].num;
                this.sexResult[j].name =
                  this.sexResult[j].cName + percent + "%";
                this.sexResult[j].num = this.sexResult[j].value;
                let str = "sex_" + genderReportVoList[i].gender;
                this.projectExecutorInformation[str] = percent + "%";
                break;
              }
            }
          }
        }
      }

      // 任务信息
      // if (data.todoTasksList instanceof Object) {
      //   this.todoTasksList = data.todoTasksList || {};
      // }
      if (data.authStatusGroupVoList instanceof Object) {
        let taskStatusReportVoList = data.authStatusGroupVoList;

        if (taskStatusReportVoList) {
          console.log("taskStatusReportVoList", taskStatusReportVoList);
          let auditResultTotal = 0;
          for (let i = 0; i < taskStatusReportVoList.length; i++) {
            for (let j = 0; j < this.auditResult.length; j++) {
              if (
                this.auditResult[j].uuid ===
                taskStatusReportVoList[i].authStatus
              ) {
                this.auditResult[j].value =
                  taskStatusReportVoList[i].num &&
                  taskStatusReportVoList[i].num !== ""
                    ? taskStatusReportVoList[i].num
                    : 0;
                auditResultTotal += this.auditResult[j].value;

                // let percent =
                //   taskStatusReportVoList[i].num !== 0
                //     ? (
                //         (taskStatusReportVoList[i].num /
                //           taskStatusReportVoList[i].allNum) *
                //         100
                //       ).toFixed(2)
                //     : "0.00";
                let percent = taskStatusReportVoList[i].proportion;
                this.auditResult[j].name =
                  this.auditResult[j].cName + percent + "%";
                // this.auditResultTotal = taskStatusReportVoList[i].allNum;

                let str = "audit_" + this.auditResult[j].uuid;
                this.auditObject[str] = percent + "%";
                let str2 = "audit_count_" + this.auditResult[j].uuid;
                this.auditObject[str2] = taskStatusReportVoList[i].num;
                let str3 = "audit_" + this.auditResult[j].uuid + "_text";
                this.auditObject[str3] = taskStatusReportVoList[i].authDesc;

                break;
              }
            }
          }
          this.auditResultTotal = auditResultTotal;
        }
      }
      if (data.taskReportVo instanceof Object) {
        // this.taskReportVo = data.taskReportVo || {};

        // { label: '待审核', value: 1 },
        // { label: '审核通过', value: 2 },
        // { label: '审核驳回', value: 3 },
        // { label: '待提审', value: 4 },
        // { label: '待建多级审核', value: 5 },
        // if(this.todoTasksList.taskStatusReportVoList)
        // let taskStatusReportVoList = data.taskReportVo.taskStatusReportVoList;

        // if (taskStatusReportVoList) {
        //   console.log(
        //     "auditResult",
        //     this.auditResult,
        //     "taskStatusReportVoList",
        //     this.taskStatusReportVoList
        //   );
        //   for (let i = 0; i < taskStatusReportVoList.length; i++) {
        //     for (let j = 0; j < this.auditResult.length; j++) {
        //       if (
        //         this.auditResult[j].uuid ===
        //         taskStatusReportVoList[i].taskStatus
        //       ) {
        //         this.auditResult[j].value =
        //           taskStatusReportVoList[i].num &&
        //           taskStatusReportVoList[i].num !== ""
        //             ? taskStatusReportVoList[i].num
        //             : 0;
        //         let percent =
        //           taskStatusReportVoList[i].num !== 0
        //             ? (
        //                 (taskStatusReportVoList[i].num /
        //                   taskStatusReportVoList[i].allNum) *
        //                 100
        //               ).toFixed(2)
        //             : "0.00";
        //         this.auditResult[j].name =
        //           this.auditResult[j].cName + percent + "%";
        //         this.auditResultTotal = taskStatusReportVoList[i].allNum;

        //         let str = "audit_" + this.auditResult[j].uuid;
        //         this.auditObject[str] = percent + "%";
        //         let str2 = "audit_count_" + this.auditResult[j].uuid;
        //         this.auditObject[str2] = taskStatusReportVoList[i].num;
        //         break;
        //       }
        //     }
        //   }
        // }

        if (data.taskReportVo.taskAllocationReportVoList instanceof Object) {
          this.taskAllocationReportVoList =
            data.taskReportVo.taskAllocationReportVoList;
        }
      } else {
      }

      if (data.demand instanceof Object) {
        if (data.todoTasksList instanceof Object) {
          data.demand.taskNumber = data.todoTasksList.length;
        } else {
          data.demand.taskNumber = 0;
        }
        if (data.taskUserVoList instanceof Object) {
          data.demand.productTaskNumber = data.taskUserVoList.length;
        } else {
          data.demand.productTaskNumber = 0;
        }
        data.demand.createTimeText = getIOSTime(data.demand.createTime);
        let date = new Date(data.demand.createTime);
        // data.demand.monthText = date.getMonth() + 1;
        data.demand.monthText =
          date.getFullYear() + "-" + (date.getMonth() + 1);
        data.demand.yearText = date.getFullYear();

        // let idx = this.topResult.findIndex((item) => item.id === "monthText");
        // this.topResult[idx].value = data.demand.monthText;
        // this.topResult[idx].value = "2023-11";

        let idx = this.topResult.findIndex((item) => item.id === "taskUserNum");
        this.topResult[idx].value = data.demand.taskUserNum;

        this.demandInfo = data.demand;
      }

      if (data.visitingPlan instanceof Object) {
        this.visitingPlan = data.visitingPlan;
      }

      console.log("data.answerReportVoList", data.answerReportVoList);

      if (data.answerReportVoList instanceof Object) {
        const answerReportVoList = data.answerReportVoList;
        this.answerReportVoList = answerReportVoList;
        // chartCount:0,
        this.chartCountLength = data.answerReportVoList.length;
        this.chartCount = 0;
      }

      if (data.research instanceof Object) {
        this.research.title = data.research.title;
        let idx = this.topResult.findIndex((item) => item.id === "title");
        this.topResult[idx].value = data.research.title;

        let allContentIdx = this.pageContent.findIndex(
          (item) => item.type === "allContent"
        );

        this.pageContent[allContentIdx].pageContent.backgroundContentHtml =
          data.research.background || "";
        this.pageContent[allContentIdx].pageContent.methodSampleContentHtml =
          data.research.methodSample || "";
        this.pageContent[allContentIdx].pageContent.resultAnalysisContentHtml =
          data.research.resultAnalysis || "";
        this.pageContent[
          allContentIdx
        ].pageContent.resultConclusionContentHtml =
          data.research.resultConclusion || "";

        let idx3 = this.topResult.findIndex((item) => item.id === "monthText");
        this.topResult[idx3].value = data.research.taskMonthly;
        this.monthText = data.research.taskMonthly;

        let idx4 = this.topResult.findIndex(
          (item) => item.id === "createTimeText"
        );
        this.topResult[idx4].value = data.research.reportTimeStr;
        this.collectionType = data.research.collectionType;
        let hidden = this.collectionType == 4;
        let iidx = this.pageContent.findIndex(
          (item) => item.type === "projectExecutorInformation"
        );
        if (iidx !== -1) {
          this.pageContent[iidx].hidden = hidden;
        }
        // this.pageContent[researchMethodsAndSamplesIdx].pageContent.contentHtml =
        //   data.research.methodSample || "";
        // this.pageContent[analysisOfResearchResultsIdx].pageContent.contentHtml =
        //   data.research.resultAnalysis || "";
        // this.pageContent[
        //   researchConclusionsOrSuggestionsIdx
        // ].pageContent.contentHtml = data.research.resultConclusion || "";
      }

      if (data.seoTask instanceof Object) {
        // let idx = this.topResult.findIndex(item => item.id === 'projectName');
        // let idx2 = this.topResult.findIndex(item => item.id === 'serviceName');
        // this.topResult[idx].value = data.seoTask.companyName
        // this.topResult[idx2].value = data.seoTask.tenantName
      }
    },
    // 医院等级
    initHospitalStatistics() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);
      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".barTarget .chart11")
      );

      const data = [
        { x: 0, y: 0, z: 0, value: 10, color: "#ff0000" },
        { x: 1, y: 1, z: 1, value: 20, color: "#00ff00" },
        // ... 更多数据
      ];
      const option4 = {
        series: [
          {
            name: "访问来源",
            type: "pie3D",
            data: [
              { value: 335, name: "直接访问" },
              { value: 310, name: "邮件营销" },
              { value: 234, name: "联盟广告" },
              { value: 135, name: "视频广告" },
              { value: 1548, name: "搜索引擎" },
            ],
            labelLine: {
              length: 10,
              length2: 10,
            },
            startAngle: -30,
            clockwise: false,
            label: {
              normal: {
                show: true,
                position: "outside",
                formatter: "{b}",
              },
            },
          },
        ],
      };

      //3 把配置给实例对象
      myCharts4.setOption(option4);
      //4 让图标自适应
      window.addEventListener("resize", function () {
        myCharts4.resize();
      });
    },
    // 药店统计
    initPharmacyStatistics() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);
      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".barTarget .chart10")
      );

      let option4 = {
        xAxis: {
          type: "category",
          data: [
            "药店一",
            "药店二",
            "药店三",
            "药店四",
            "药店五",
            "药店六",
            "药店七",
            "药店八",
            "药店九",
            "其他",
          ],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130, 20, 20, 330],
            type: "bar",
          },
        ],
      };

      //3 把配置给实例对象
      myCharts4.setOption(option4);
      //4 让图标自适应
      window.addEventListener("resize", function () {
        myCharts4.resize();
      });
    },
    // 年龄统计
    initAgeStatistisc() {
      let that = this;

      if (this.userReportVo.ageReportVoList) {
        let ageReportVoList = this.userReportVo.ageReportVoList;

        for (let i = 0; i < ageReportVoList.length; i++) {
          for (let j = 0; j < this.ageResult.length; j++) {
            if (this.ageResult[j].uuid === ageReportVoList[i].descValue) {
              this.ageResult[j].value = ageReportVoList[i].num;
              let percent =
                ageReportVoList[i].num !== 0
                  ? (
                      (ageReportVoList[i].num / ageReportVoList[i].allNum) *
                      100
                    ).toFixed(2)
                  : "0.00";
              this.ageResult[j].name = this.ageResult[j].oName + percent + "%";
              this.ageResultCount = ageReportVoList[i].allNum;
              this.projectExecutorInformation[ageReportVoList[i].descValue] =
                percent + "%";

              break;
            }
          }
        }
      }
      const dataResult = [
        ...this.ageResult,
        {
          // make an record to fill the bottom 50%
          value: that.ageResultCount == 0 ? 1 : that.ageResultCount,
          // opacity:0,
          itemStyle: {
            normal: { color: "#edf3ff" },
          },
          // color: '#FF488A'},
          label: {
            show: false,
          },
        },
      ];
      if (that.ageResultCount === 0) {
        dataResult.push({
          // make an record to fill the bottom 50%
          value: that.ageResultCount == 0 ? 1 : that.ageResultCount,
          // opacity:0,
          itemStyle: {
            normal: { color: "#edf3ff" },
          },
          // color: '#FF488A'},
          label: {
            show: false,
          },
        });
      }

      console.log("this.ageResult", dataResult, this.ageResultCount);
      const str = this.uuid;
      const dom = document.getElementById(str);

      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".leftLine .chart")
      );

      let option4 = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          show: false,
          normal: {
            top: "0%",
          },
          // left: "center",
          // doesn't perfectly work with our tricks, disable it
          // selectedMode: false,
        },
        series: [
          {
            // name: "Access From",
            type: "pie",
            radius: ["40%", "60%"],
            center: ["50%", "60%"],
            // adjust the start angle
            startAngle: 180,
            label: {
              show: true,
              formatter(param) {
                // correct the percentage
                return param.name;
              },
            },
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
              // emphasis: {
              //   label: {
              //     show: true,
              //     position: "center",
              //     textStyle: {
              //       fontSize: "14",
              //       // fontWeight:'bold'
              //     },
              //   },
              // },
            },
            data: dataResult,
          },
        ],
        // tooltip: {
        //   trigger: "item",
        // },
        // // legend: {
        // //   top: "5%",
        // //   left: "center",
        // // },
        // series: [
        //   {
        //     name: "Access From",
        //     type: "pie",
        //     radius: ["30%", "50%"],
        //     avoidLabelOverlap: false,
        //     label: {
        //       show: false,
        //       position: "center",
        //     },
        //     emphasis: {
        //       label: {
        //         show: false,
        //         fontSize: 40,
        //         fontWeight: "bold",
        //       },
        //     },
        //     labelLine: {
        //       show: false,
        //     },
        //     data: [
        //       { value: 1048, name: "18-30周岁" },
        //       { value: 735, name: "31-40周岁" },
        //       { value: 580, name: "41-50周岁" },
        //       { value: 484, name: "51-70周岁" },
        //       { value: 300, name: "70周岁以上" },
        //     ],
        //   },
        // ],
      };

      //3 把配置给实例对象
      myCharts4.setOption(option4);
      //4 让图标自适应
      window.addEventListener("resize", function () {
        myCharts4.resize();
      });
    },
    // 性别 chart20
    initSexStatisisc() {
      let that = this;

      const dataResult = [
        ...this.sexResult,
        {
          // make an record to fill the bottom 50%
          value: that.sexResultCount == 0 ? 1 : that.sexResultCount,
          // opacity:0,
          itemStyle: {
            normal: { color: "#edf3ff" },
          },
          // color: '#FF488A'},
          label: {
            show: false,
          },
        },
      ];
      if (that.sexResultCount === 0) {
        dataResult.push({
          // make an record to fill the bottom 50%
          value: that.sexResultCount == 0 ? 1 : that.sexResultCount,
          // opacity:0,
          itemStyle: {
            normal: { color: "#edf3ff" },
          },
          // color: '#FF488A'},
          label: {
            show: false,
          },
        });
      }

      // console.log("this.ageResult", dataResult, this.ageResultCount);
      const str = this.uuid;
      const dom = document.getElementById(str);

      //1 实例化对象
      let myCharts4 = window.echarts.init(dom.querySelector("#chart20"));

      let option4 = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          show: false,
          normal: {
            top: "0%",
          },
          // left: "center",
          // doesn't perfectly work with our tricks, disable it
          // selectedMode: false,
        },
        series: [
          {
            // name: "Access From",
            type: "pie",
            radius: ["40%", "60%"],
            center: ["50%", "60%"],
            // adjust the start angle
            startAngle: 180,
            label: {
              show: true,
              formatter(param) {
                // correct the percentage
                return param.name;
              },
            },
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
              // emphasis: {
              //   label: {
              //     show: true,
              //     position: "center",
              //     textStyle: {
              //       fontSize: "14",
              //       // fontWeight:'bold'
              //     },
              //   },
              // },
            },
            data: dataResult,
          },
        ],
      };

      //3 把配置给实例对象
      myCharts4.setOption(option4);
      //4 让图标自适应
      window.addEventListener("resize", function () {
        myCharts4.resize();
      });
    },

    // 待处理
    initAuditingStatisisc() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);
      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".rightLine .chart4")
      );

      const data = [];
      data.push({
        name: that.auditResult[0].name,
        value: that.auditResult[0].value,
      });
      if (
        that.auditResultTotal - that.auditResult[0].value === 0 &&
        that.auditResult[0].value !== 0
      ) {
      } else {
        data.push({
          name: "其他",
          value: that.auditResultTotal - that.auditResult[0].value,
        });
      }

      nandingBingEchart(data, myCharts4, 0);
      // const auditResult = [
      //   that.auditResult[0],
      //   {
      //     // make an record to fill the bottom 50%
      //     value:
      //       that.auditResultTotal === 0
      //         ? 100
      //         : that.auditResultTotal - that.auditResult[0].value,
      //     // that.auditResultTotal,
      //     // opacity:0,
      //     itemStyle: {
      //       color: "#FFFFFF",
      //       // normal: { color: "#FFFFFF" },
      //     },
      //     // color: '#FF488A'},
      //     label: {
      //       show: false,
      //     },
      //   },
      // ];
      // initRingChart(myCharts4, auditResult);

      //3 把配置给实例对象
      // myCharts4.setOption(option4);
      //4 让图标自适应
      // window.addEventListener("resize", function () {
      //   myCharts4.resize();
      // });
    },
    // 处理中
    initAuditSuccessStatisisc() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);
      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".rightLine .chart2")
      );

      const data = [];
      data.push({
        name: that.auditResult[1].name,
        value: that.auditResult[1].value,
      });

      if (
        that.auditResultTotal - that.auditResult[1].value === 0 &&
        that.auditResult[1].value !== 0
      ) {
      } else {
        data.push({
          name: "其他",
          value: that.auditResultTotal - that.auditResult[1].value,
        });
      }
      nandingBingEchart(data, myCharts4, 1);
    },
    // 已处理
    initAuditErrorStatisisc() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);
      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".rightLine .chart3")
      );

      const data = [];
      data.push({
        name: that.auditResult[2].name,
        value: that.auditResult[2].value,
      });

      if (
        that.auditResultTotal - that.auditResult[2].value === 0 &&
        that.auditResult[2].value !== 0
      ) {
      } else {
        data.push({
          name: "其他",
          value: that.auditResultTotal - that.auditResult[2].value,
        });
      }

      nandingBingEchart(data, myCharts4, 2);
    },
    // 已关闭
    initAuditCloseStatisisc() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);
      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".rightLine .chart24")
      );
      const data = [];
      data.push({
        name: that.auditResult[3].name,
        value: that.auditResult[3].value,
      });

      if (
        that.auditResultTotal - that.auditResult[3].value === 0 &&
        that.auditResult[3].value !== 0
      ) {
      } else {
        data.push({
          name: "其他",
          value: that.auditResultTotal - that.auditResult[3].value,
        });
      }

      nandingBingEchart(data, myCharts4, 3);
    },

    // 已撤销
    initAuditAlwayCloseStatisisc() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);
      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".rightLine .chart22")
      );
      const data = [];
      data.push({
        name: that.auditResult[4].name,
        value: that.auditResult[4].value,
      });

      if (
        that.auditResultTotal - that.auditResult[4].value === 0 &&
        that.auditResult[4].value !== 0
      ) {
      } else {
        data.push({
          name: "其他",
          value: that.auditResultTotal - that.auditResult[4].value,
        });
      }
      nandingBingEchart(data, myCharts4, 4);
    },

    // 项目执行人任务数量分析
    initProductAnalysis() {
      let that = this;
      const str = this.uuid;
      const dom = document.getElementById(str);

      // taskAllocationReportVoList
      let cData = [];
      var obj = {
        num_0_18: 1,
        num_18_36: 2,
        num_36_54: 3,
        num_54_72: 4,
        num_72_90: 5,
        num_72_: 6,
      };
      for (let key in obj) {
        cData.push(0);
      }
      for (let i = 0; i < this.taskAllocationReportVoList.length; i++) {
        let idxx = obj[this.taskAllocationReportVoList[i].descValue];
        if (idxx) {
          cData[idxx - 1] = this.taskAllocationReportVoList[i].num;

          let percent = (
            (this.taskAllocationReportVoList[i].num /
              this.taskAllocationReportVoList[i].allNum) *
            100
          ).toFixed(2);
          this.productAnalysisInfo[
            this.taskAllocationReportVoList[i].descValue
          ] = percent + "%";
          // this.taskAllocationReportVoList[i]
          let str2 = this.taskAllocationReportVoList[i].descValue + "_count";
          this.productAnalysisInfo[str2] =
            this.taskAllocationReportVoList[i].num;
          // this.productAnalysisInfo[this.taskAllocationReportVoList[i].descValue] = this.taskAllocationReportVoList[i].num;
        }
      }
      console.log("cData", cData, this.taskAllocationReportVoList);

      //1 实例化对象
      let myCharts4 = window.echarts.init(
        dom.querySelector(".barTarget .chart")
      );

      let option4 = {
        grid3D: {},
        xAxis3D: {
          type: "value",
        },
        yAxis3D: {
          type: "category",
          data: ["0-18", "18-36", "36-54", "54-72", "72-90", "90单以上"],
          axisLabel: {
            formatter: "{value}",
          },
        },
        zAxis3D: {},
        // xAxis: {
        //   type: "value",
        // },
        // yAxis: {
        //   type: "category",
        //   data: ["0-18", "18-36", "36-54", "54-72", "72-90", "90单以上"],
        //   axisLabel: {
        //     formatter: "{value}",
        //   },
        //   // data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        // },
        series: [
          {
            data: cData,
            // type: "bar",
            type: "bar3D",
          },
        ],
      };

      //3 把配置给实例对象
      initColumnar(myCharts4, cData);
      // myCharts4.setOption(option4);
      //4 让图标自适应
      window.addEventListener("resize", function () {
        myCharts4.resize();
      });
    },
    // 获取拜访详情
    async queryOne() {
      const res = await queryOne({ id: this.taskId, isCurrentUser: 2 });

      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (idx !== -1) {
        console.log("kkk");
        this.pageContent[idx].pageContent.articleContent = res.data.content;
        this.exportVisitType = res.data.type - 0;

        //  item.value = [res.data.startTime, res.data.endTime];
        this.pageContent[idx].pageContent.planTime =
          res.data.startTime + " 至 " + res.data.endTime;

        this.$forceUpdate();
      }
      let timer = this.timer

      this.$nextTick(() => {
        setTimeout(() => {
          this.updateSuccess();
        }, timer);
      });

      // this.updateSuccess();

      console.log("res", res);
    },

    // 获取拜访列表
    async getVisitCordList(type) {
      const res = await queryList({
        type: type,
        planId: this.taskId,
      });

      const data = res.data;
      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (type === 1) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.defaultTableData = data;
        }
      } else if (type === 2) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.characterTableData = data;
        }
      } else if (type === 3) {
        if (idx !== -1) {
          const drugstoreFeedbackArr = getdrugstoreFeedbackList();

          this.pageContent[idx].pageContent.pharmacyTableData = data.map(
            (item) => {
              item.writeTimeText = getIOSTime(item.writeTime);
              item.drugstoreFeedbackText = this.getEnumText(
                item.drugstoreFeedback,
                drugstoreFeedbackArr
              );
              return item;
            }
          );
        }
      }

      this.$nextTick(() => {
        this.updateSuccess();
      });

      // return data;
    },
    async loadChart() {
      this.initAuditErrorStatisisc();
      this.initAuditSuccessStatisisc();
      this.initAuditingStatisisc();
      // await this.syncStatisisItem(() => {

      // });
      // await this.syncStatisisItem(() => {

      // });
      // await this.syncStatisisItem(() => {

      // });
      // await this.syncStatisisItem(() => {
      //   this.initProductAnalysis();
      // });
      // await this.syncStatisisItem(() => {
      //   this.initAuditCloseStatisisc();
      // });
      // await this.syncStatisisItem(() => {
      //   this.initAuditAlwayCloseStatisisc();
      // });
    },
    async updateSuccess(is) {
      if (!is) {
        this.initsuccesscount += 1;
      }

      console.log(
        "this.initsuccesscount",
        this.initsuccesscount,
        this.targetCount
      );

      if (
        this.initsuccesscount + this.chartCount ===
        this.targetCount + this.chartCountLength
      ) {
        await this.loadChart();
        this.pageLoading = false;
        let timer = this.timer

        this.$nextTick(() => {
          this.initpage();
          setTimeout(() => {
            this.$emit("compute", {});
            this.addComputeTag();
          }, timer);
        });
      }
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    initpageData() {
      // 获取详情
      this.queryOne().then((res) => {
        if (this.exportVisitType === 1) {
          // 获取拜访记录- 默认
          this.getVisitCordList(1);
        } else if (this.exportVisitType === 2) {
          // 获取拜访记录- 人物
          this.getVisitCordList(2);
        } else if (this.exportVisitType === 3) {
          // 获取拜访记录- 药店
          this.getVisitCordList(3);
        }
      });

      // this.seotaskexecutorplangetlist();

      // this.getCountDetail();
    },

    initpage() {
      this.authHeightResult = {};
      for (let i = 0; i < this.pageContent.length; i++) {
        if (this.pageContent[i].authHeight) {
          let id = "authHeight" + i;
          this.authHeightResult[i] = id;
        }
      }

      this.$nextTick(() => {
        this.save();
      });
    },
    // 页面的倍数
    initHeight(height) {
      let pagecount = 1;
      while (height > this.pageSize.height) {
        pagecount += 1;
        height -= this.pageSize.height;
      }

      return pagecount * this.pageSize.height;
    },
    // 保存前初始化页面
    save() {
      for (let key in this.authHeightResult) {
        let id = this.authHeightResult[key];

        let dom = document.getElementById(id);
        console.log(dom.clientHeight, this.pageContent[key]);
        // this.pageContent[key].targetHeight = this.initHeight(dom.clientHeight);
        this.pageContent[key].targetHeight = dom.clientHeight;
      }
      this.$forceUpdate();
    },
  },
};
</script>




<style lang="scss" scoped>
$ColorMain: #4787ff;
.ps50 {
  margin-top: 50px;
  margin-bottom: 50px;
}
.bgMain {
  background: #edf3ff;
}
.report-content-box {
  background: #edf3ff;
}
.pageContent {
  // font-family: "Microsoft YaHei", sans-serif;
  zoom: 0.70381;
}
#chart20 {
  width: 100%;
  height: 100%;
}
.information-c {
  margin-bottom: 30px;
}
.bg-top-theme {
  height: 325px;
  display: flex;
  align-items: center;
  padding: 0px 30px;
  .bg-top-theme-l {
    flex: 1;
    color: $ColorMain;
    padding-right: 60px;
  }
  .bg-top-theme-info-t {
    font-weight: 550;
    width: 250px;
    overflow: hidden;
  }
  .bg-top-img {
    width: 255px;
    height: 168px;
    // height: 208px;
  }
  .analysis-report-t {
  }
  .bgimg {
    width: 100%;
    height: auto;
  }
  .bg-top-theme-info-t {
    font-size: 28px;
    font-weight: 500;
    line-height: 1.5;
    color: $ColorMain;
  }
  .bg-top-theme-info {
    margin-top: 30px;
    background: #fff;
    display: inline-block;
    font-size: 22px;
    line-height: 2;
    padding: 0 20px;
    border-radius: 20px;
  }
}
.bg-top-border-one {
  // height: 300px;
  padding: 10px;
  box-sizing: border-box;
  background: #cbdeff;
  border-radius: 10px;
  margin-bottom: 20px;
  .bg-top-border-two {
    background: #fff;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    padding: 30px 45px 48px 30px;
  }
  .bg-top-border-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  .bg-top-border-item:last-child {
    margin-bottom: 0;
  }
  .bg-top-border-label {
    min-width: 230px;
    width: 230px;
    display: flex;
    align-items: center;
    color: $ColorMain;
  }
  .bg-top-border-value {
    flex: 1;
  }
  .bg-top-border-ico {
    width: 25px;
    height: 25px;
    margin-right: 10px;
  }
}
.user-content-box {
  background: #fff;
  overflow: hidden;
  .user-content-t {
    // margin-top: 40px;
    font-size: 28px;
    font-weight: 550;
    background: #cbdefe;
    line-height: 1.5;
    // padding: 0 20px;
    border-radius: 10px;
    // margin-bottom: 20px;
    // display: flex;
    // justify-content: center;
    display: inline-block;
    font-size: 18px;
    padding: 5px 20px;
    margin: 40px auto 20px;
  }
  .user-content-c {
    margin-bottom: 40px;
    padding: 0 20px;
    line-height: 1.5;
  }
  .user-d-flex {
    display: flex;
    justify-content: center;
  }
}
.border-l {
  // border-left: 1px solid #dbdbdb;
  padding-left: 20px;
}
.border-r {
  // border-right: 1px solid #dbdbdb;
  padding-right: 20px;
}
.border-t {
  // border-top: 1px solid #dbdbdb;
  padding-top: 15px;
}
.border-b {
  // border-bottom: 1px solid #dbdbdb;
  padding-bottom: 15px;
}
.border-top {
  // border-top: 1px solid #dbdbdb;
  // border-right: 1px solid #dbdbdb;
  // border-left: 1px solid #dbdbdb;

  padding-top: 15px;
  padding-right: 20px;
  padding-left: 20px;
}
.border-bottom {
  border-bottom: 1px solid #dbdbdb;
  border-right: 1px solid #dbdbdb;
  border-left: 1px solid #dbdbdb;

  padding-bottom: 10px;
  padding-right: 10px;
  padding-left: 10px;
}

.mg50 {
  margin-bottom: 50px;
  margin-top: 50px;
}
.reportContent-tip {
  line-height: 2;
  padding: 10px;
  text-align: right;
  font-size: 14px;
}
//饼图的大小
.chartsGl {
  height: 200px;
  width: 380px;
}
//饼图底座（我也想给你们底座图片 可是我不知道咋给）
.buttomCharts {
  background: center top
    url(data:image/png;base64,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)
    no-repeat;
  background-size: 100% 100%;
  height: 350px;
  width: 365px;
  margin-top: -439px;
  margin-left: -18px;
}

.reportContent-lt {
  font-size: 14px;
  font-weight: 550;
  padding: 20px 0;
}
.reportContent-t {
  font-size: 18px;
  text-align: center;
  font-weight: 550;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: $ColorMain;
}
.reportContent-tl {
  height: 7px;
  width: 150px;
  /* background: #737576; */
  margin-right: 20px;
  background: linear-gradient(to right, #edf3ff, #4e8cfd);
  border-radius: 40px;
  // border-radius: 2;
}
.reportContent-tr {
  height: 7px;
  width: 150px;
  /* background: #737576; */
  margin-left: 20px;
  background: linear-gradient(to right, #4e8cfd, #edf3ff);
  border-radius: 40px;
}
.reportContent-l {
  flex: 1;
}
.reportContent-box {
  .d-flex {
    display: flex;
  }
  .barTarget .chart {
    width: 100%;
    height: 100%;
  }
  .panel {
    height: 400px;
  }

  .barTarget .chart10,
  .barTarget .chart11 {
    width: 100%;
    height: 100%;
  }
  .table-right-txt {
    display: flex;
    justify-content: flex-end;
    line-height: 2;
    color: #7c7c7c;
    padding: 10px 0;
  }

  .project-executor-t {
    font-size: 16px;
    font-weight: 550;
    padding: 20px 0;

    // line-height: 2;
  }

  .information-border-one {
    // border: 2px solid #a0a1a4;
    padding: 5px;
    // background: #f8fbff;
    margin-top: 50px;
  }
  .information-border-two {
    // border: 1px solid #a0a1a4;
    padding: 20px;
    background: #fff;
  }
  .information-txt-box {
    // display: flex;
    // align-items: center;
    line-height: 1.5;
    flex-wrap: wrap;
    word-break: break-all;
    font-size: 16px;
  }
  .information-t {
    font-size: 16px;
    font-weight: 550;
    padding-right: 10px;
    display: flex;
    align-items: center;
  }
  .information-t-r {
    width: 5px;
    height: 5px;
    background: #000;
    border-radius: 50%;
    margin-right: 5px;
  }

  .barTarget .barChart {
    width: 100%;
    height: 100%;
  }
}

.project-executor-information-box {
  .leftLine .chart,
  .rightLine .chart,
  .rightLine .chart4,
  .rightLine .chart2,
  .rightLine .chart3,
  .barTarget .chart,
  .rightLine .chart24,
  .rightLine .chart22 {
    width: 100%;
    height: 100%;

    // .pannel-footer {
    //   position: absolute;
    //   width: 100%;
    //   bottom: 0;
    //   left: 0;
    //   &::before {
    //     position: absolute;
    //     content: "";
    //     left: 0;
    //     bottom: 0;
    //     width: 10px;
    //     height: 10px;
    //     border-left: 2px solid #02a6b5;
    //     border-bottom: 2px solid #02a6b5;
    //   }

    //   &::after {
    //     position: absolute;
    //     content: "";
    //     right: 0;
    //     bottom: 0;
    //     width: 10px;
    //     height: 10px;
    //     border-right: 2px solid #02a6b5;
    //     border-bottom: 2px solid #02a6b5;
    //   }
    // }
  }

  .panel {
    width: 100%;
    height: 300px;
    position: relative;
    // height: 100%;
  }
  .d-flex {
    display: flex;
    align-items: center;
  }
  .project-executor-information-l,
  .project-executor-information-r {
    flex: 1;
  }
  .project-executor-information-r {
    display: flex;
    justify-content: center;
    height: 300px;
    // align-items: center;
  }

  .age-tabs {
    display: flex;
    // align-items: center;
    align-items: flex-start;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 90%;
    min-height: 102px;
  }
  .age-tab-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-right: 10px;
    margin-bottom: 10px;
    line-height: 2;
  }
  .age-radio {
    width: 15px;
    height: 15px;
    margin-right: 10px;
    border-radius: 50%;
  }
  .pannel-footer {
    position: absolute;
    // height: 150px;
    top: 255px;
    bottom: 0;
    left: 0;
    right: 0;
    // background: yellow;
    width: 100%;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .age-tabs-t {
    // position: absolute;
    // bottom: 0;
    display: flex;
    justify-content: center;
    font-size: 14px;
    font-weight: 550;
    width: 40%;
    border: 1px solid #dbdbdb;
    line-height: 2;
    margin-bottom: 10px;
    width: 180px;
  }
  .age-tabs-info {
    font-size: 24px;
    display: flex;
    justify-content: center;
    font-weight: 550;
    width: 40%;
    // border: 1px solid #dbdbdb;
    line-height: 2;
    margin-bottom: 10px;
  }
  .sex-border-one {
    height: 300px;
    display: flex;
    width: 300px;
    justify-content: center;
    overflow: hidden;
    position: relative;
  }
  .sex-border-one-l {
    width: 70px;
    height: 70px;
    border-top: 1px dashed #dbdbdb;
    border-left: 1px dashed #dbdbdb;
    transform: rotate(-45deg);
    position: absolute;
    top: 10px;
    left: 0;
  }
  .sex-border-one-c {
    width: 185px;
    border-top: 1px dashed #dbdbdb;
    border-bottom: 1px dashed #dbdbdb;
  }
  .seximg-box {
    width: 168px;
    height: 95px;
    margin-top: 80px;
    position: relative;
  }
  .seximg {
    width: 168px;
    height: 95px;
  }
  .seximg-txt {
    position: absolute;
    bottom: 14px;
    left: 26px;
    font-size: 14px;
    color: #fff;
    width: 70px;
    text-align: center;
  }
  .seximg-txt-right {
    position: absolute;
    bottom: 37px;
    right: 5px;
    font-size: 14px;
    color: #fff;
    width: 70px;
    text-align: center;
  }

  .information-border-one {
    // border: 2px solid #a0a1a4;
    padding: 5px;
    background: #f8fbff;
    margin-top: 50px;
  }
  .information-border-two {
    // border: 1px solid #a0a1a4;
    padding: 20px;
    background: #fff;
  }
  .information-txt-box {
    // display: flex;
    // align-items: center;
    line-height: 1.5;
    flex-wrap: wrap;
    word-break: break-all;
    font-size: 16px;
  }
  .information-t {
    font-size: 16px;
    font-weight: 550;
    padding-right: 10px;
  }
  .project-executor-t {
    font-size: 16px;
    font-weight: 550;
    padding: 20px 0;

    // line-height: 2;
  }
  .pannel-footer3 {
    // background: yellow;
    width: 100%;
    flex-direction: column;
    display: flex;
    // justify-content: center;
    align-items: center;
    height: 180px;
  }
}
.analysis-report-box {
  .analysis-report-row {
    display: flex;
  }
  .analysis-report-t {
    font-size: 24px;
    font-weight: 550;
    line-height: 2;
    display: flex;
    justify-content: center;
    color: #7c7c7c;
  }
  .analysis-report-info {
    font-size: 18px;
    font-weight: 550;
    line-height: 2;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    color: #7c7c7c;
  }
  .analysis-report-form {
  }
  .border-one {
    // border: 2px solid #313131;
    border: 2px solid #a0a1a4;
    padding: 10px;
    background: #f8fbff;
  }
  .border-two {
    border: 1px solid #a0a1a4;
    padding: 20px;
  }
  .analysis-report-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  .analysis-report-item {
    flex: 1;
    display: flex;
    align-items: center;
  }
  .analysis-report-l {
    min-width: 80px;
    text-align: left;
    color: #7c7c7c;
    padding-right: 10px;
    font-size: 14px;
  }
  .analysis-report-r {
    color: #5d5e5e;
    font-size: 14px;
  }
}

::v-deep .header-row-class-name th {
  background: #dfe6ec;
}

.mgt30 {
  margin-top: 30px;
}
.pageContent {
  width: 100%;
}
.everyPage {
  overflow: hidden;
  padding: 0 0px 5px;
  box-sizing: border-box;
}
.everyPageContent {
  width: 100%;
  height: 100%;
  // background: #fff;
  background: #edf3ff;
}
// 封面
.cover-page {
  padding-top: 100px;
  padding-left: 50px;
  height: 100%;
  overflow: hidden;
  background: #fff;
  box-sizing: border-box;

  .cover-page-bottom-txt {
    // display:flex;
    margin-top: 50px;
    line-height: 2;
    font-size: 18px;
    text-align: center;
  }
  .cover-text-box-info {
    font-size: 16px;
    color: #7c7c7c;
  }

  .cover-text-box-bottom {
    position: absolute;
    bottom: 100px;
    left: 50px;
    color: #fff;
  }

  .cover-text-box-title {
    font-size: 40px;
    color: #fff;
  }

  .cover-page-icon {
    margin-bottom: 30px;
  }

  .cover-page-img-box {
    position: relative;
  }

  .cover-text-box {
    position: absolute;
    top: 100px;
    left: 30px;
  }

  .cover-page-img {
    width: 100%;
  }

  .cover-text-box-info {
    color: #fff;
    font-size: 24px;
  }
}
// 目录
.directory-page {
  padding: 20px;
  .directory-title {
    font-size: 28px;
    line-height: 2;
    font-weight: 550;
    margin-bottom: 30px;
  }
}

// 问卷活动调研
.serve-page {
  padding: 20px;

  .serve-page-title {
    height: 50px;
    // background: #3a78f1;
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 550;
    // color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0e0800;
  }

  .serve-page-execute-time {
    text-align: center;
    font-weight: 550;
    margin-bottom: 10px;
  }

  .serve-page-execute-sub1 {
    font-weight: 550;
    margin-bottom: 20px;
  }

  .serve-page-execute-center-t1 {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
    font-weight: 550;
  }

  .serve-page-execute-introduction {
    text-indent: 2em;
    color: #7c7c7c;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 40px;
  }

  .serve-cols {
    display: flex;
    align-items: center;
    height: 40px;
    // border-bottom: 1px solid #000;
    border-top: none;
  }
  .serve-col {
  }
  .serve-cols-title {
    padding: 20px;
    background: #ffefef;
    // border: 2px solid #de4040;
    line-height: 1.5;
    color: #de4040;
  }
  .border-bottom {
    border-bottom: 1px solid #000;
  }
  .serve-col-span {
    width: 130px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .serve-col-label {
    padding-left: 30px;
  }
  .serve-cols-info {
    line-height: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #edf3ff;
    color: blue;
  }
}

.person-serve-detail {
  padding: 20px;

  .person-serve-detail-title {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 550;
    font-size: 18px;
  }
  .person-serve-detail-until {
    display: flex;
    justify-content: flex-end;
  }
}

.one {
  background: yellow;
}
.two {
  background: blue;
}
</style>
