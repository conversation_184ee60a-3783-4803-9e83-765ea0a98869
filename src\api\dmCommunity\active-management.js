/* eslint-disable */
import requestV1 from "@/common/utils/modules/request";
import env from "@/config/env";
const prefix = "/dm/api/v1";
// 活动分页列表
export function mktactivityQueryPage(data) {
  return requestV1.postJson(`${prefix}/mktactivity/query/page`, data);
}

// 中奖记录分页列表
export function mktactivitylotteryRecord(data) {
  return requestV1.postJson(
    `${prefix}/mktactivitylotteryrecord/query/page`,
    data
  );
}
// 根据主键回显物流信息
export function queryOne(data) {
  return requestV1.get(`${prefix}/mktactivitylotteryrecord/query/one`, data);
}
// 上传物流单号
export function updateLogistics(data) {
  return requestV1.putJson(`${prefix}/mktactivitylotteryrecord/update`, data);
}
// 参与记录统计
export function participateStatistics(data) {
  return requestV1.postJson(
    `${prefix}/mktactivitylotteryrecord/participate/statistics`,
    data
  );
}
