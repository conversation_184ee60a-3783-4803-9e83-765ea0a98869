<!--表单选择组件-->
<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :label-width="config.width"
    :class="itemClass"
    :prop="config.name"
  >
    <el-select
      v-model="form.data.select"
      filterable
      :disabled="disabled"
      :multiple="config.multiple"
      :filterable="filterable"
      :class="childClass"
      :placeholder="config.placeholder"
      clearable
    >
      <el-option
        v-for="(item) in array"
        :key="item.key"
        :label="item.value"
        :value="item.key"
        >{{item.value}}</el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import validate from "@/common/utils/modules/validate";

  export default {
  name: 'Select',
  props: {
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '下拉框',
          name: 'select',
          placeholder: '请选择下拉框',
          rules: [
            { required: true, message: '请选择下拉框', trigger: 'change' }
          ],
          array: [],
          dicKey: '',
          disabled: false,
          multiple: false // 多选
        }
      }
    },
    // 能否编辑
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    // 是否能过滤
    filterable: {
      type: Boolean,
      required: false,
      default: false
    },
    // // 是否多选
    // multiple: {
    //   type: Boolean,
    //   required: false,
    //   default: false
    // },
    data: {
      required: false,
      default: ''
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    idx: {
      type: Number,
      default: 0
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          select: null
        }
      },
      array: []
    }
  },
  watch: {
    data: {
      handler(val) {
        console.log("edit select:",val)
        if (!this.$validate.isNull(val)) {
          this.form.data.select = val
        } else {
          this.form.data.select = ''
        }
        this.getDic()
        // console.log("val:",val)
      },
      deep: true
    },
    config: {
      handler(val) {
        if (val.array.length > 0) {
          this.array = val.array
        } else {
          this.array = []
        }
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', this.config.name, this.form.data.select, this.idx)
        if (!validate.isNull(this.form.data.select)) {
          const obj = this.array.find((item) => {
            // console.log(item, 'item')
            return item.key === this.form.data.select+ ''
          })
          this.$emit('getSelected', obj)
        }
      },
      deep: true
    }
  },
  mounted() {
    this.form.data.select = this.data
    // console.log()
    this.getDic()
  },
  methods: {
    /**
     * 当传入的参数配置array为''且dicKey的值不为空，请求获取dicKey的数据字典
     */
    getDic() {
      const ar = this.config.array
      if (ar.length > 0) {
        this.array = ar
        return
      }
      // if (!this.$validate.isNull(this.config.dicKey)) {
      //   this.$api.dic.getDicInfo({ 'dictType': this.config.dicKey }, (res) => {
      //     this.array = res
      //   })
      // }
    },
    reset() {
      this.form.data.select = null
    }
  }
}
</script>

<style lang="scss">

</style>
