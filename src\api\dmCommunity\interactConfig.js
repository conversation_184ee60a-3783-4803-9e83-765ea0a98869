/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 互动配置管理
 */

// 互动分页列表
export function getInteractConfigQueryPage (data) {
    return requestV1.postJson(`${prefix}/searchbuzzword/query/page`,data)
}

// 互动保存数据
export function getInteractConfigInsert (data) {
    return requestV1.postJson(`${prefix}/searchbuzzword/insert`,data)
}

// 互动根据主键单一查询
export function getInteractConfigQueryOne (data) {
    return requestV1.get(`${prefix}/searchbuzzword/query/one`,data)
}

// 互动根据主键单一更新
export function getInteractConfigUpdate (data) {
    return requestV1.putJson(`${prefix}/searchbuzzword/update`,data)
}
