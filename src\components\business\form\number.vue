<!--数字输入框-->
<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">
    <el-input
      v-model="form.data.number"
      :class="childClass"
      :placeholder="config.placeholder"
      type="number"
      style=""/>
  </el-form-item>
</template>

<script>
export default {
  name: 'Number',
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '数字输入',
          name: 'number',
          placeholder: '请填写数字输入',
          rules: [
            { required: true, message: '请输入数字输入', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: Number,
      required: true
    },
    // el-form-item的class名
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    // el-input的class名
    childClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          number: 0
        }
      }
    }
  },

  watch: {
    data: {
      handler(val) {
        this.form.data.number = Number(val)
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.number)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">

</style>
