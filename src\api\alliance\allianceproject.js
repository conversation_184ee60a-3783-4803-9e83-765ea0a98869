import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 项目列表
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/allianceproject/delete/batch/${data.ids}`)
}

// 根据id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/allianceproject/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/allianceproject/insert`, data)
}

// 多参数列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/allianceproject/query/list`, data);
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/allianceproject/query/page`, data);
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/allianceproject/query/one`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/allianceproject/update`, data)
}

// 更新启动状态
export function updateOpenStatus(data = {}) {
  return requestV1.get(`${prefix}/allianceproject/enable/${data.id}`, data)
}
// 通过companyId查询该供应商的问卷
export function queryResearchList(data) {
  return requestV1.postJson(`${prefix}/allianceproject/query/research/list`, data);
}
// 多列表查询 
export function queryNoTenantList(data) {
  return requestV1.get(`${prefix}/allianceproject/query/noTenant/list`, data);
}

// 克隆项目
export function cloneProject(data) {
  return requestV1.postForm(`${prefix}/allianceproject/clone/project`, data);
}
