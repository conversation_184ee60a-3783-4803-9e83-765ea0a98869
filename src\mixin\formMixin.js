/* eslint-disable eol-last */
// 表单页面通用mixin

export default {
  data() {
    return {
      formList: [] // 表单list
    }
  },
  methods: {
    getEnumText(value, list) {
      const itemType = list.find(item => item.value === value)
      return (itemType && Object.keys(itemType).length) ? itemType.label : ''
    },
    setFormData(id, key, value, list = this.formList) {
      list.forEach((item, index) => {
        if (item.id === id) {
          list[index][key] = value
        }
      })
    },
    getFormData(id, key, list = this.formList) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].id === id) {
          return list[i][key];
        }
      }
    },
    getImgUrlObj({ url, formData }) {
      this.setFormData(formData.id, 'value', url);
    },
  }
}