<template>
  <div class="sys-select">
    <el-select filterable clearable size="mini" v-model="systemId" :disabled="disabled" placeholder="请选择系统服务">
      <el-option
        v-for="item in sysList"
        :key="item.key"
        :label="item.value"
        :value="item.key"/>
    </el-select>
  </div>
</template>

<script>
export default {
  props: {
    sysId: {
      type: String,
      required: false,
      default: ''
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      systemId: "",
      sysList: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getSysInfo()
    })
  },
  methods: {
    // 获取系统信息
    getSysInfo() {
      this.$ext.common.getSystemInfoList((data)=>{
        this.sysList = data
      })
    }
  },
  watch: {
    systemId: {
      handler(value) {
        this.$emit('updateSys', value)
      }
    },
    sysId: {
      handler(value) {
        this.systemId = value
      }
    }
  }
}
</script>

<style lang="scss">
.sys-select {
  margin-left: 20px;
  display: inline-block;

  .el-input--medium .el-input__inner {
    height: 28px;
    line-height: 28px;
  }

  .el-input__icon {
    line-height: 28px;
  }
}
</style>
