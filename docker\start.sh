#!/usr/bin/env bash
chmod u+x *.sh
#根目录
docker build -t pc-web-test -f docker/Dockerfile .
#启动命令
#测试环境
docker run -p 8001:80 -d --name gb-web-pc-manage-test -v $PWD/dist:/usr/share/nginx/html/ nginx:alpine
#正式环境
docker run -p 8001:80 -d --name gb-web-pc-manage-prod -v $PWD/dist:/usr/share/nginx/html/ nginx:alpine

#权限环境
docker run -p 8003:80 -d --name gb-web-pc-auth-prod -v $PWD/dist:/usr/share/nginx/html/ nginx:alpine
