/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

const prefix = '/dm/api/v1'

/**
 * 每日辟谣管理
 */

// 辟谣类型-分页列表
export function refuterumortypeQueryPage (data) {
  return requestV1.postJson(`${prefix}/refuterumortype/query/page`,data)
}

// 辟谣类型-保存数据
export function refuterumortypeInsert (data) {
  return requestV1.postJson(`${prefix}/refuterumortype/insert`,data)
}

// 辟谣类型-单个查询
export function getRefuterumortypeQueryOne (data) {
  return requestV1.get(`${prefix}/refuterumortype/query/one`,data)
}

// 辟谣类型-更新数据
export function updateRefuterumortypeData (data) {
  return requestV1.putJson(`${prefix}/refuterumortype/update`,data)
}

// 辟谣类型-批量删除
export function BatchDeleteRefuterumortype (data) {
  return requestV1.deleteForm(`${prefix}/refuterumortype/delete/batch/${data.ids}`)
}

// 辟谣类型-单一删除
export function DeleteOneRefuterumortype (data) {
  return requestV1.deleteForm(`${prefix}/refuterumortype/delete/one/${data.id}`)
}

// 辟谣列表 ------
// 辟谣列表-分页列表
export function refuterumordetailQueryPage (data) {
  return requestV1.postJson(`${prefix}/refuterumordetail/query/page`,data)
}

// 辟谣列表-保存数据
export function refuterumordetailInsert (data) {
  return requestV1.postJson(`${prefix}/refuterumordetail/insert`,data)
}

// 辟谣列表-单个查询
export function getRefuterumordetailQueryOne (data) {
  return requestV1.get(`${prefix}/refuterumordetail/query/one`,data)
}


// 辟谣列表-更新数据
export function updateRefuterumordetailData (data) {
  return requestV1.putJson(`${prefix}/refuterumordetail/update`,data)
}

// 辟谣列表-批量删除
export function BatchDeleteRefuterumordetail (data) {
  return requestV1.deleteForm(`${prefix}/refuterumordetail/delete/batch/${data.ids}`)
}

// 辟谣列表-单一删除
export function DeleteOneRefuterumordetail (data) {
  return requestV1.deleteForm(`${prefix}/refuterumordetail/delete/one/${data.id}`)
}

// 辟谣统计 ------
// 辟谣统计-统计分页列表
export function refuterumorrecordQueryPage (data) {
  return requestV1.postJson(`${prefix}/refuterumorrecord/query/page`,data)
}

// 辟谣统计-人数统计
export function getRefuterumorrecordStatistic (data) {
  return requestV1.postJson(`${prefix}/refuterumorrecord/statistic`,data)
}
// 更新辟谣规则
export function refuterumorruleUpdate (data) {
  return requestV1.putJson(`${prefix}/refuterumorrule/update`,data)
}
// 获取默认辟谣规则
export function refuterumorruleQueryDefault (data) {
  return requestV1.get(`${prefix}/refuterumorrule/query/default`,data)
}
// 导入辟谣题目
export const importRefuterumordetail = env.ctx + '/dm/api/v1/refuterumordetail/import'


//帖子话题------------------------------------
// 话题-统计
export function postmessagetopicQueryStatistic (data) {
  return requestV1.postJson(`${prefix}/postmessagetopic/query/statistic`,data)
}
// 话题-统计分页
export function postmessagetopicQueryStatisticPage (data) {
  return requestV1.postJson(`${prefix}/postmessagetopic/query/statistic/page`,data)
}
// 话题-分页列表查询
export function postmessagetopicQueryPage (data) {
  return requestV1.postJson(`${prefix}/postmessagetopic/query/page`,data)
}
// 话题-保存数据
export function postmessagetopicInsert (data) {
  return requestV1.postJson(`${prefix}/postmessagetopic/insert`,data)
}
// 话题-更新数据
export function postmessagetopicUpdate (data) {
  return requestV1.putJson(`${prefix}/postmessagetopic/update`,data)
}
// 话题-根据主键单一查询
export function postmessagetopicQueryOne (data) {
  return requestV1.get(`${prefix}/postmessagetopic/query/one`,data)
}
// 话题-根据参数列表查询
export function postmessagetopicQueryList (data) {
  return requestV1.get(`${prefix}/postmessagetopic/query/list`,data)
}
// 话题-单一删除
export function postmessagetopicDeleteOne (data) {
  return requestV1.deleteForm(`${prefix}/postmessagetopic/delete/one/${data.id}`)
}
// 话题-批量删除
export function postmessagetopicDeleteBatch (data) {
  return requestV1.deleteForm(`${prefix}/postmessagetopic/delete/batch/${data.ids}`)
}

// 小葫芦客服设置 - 新增
export function customerserviceprofilesInsert (data) {
  return requestV1.postJson(`${prefix}/customerserviceprofiles/insert`,data)
}

// 小葫芦客服设置 - 更新
export function customerserviceprofilesUpdate (data) {
  return requestV1.putJson(`${prefix}/customerserviceprofiles/update`,data)
}

// 小葫芦客服设置 - 根据主键单一(无参数版)
export function customerserviceprofilesQueryNoParametersOne (data) {
  return requestV1.get(`${prefix}/customerserviceprofiles/query/no/parameters/one`,data)
}