import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 帖子运营配置-关联表
 */

// 热度配置保存
export function hotConfigSave (data) {
    return requestV1.postJson(`${prefix}/postmessagerulerelation/hot/config/save`, data)
}

// 帖子批量配置热度保存
export function hotConfigBatchSave (data) {
  return requestV1.postJson(`${prefix}/postmessagerulerelation/hot/config/batch/save`, data)
}



// 帖子配置保存
export function operateConfigSave (data) {
    return requestV1.postJson(`${prefix}/postmessagerulerelation/operate/config/save`, data)
}

// 帖子配置保存 - 商户端新增的
export function operateCircleUpdate (data) {
    return requestV1.postForm(`${prefix}/postmessage/ei/circle/update`, data)
}



// 热度配置分页查询
export function queryHotConfigPage (data) {
    return requestV1.postJson(`${prefix}/postmessagerulerelation/query/hot/config/page`, data)
}

// 访问配置分页查询
export function queryVisitConfigPage (data) {
    return requestV1.postJson(`${prefix}/postmessagerulerelation/query/visit/config/page`, data)
}

// 访问配置保存
export function visitConfigSave (data) {
    return requestV1.postJson(`${prefix}/postmessagerulerelation/visit/config/save`, data)
}

// 访问配置删除
export function visitConfigDel (data) {
    return requestV1.deleteForm(`${prefix}/postmessagerulerelation/delete/one/${data.id}`)
}

// 访问配置编辑
export function visitConfigEdit (data) {
    return requestV1.putJson(`${prefix}/postmessagerulerelation/hot/config/update`, data)
}

// 一键取消配置
export function cannelConfig (data) {
  return requestV1.postForm(`${prefix}/postmessagerulerelation/batch/cancel/hot/config`, data)
}

// 一键候补执行配置
export function postmessagerulerelationSendDelayMsg (data) {
  return requestV1.postJson(`${prefix}/postmessagerulerelation/send/delay/msg`, data)
}

