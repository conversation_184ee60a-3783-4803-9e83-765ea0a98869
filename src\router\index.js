import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */

/* eslint-disable */
export const constantRoutes = [
    // 白名单服务器导出路由和下载 
    {
      path: '/servePrint/download',
      component: resolve => require(['@/views/servePrint/download'], resolve),
      hidden: true
    },
    {
      path: '/servePrint',
      component: resolve => require(['@/views/servePrint/index'], resolve),
      hidden: true
    },  
    {
      path: '/servePrint/ground-card',
      component: resolve => require(['@/views/servePrint/ground-card'], resolve),
      hidden: true
    },
    {
      path: '/servePrint/downloadPage',
      component: resolve => require(['@/views/servePrint/downloadPage'], resolve),
      hidden: true
    },
    {
        path: '/redirect',
        component: Layout,
        hidden: true,
        children: [{
            path: '/redirect/:path(.*)',
            component: resolve => require(['@/views/redirect/index'], resolve)
        }]
    },
    {
        path: '/login',
        component: resolve => require(['@/views/login/index'], resolve),
        hidden: true,
        meta: {
          title: '绿葆物联网|广州绿葆网络发展有限公司|绿葆物联网DM|绿葆网络官网'
        }
    },
    {
        path: '/auth-redirect',
        component: resolve => require(['@/views/login/auth-redirect'], resolve),
        hidden: true
    },
    {
        path: '/404',
        component: resolve => require(['@/views/error-page/404'], resolve),
        hidden: true
    },
    {
        path: '/401',
        component: resolve => require(['@/views/error-page/401'], resolve),

        hidden: true
    },
    {
        path: '/bindSuccess',
        component: resolve => require(['@/views/bindSuccess/index'], resolve),
        hidden: true
    },

    {
        path: '/',
        component: Layout,
        redirect: '/index',
        hidden: true,
        children: [{
            path: 'index',
            component: resolve => require(['@/views/index/index'], resolve),
            name: 'Index',
            meta: { title: 'index', icon: 'dashboard', affix: true }
        }]
    }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [

    /** when your routing map is too long, you can split it into small modules **/
    // 404 page must be placed at the end !!!


    { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
    // mode: 'hash', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
})

const router = createRouter()
router.beforeEach((to, from, next) => {
    // console.log(from, to)
    next()
})

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-*********
export function resetRouter() {
    const newRouter = createRouter()
    router.matcher = newRouter.matcher // reset router
}

export default router