/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'
const prefix2 = '/dm/api/v2'
import env from '@/config/env'

/**
 * 小程序渠道链管理-访问记录
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/minichannellinklog/insert`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/minichannellinklog/query/page`, data)
}
// 分页查询-陪诊
export function queryAccompanyLogPage (data) {
    return requestV1.postJson(`${prefix}/minichannellinklog/query/accompanyPage`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/minichannellinklog/update`, data)
}

// 根据主键单一查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/minichannellinklog/query/one`, data)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/minichannellinklog/delete/one/${data.id}`)
}

// 数据统计
export function queryStatisticsTotal (data) {
  return requestV1.postJson(`${prefix}/minichannellinklog/query/statistics/total`, data)
}
// 商户端数据统计
export function queryStatisticsMerchantTotal (data) {
  // return requestV1.postJson(`${prefix}/minichannellinklog/query/statistics/total`, data)
  return requestV1.postJson(`${prefix}/minichannellinklog/query/statistics/merchant/total`, data)
}
export function queryStatisticsMerchantTotalV2 (data) {
  // return requestV1.postJson(`${prefix}/minichannellinklog/query/statistics/total`, data)
  return requestV1.postJson(`${prefix2}/minichannellinklog/query/statistics/merchant/total`, data)
}
// 数据统计-陪诊
export function queryStatisticsMerchantAccompanyTotal (data) {
  // return requestV1.postJson(`${prefix}/minichannellinklog/query/statistics/total`, data)
  return requestV1.postJson(`${prefix}/minichannellinklog/query/statistics/accompanyTotal`, data)
}

// 商户端新增的 分页查询
export function queryMerchantPage (data) {
    return requestV1.postJson(`${prefix}/minichannellinklog/query/merchant/page`, data)
}
// 渠道链导出-陪诊
export async function linklogExportAccompany(data){
    return requestV1.download(`${prefix}/minichannellinklog/exportAccompany`, data, `渠道链流水.xlsx`, 'post',true)
}
