/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 在线会议-参会人员
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/meetingviewuser/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/meetingviewuser/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/meetingviewuser/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/meetingviewuser/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/meetingviewuser/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/meetingviewuser/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/meetingviewuser/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/meetingviewuser/update`, data)
}

// 修改是否禁言
export function updateMuteStatus (data) {
    return requestV1.postForm(`${prefix}/meetingviewuser/update/mute/status`, data)
}

// 获取参会人员
export function getUserListByMainid (data) {
    return requestV1.get(`${prefix}/meetingviewuser/get/user/list/by/mainid`, data)
}

// 修改是否观看
export function updateWatchStatus (data) {
    return requestV1.postForm(`${prefix}/meetingviewuser/update/watch/status`, data)
}
