<template>
    <div>
        <el-upload
            :drag="drag"
            ref="uploadCom"
            :action="doUpload"
            list-type="text"
            :before-upload="handleBeforeUpload"
            :on-success="handleSuccess"
            :before-remove="handleRemove"
            :on-preview="handlePreview"
            :headers="uploadHeaders"
            :multiple="multiple"
            :disabled="disabled"
            name="files"
            :limit="limit"
            :data="{ groupId, relId }"
            :file-list="realFileList"
            :accept="accept"
            :on-progress="onProgress"
            :form-data="formData"
            v-bind="$attrs"
            v-on="$listeners"
        >
            <template v-if="drag">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </template>
            <template v-else>
              <el-button size="mini" type="primary" >点击上传</el-button>
            </template>
            <div slot="tip" class="el-upload__tip" v-if="$slots.tip"><slot name="tip" /></div>
        </el-upload>
    </div>
</template>

<script>
import { doUpload, deleteByPath } from '@/api/index'
import { getToken } from '@/utils/auth'
import { imgServer } from '@/api/config'

export default {
  props: {
    drag: {
      type: Boolean,
      default: () => false
    },
    accept: {
      type: String,
      default: () => {
        return '.mp3,.mp4,.pdf,.doc,.docx,.xlsx,.xls'
      }
    },
    groupId: {
      type: [Number, String],
      default: 4001
    },
    relId: {
      type: String,
      default: function() {
        return ''
      }
    },
    value: {
      type: Array,
      default: null
    },
    limit: {
      type: Number,
      default: 2
    },
    multiple: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: Number,
      default: 100
    },
    formData: {
      type: Object,
      default() {
        return null
      }
    }
  },
  data() {
    return {
        doUpload,
        uploadHeaders: {
            Authorization: getToken()
        },
        imgServer,
        isInit: false,
        // 上传文件列表
        uploadFileArr: [],
        // 查看上传文件列表
        realFileList: []
    }
  },
  watch: {
    value() {
        if(!this.$validate.isNull(this.value)) {
          if(!this.isInit) {
            this.isInit = true
            this.initFilesFormat()
          }
        } else {
          this.isInit = false
          this.realFileList = []
          this.uploadFileArr = []
        }
    }
  },
  mounted() {
    this.initFilesFormat()
  },
  methods: {
    abort (list) {
      list = list || this.uploadFileArr
      this.$refs.uploadCom.abort(list)
    },
    handlePreview (file) {
        window.open(file.url)
    },
    onProgress() {
        this.$emit('onProgress', ...arguments)
    },
    handleRemove (file) {
        const index = this.uploadFileArr.findIndex(item => item.dir === file.dir)
        if(!this.$validate.isNull(index)) {
            this.uploadFileArr.splice(index, 1)
        }
        this.$emit('change', this.uploadFileArr)
        this.$emit('getUrlObj', { url: this.uploadFileArr, formData: this.formData })
    },
    // 文件列表规范
    initFilesFormat () {
        if(!this.$validate.isNull(this.value)) {
            let list = []
            let uploadFileList = []
            this.value.forEach(item => {
                if(this.$validate.isNull(item)) return
                const prefixName = item.prefixName || item.name || ''
                list.push({
                    ...item,
                    name: prefixName.indexOf('.') != -1 ? prefixName : `${prefixName}.${item.suffix}`,
                    url: !item.dir ? '' : this.$validate.isURL(item.dir) ? item.dir : imgServer + item.dir
                })
                uploadFileList.push({
                  ...item,
                  name: prefixName.indexOf('.') != -1 ? prefixName : `${prefixName}.${item.suffix}`,
                  url: item.dir ? item.dir : ''
                })
            })

            this.uploadFileArr = uploadFileList
            this.realFileList = list
        }
    },
    beforeDestroy() {
      this.isInit = false
    },
    handleBeforeUpload (file) {
        console.log('before', file)
        this.$emit('beforeUpload', file)
        return true
    },
    /**
     * 根据字段过滤掉相同的数据
     * @param {Array} data 数据源
     * @param {String} key 字段名
     */
    filterResponse(data,key){
        let obj = {}
        data = data.reduce((item,next) => {
            obj[next[key]] ? '' : obj[next[key]] = true && item.push(next)
            return item
        },[])
        return data
    },
    handleSuccess (response,file,fileList) {
        this.isInit = true
        if(!this.$validate.isNull(response.data) && Array.isArray(response.data)) {
            response.data[0].uid = file.uid
            // response.data[0].file = file
            let is = false
            this.uploadFileArr.forEach((item, index) => {
              if(item.uid === file.uid) {
                this.uploadFileArr[index] = {
                  ...item,
                  ...response.data[0],
                  fileObj: file
                }
                is = true
              }
            })
            if(!is) {
              this.uploadFileArr.push({
                ...response.data[0],
                fileObj: file
              })
            }
            if (this.uploadFileArr.length === fileList.length) {
              this.realFileList = this.uploadFileArr.map(item => {
                return {
                  ...item,
                  url: !item.dir ? '' : this.$validate.isURL(item.dir) ? item.dir : imgServer + item.dir
                }
              })
            }
            this.$emit('getUrlObj', { url: this.uploadFileArr, formData: this.formData })
            this.$emit('change', this.uploadFileArr)
        }
    }

  }

}
</script>

<style lang="scss" scoped>
</style>

