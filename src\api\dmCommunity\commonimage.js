/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 图片管理
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/commonimage/delete/batch/${data.ids}`)
}
// 根据id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/commonimage/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/commonimage/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/commonimage/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/commonimage/query/one`, data)
}

// 评论审核分页
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/commonimage/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/commonimage/update`, data)
}

// 批量上传
export function batchInsert(data) {
  return requestV1.postJson(`${prefix}/commonimage/batch/insert`, data)
}
