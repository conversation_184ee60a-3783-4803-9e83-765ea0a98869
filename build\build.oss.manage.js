// npm run oss:**
// npm install aliyunoss-webpack-plugin --save-dev
// cnpm install aliyunoss-webpack-plugin --save-dev
var ossWebpackUpload = require('aliyunoss-webpack-plugin')

/**初始话
 *
 * buildPath: 需要上传的文件路径,支持整个文件夹的遍历。支持node-glob风格路径，具体可以参见node-glob的文档。
 region: oss的区域，如:oss-cn-shanghai。
 accessKeyId: 阿里云的权限访问的key。
 accessKeySecret: 阿里云的权限访问的secret。
 bucket: 阿里云OSS上的命名空间。
 deleteAll: 先删除oss上的代码之后再上传，默认为false
 generateObjectPath: 函数（可选），函数参数为上传的文件名，必须返回一个字符串作为文件名，默认为文件原名。通过该函数可以让使用者自定义上传的文件名或者修改oss的路径，比如加一个前缀等
 getObjectHeaders: 函数（可选），函数参数为上传的文件名，返回设置的headers对象，默认为空，具体可以参见ali-oss的文档
 internal: 使用内网还是外网（可选），默认外网
 */

var ossLoad = new ossWebpackUpload({
  buildPath: 'dist/**',
  region: 'oss-cn-shenzhen',//这里只填地域部分,不要复制的太全了
  accessKeyId: 'LTAI4GJ2BaLPQAvcgR5xGv8M',
  accessKeySecret: '******************************',
  bucket: 'lvbao-saas',
  deleteAll: true,
  generateObjectPath: function(filename,file) {
    return file.replace(/dist\//, '/static/manage/');
  },
  getObjectHeaders: function(filename) {
    return {
      Expires: 6000
    }
  }
});

/**上传 */
ossLoad.apply()



