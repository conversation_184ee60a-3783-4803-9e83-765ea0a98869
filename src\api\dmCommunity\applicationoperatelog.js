/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 外部应用服务/横幅位置/企微群/帖子-操作记录分页列表查询
 */

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/bannerapplicationoperatelog/query/page`, data);
}

// 统计数据
export function getStatistic (data) {
    return requestV1.postJson(`${prefix}/bannerapplicationoperatelog/get/statistic`, data)
}

// 应用服务分页列表
export function applicationserviceoperatelogQueryPage(data) {
    return requestV1.postJson(`${prefix}/applicationserviceoperatelog/query/page`, data);
}

// 应用服务统计数据
export function applicationserviceoperatelogGetStatistic (data) {
    return requestV1.postJson(`${prefix}/applicationserviceoperatelog/get/statistic`, data)
}

// 帖子操作流水分页
export function queryPostmessagePage (data,isNowMonth) {
    let ReqPrefix = isNowMonth ? prefix : '/dw/api/v1'
    return requestV1.postJson(`${ReqPrefix}/applicationoperatelog/query/postmessage/page`, data)
}

// 帖子操作流水统计数据
export function getPostmessageStatistic (data,isNowMonth) {
    let ReqPrefix = isNowMonth ? prefix : '/dw/api/v1'
    return requestV1.postJson(`${ReqPrefix}/applicationoperatelog/get/postmessage/statistic`, data)
}

// 圈子关注取关数据统计-关注统计
export function queryCircleclassifyAttentionTotal (data) {
    return requestV1.postJson(`${prefix}/applicationoperatelog/query/circleclassify/attention/total`, data)
}

// 圈子关注取关数据统计-分页
export function queryCircleclassifyAttentionPage (data) {
    return requestV1.postJson(`${prefix}/applicationoperatelog/query/circleclassify/attention/page`, data)
}

// 清洗数据
export function dataClean (data) {
  return requestV1.postForm(`${prefix}/applicationoperatelog/data/clean`, data)
}

// 应用服务导出接口
export function applicationoperatelogExport (data) {
  return requestV1.download(`${prefix}/applicationoperatelog/export`, data, `应用服务数据统计.xlsx`, 'post',{
    'content-type': 'application/json; charset=utf-8'
  })
}

// 横幅管理曝光量统计数据
export function applicationoperatelogQueryBannerPreviewPage (data) {
  return requestV1.postJson(`${prefix}/applicationoperatelog/query/banner/preview/page`, data)
}

// banner图导出接口
export function applicationoperatelogBannerPreviewExport (data) {
  return requestV1.download(`${prefix}/applicationoperatelog/banner/preview/export`, data, `banner图数据统计.xlsx`, 'post',{
    'content-type': 'application/json; charset=utf-8'
  })
}

// 横幅管理曝光量统计数据
export function applicationoperatelogQueryBannerPreviewPageV2 (data) {
  return requestV1.postJson(`/dm/api/v2/applicationoperatelog/query/banner/preview/page`, data)
}

// 统计数据 马甲
export function getPreviewStatistic (data) {
  return requestV1.postJson(`${prefix}/applicationoperatelog/query/banner/preview/statistic`, data)
}
