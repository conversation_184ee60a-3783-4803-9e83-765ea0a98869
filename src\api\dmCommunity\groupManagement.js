/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'
const prefix2 = '/manage/api'

/**
 * 引流号列表群管理
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/wxexternalcontactgroupchat/delete/batch/${data.ids}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/wxexternalcontactgroupchat/insert`, data)
}



// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/wxexternalcontactgroupchat/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/wxexternalcontactgroupchat/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/wxexternalcontactgroupchat/query/page`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/wxexternalcontactgroupchat/update`, data)
}

// 一键同步 
export function wxexternalcontactgroupchatoneKeySync(data) {
  return requestV1.postForm(`${prefix}/wxexternalcontactgroupchat/oneKeySync`, data)
}

// 单个同步
export function wxexternalcontactgroupchatsingleSync(data) {
  return requestV1.postForm(`${prefix}/wxexternalcontactgroupchat/singleSync`, data)
}


// 添加关联群 
export function wxexternalcontactgroupchatAddRelation(data) {
  return requestV1.postForm(`${prefix}/wxexternalcontactgroupchat/add/relation`, data)
}
// 编辑关联群
export function wxexternalcontactgroupchatEditRelation(data) {
  return requestV1.putForm(`${prefix}/wxexternalcontactgroupchat/edit/relation`, data)
}


// 关联群列表 wxexternalcontactgroupchat/relation/list/page
export function wxexternalcontactgroupchatListRelation(data) {
  return requestV1.postJson(`${prefix}/wxexternalcontactgroupchat/relation/list/page`, data)
}

// 关联群单一查询 
export function wxexternalcontactgroupchatRelationOne(data) {
  return requestV1.get(`${prefix}/wxexternalcontactgroupchat/relation/${data.id}`)
}


// 删除关联群 
export function wxexternalcontactgroupchatdeleteRelation(data) {
  return requestV1.deleteForm(`${prefix}/wxexternalcontactgroupchat/delete/relation`, data)
}


// 群成员详情
export function wxexternalcontactgroupchatmemberquerypage(data) {
  return requestV1.postJson(`${prefix}/wxexternalcontactgroupchatmember/query/page`, data)
}

// PutStatus批量更新
export function wxexternalcontactgroupchatupdateputstatus(data) {
  return requestV1.putJson(`${prefix}/wxexternalcontactgroupchat/update/putStatus`, data)
}


// 关联个微 
export function wxmemberaddrelationAdd(data) {
  return requestV1.postJson(`${prefix}/member/add/relation`, data)
}

// 个微用户列表
export function wxmemberList(data) {
  return requestV1.get(`${prefix}/member/query/list`, data)
}

// 个微成员分页 
export function wxmemberPage(data) {
  return requestV1.postJson(`${prefix}/member/query/page`, data)
}

// 根据个微-成员ID 查询详情
export function wxmemberListByIds(data) {
  return requestV1.postJson(`${prefix}/member/listByIds`, data)
}

// 个微-自营 分组列表 api/v1/member/query/relation/page
export function wxmemberqueryrelation(data) {
  return requestV1.postJson(`${prefix}/member/query/relation/page`, data)
}

// 个微-自营 绑定和解绑
export function wxmemberbinding(data) {
  return requestV1.postJson(`${prefix}/member/binding`, data)
}


// 个微-自营 查询分组条目详情
export function wxmemberaddrelationQueryOne(data) {
  return requestV1.get(`${prefix}/member/relation/${data.id}`)
}


// 个微-自营 查询分组条目详情编辑
export function wxmemberaddrelationEdit(data) {
  return requestV1.putForm(`${prefix}/member/edit/relation`, data)
}

// 个微-自营 没有关联的成员列表
export function wxmembernoGroupRelation(data) {
  return requestV1.postJson(`${prefix}/member/query/noGroupRelation`, data)
}

// 个微-自营 编辑成员 member/update
export function wxmemberupdate(data) {
  return requestV1.putJson(`${prefix}/member/update`, data)
}
// 个微-自营 单一成员查询 
export function wxmemberqueryone(data) {
  return requestV1.get(`${prefix}/member/query/one`, data)
}

// 视频号分组--关联列表
export function linkVideoGroupQueryPage(data) {
  return requestV1.postJson(`${prefix2}/wxAuth/video/page`, data)
}

// 视频号分组-关联操作
export function wxAuthGroupRelationAdd(data) {
  return requestV1.postJson(`${prefix2}/wxAuthGroupRelation/batch/add`, data)
}

// 批量删除-分组关联
export function wxAuthGroupRelationDeleteBatch(data) {
  return requestV1.deleteForm(`${prefix2}/wxAuthGroupRelation/delete/batch/${data.ids}`)
}

// 视频号-编辑关联操作 
export function wxAuthGroupRelationEdit(data) {
  return requestV1.postJson(`${prefix2}/wxAuthGroupRelation/edit`,data)
}