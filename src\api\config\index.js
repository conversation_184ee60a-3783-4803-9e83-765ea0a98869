/* eslint-disable */
let baseURL // 请求地址
let imgServer //图片地址
let logImageServer //
const header = window.location.href.indexOf('https') === -1 ? 'http://' : 'https://'
const NODE_ENV = process.env.NODE_ENV

/**
 * 修改useManberId获取对应地址在本地进行请求调试
 * **/

const useManberId = 9
const devManber = [
    { id: 1, url: '192.168.3.10:3030', desc: '锋' },
    { id: 2, url: '192.168.3.15:3030', desc: '潘' },
    { id: 3, url: '192.168.3.110:1122', desc: '测试服务器' },
    { id: 4, url: '192.168.3.13:3030', desc: '力军' },
    { id: 5, url: '192.168.3.45:3030', desc: '杰鑫' },
    { id: 6, url: 'saas.ngrok.greenboniot.cn', desc: '测试' },
    { id: 7, url: 'saas.greenboniot.cn', desc: '正式' },
    { id: 8, url: '127.0.0.1:7004', desc: '新服务调试' },
    { id: 9, url: '192.168.3.56:3030', desc: '桂春' },
]

//根据id获取请求地址
function getManber() {
    for (const i in devManber) {
        if (devManber[i].id === useManberId) {
            return header + devManber[i].url
        }
    }
}


const env = {
    'development': {
        baseURL: getManber(),
        imgServer: header + 'test-file.greenboniot.cn/',
        logImageServer: header + 'log.greenboniot.cn/'

    },
    'production': {
        baseURL: header + 'saas.greenboniot.cn',
        imgServer: header + 'file.greenboniot.cn/',
        logImageServer: header + 'log.greenboniot.cn/'
    },
    'staging': {
        baseURL: header + 'saas.ngrok.greenboniot.cn',
        imgServer: header + 'test-file.greenboniot.cn/',
        logImageServer: header + 'log.greenboniot.cn/'
    }
}

baseURL = env[NODE_ENV].baseURL
imgServer = env[NODE_ENV].imgServer
logImageServer = env[NODE_ENV].logImageServer

console.log('baseURL',baseURL);
export {
    baseURL,
    imgServer,
    logImageServer
}
