import { mapGetters } from 'vuex'
import env from '@/config/env/index'
export default {
  methods: {
    // 是否有权限
    isPermissionsVisible(permission) {
      console.log('env',env)
      if(!env.isOpenPermission || (env.isOpenPermission && this.permissions[permission])) {
        return true;
      } else {
        return false;
      }
    }
  },
  computed:{
    ...mapGetters(['permissions']),
  }
}