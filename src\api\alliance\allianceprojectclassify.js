import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 项目分类
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/allianceprojectclassify/delete/batch/${data.ids}`)
}

// 根据id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/allianceprojectclassify/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/allianceprojectclassify/insert`, data)
}

// 多参数列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/allianceprojectclassify/query/list`, data);
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/allianceprojectclassify/query/page`, data);
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/allianceprojectclassify/query/one`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/allianceprojectclassify/update`, data)
}

// 更新启动状态
export function updateOpenStatus(data = {}) {
  return requestV1.get(`${prefix}/allianceprojectclassify/enable/${data.id}`, data)
}
