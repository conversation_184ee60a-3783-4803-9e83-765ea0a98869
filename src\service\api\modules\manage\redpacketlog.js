/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 * 投放计划管理-任务红包订单
 */

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/redpacketlog/query/page`, data)
}

// 根据用户openId查询待领取红包订单
export function waitReceiveListByOpenId(data) {
  return requestV1.get(`${prefix}/api/v1/redpacketlog/waitReceiveList/${data.openId}`)
}
