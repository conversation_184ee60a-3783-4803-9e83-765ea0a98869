/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 病例征集活动-征集流程配置
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/casecollectflowconfig/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/casecollectflowconfig/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/casecollectflowconfig/add`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`/dm/api/v2/casecollectflowconfig/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/casecollectflowconfig/query/page`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/casecollectflowconfig/update`, data)
}

// 根据病例征集获取节点list 不过滤不生效状态
export function getListBymMainid(data) {
  return requestV1.get(`/dm/api/v1/casecollectflowconfig/get/list/by/mainid`, data)
}

// 根据病例征集获取节点list 过滤生效状态
export function getListBymMainidV2(data) {
  return requestV1.get(`/dm/api/v2/casecollectflowconfig/get/list/by/mainid`, data)
}

// 修改启动状态
export function updateOpenStatus(data) {
  return requestV1.postForm(`${prefix}/casecollectflowconfig/update/open/status`, data)
}

//获取用户提交详情
export function getCommitDetail(data) {
  return requestV1.get('/dm/api/v1/casecollectflowconfig/get/commit/detail', data)
}

// 层级审核统计 
export function getaudittaskexistRunningOne(data) {
  return requestV1.get('/dm/api/v1/audittask/existRunningOne', data)
}


// 获取已审核的记录 
export function audittasklogQueryByTaskIdAndItemId(data) {
  return requestV1.get('/dm/api/v1/audittasklog/queryByTaskIdAndItemId', data)
}

// 批量审核
export function casecollectsubmitlogCommitAuditBatch(data) {
  return requestV1.postJson('/dm/api/v1/casecollectsubmitlog/commit/audit/batch', data)
}

// 问卷获取选项比例 
export function casecollectsubmitlogGetFormTemplat(data) {
  return requestV1.get('/dm/api/v1/casecollectsubmitlog/get/form/templat/statistics', data)
}

