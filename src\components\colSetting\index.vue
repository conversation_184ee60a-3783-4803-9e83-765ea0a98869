<template>
  <!-- 列设置 -->
  <el-drawer :append-to-body="true" :modal-append-to-body="false"  :visible.sync="cvisible" title="列设置" size="450px" :before-close="handleClose">
    
    <div class="table-main">
      <el-table
        :data="colSettingArr"
        :border="true"
        row-key="prop"
        default-expand-all
        :tree-props="{ children: '_children' }"
      >
        <el-table-column prop="label" align="center" label="列名" />
        <el-table-column v-slot="scope" prop="isShow" align="center" label="显示">
          <el-switch v-model="scope.row.isShow" />
        </el-table-column>
        <!-- <el-table-column v-slot="scope" prop="sortable" align="center" label="排序">
          <el-switch v-model="scope.row.sortable" />
        </el-table-column> -->
        <!-- <template #empty>
          <div class="table-empty">
            <img src="@/assets/images/notData.png" alt="notData" />
            <div>暂无可配置列</div>
          </div>
        </template> -->
      </el-table>
    </div>
  </el-drawer>
</template>

<script>

export default {
  name:"colSetting",
  props:{
    colSetting:{
      type:Array,
      default:function (){
        return []
      }
    },
    visible:{
      type:Boolean,
      default:false
    },
  },

  watch:{
    visible(n){
      this.cvisible = n;
      if(n){
        this.colSettingArr = JSON.parse(JSON.stringify(this.colSetting))
      }


    }
  },
  methods:{
    handleClose(){
      this.$emit('close',this.colSettingArr)
    }
  },
  data(){
    return {
      cvisible:false,
      colSettingArr:[]
    }
  }
}

</script>
