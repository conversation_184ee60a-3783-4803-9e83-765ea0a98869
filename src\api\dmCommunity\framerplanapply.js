/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 创作者计划
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/framerplanapply/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/framerplanapply/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/framerplanapply/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/framerplanapply/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/framerplanapply/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/framerplanapply/update`, data)
}

// 审核数据
export function examine (data) {
  return requestV1.postJson(`${prefix}/framerplanapply/examine`, data)
}
