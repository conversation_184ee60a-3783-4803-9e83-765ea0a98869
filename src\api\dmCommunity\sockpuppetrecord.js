/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * 马甲列表
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/sockpuppetrecord/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/sockpuppetrecord/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/sockpuppetrecord/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/sockpuppetrecord/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/sockpuppetrecord/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/sockpuppetrecord/update`, data)
}

// 修改启动状态
export function switchOpenstatus (data) {
    return requestV1.get(`${prefix}/sockpuppetrecord/switch/openstatus`, data)
}

// 批量保存数据
export const batchInsert = env.ctx + '/dm/api/v1/sockpuppetrecord/batchInsert'
