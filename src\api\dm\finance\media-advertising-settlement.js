/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 * 财务管理-媒体广告投放结算申请
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/putplansettleapply/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/putplansettleapply/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/putplansettleapply/insert`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/putplansettleapply/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/putplansettleapply/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/putplansettleapply/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
  return requestV1.get(`${prefix}/putplansettleapply/query/param`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/putplansettleapply/update`, data)
}


// 审批驳回 
export function auditVeto(data) {
  return requestV1.get(`${prefix}/putplansettleapply/audit/veto/${data.id}`, data)
}

// 审批通过
export function auditPass(data) {
  return requestV1.get(`${prefix}/putplansettleapply/audit/pass/${data.id}`, data)
}

// 批量审批
export function batchAudit(data) {
  return requestV1.postForm(`${prefix}/putplansettleapply/batch/audit`, data)
}

// 媒体广告结算申请总金额统计
export function totalStatistics(data) {
  return requestV1.postJson(`${prefix}/putplansettleapply/total/statistics`, data)
}