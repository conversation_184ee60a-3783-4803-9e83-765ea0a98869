/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 积分规则配置
 */

// 积分规则-事件类型查询
export function getEventTypeList (data) {
    return requestV1.get(`${prefix}/pointrule/eventTypeList`, data)
}
// 积分规则-业务类型查询
export function getBusinessTypeList (data) {
    return requestV1.get(`${prefix}/pointrule/businessTypeList`, data)
}
// 获取栏目列表 
export function getMeetingclassify (data) {
    return requestV1.get(`${prefix}/meetingclassify/query/list`, data)
}
// 积分规则 分页
export function pointruleQueryPage (data) {
    return requestV1.postJson(`${prefix}/pointrule/query/page`, data)
}
// 积分规则 新增
export function pointruleInsert (data) {
    return requestV1.postJson(`${prefix}/pointrule/insert`, data)
}
// 积分规则 更新
export function pointruleUpdate (data) {
    return requestV1.putJson(`${prefix}/pointrule/update`, data)
}
// 积分规则 批量删除
export function pointruleDeleteBatch (ids) {
    return requestV1.deleteJson(`${prefix}/pointrule/delete/batch/${ids}`)
}
// 积分规则 指定删除
export function pointruleDeleteOne (id) {
    return requestV1.deleteJson(`${prefix}/pointrule/delete/one/${id}`)
}
