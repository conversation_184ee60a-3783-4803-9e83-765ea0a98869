/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 * 投放计划管理-小号投放监测任务
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/accountplanlisteningtask/delete/batch/${data.ids}`)
}

// 小号投放-保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/accountplanlisteningtask/insert`, data)
}

// 小号投放-根据主键单一查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/accountplanlisteningtask/query/one`, data)
}

// 小号投放-更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/accountplanlisteningtask/update`, data)
}

// 小号投放-分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/accountplanlisteningtask/query/page`,data)
}

// 小号投放任务子项-分页查询
export function itemQueryPage(data) {
  return requestV1.postJson(`${prefix}/accountplanlisteningtaskitem/query/page`,data)
}

// 小号投放-查询所有引流号
export function getList(data) {
  return requestV1.postJson(`/manage/api/wxAuth/queryList`,data)
}

//获取吸粉计划接口
export function putQueryPage(data) {
  return requestV1.postJson('/manage/api/wxFansPlan/queryPage', data);
}

// 小号投放-根据引流号查询旗下分组
export function authQueryPage(data) {
  return requestV1.postForm(`${prefix}/accountplanlisteningtask/auth/groups`,data)
}

// 小号投放-启动或取消任务
export function changeStatus(data) {
  return requestV1.postForm(`${prefix}/accountplanlisteningtask/lunch/switch`,data)
}

// 小号投放-启动或取消任务
export function batchChangeStatus(data) {
  return requestV1.postForm(`${prefix}/accountplanlisteningtask/batch/lunch/switch`,data)
}

// 更新指定字段操作
export function accountplanlisteningtaskUpdateRiskTimes(data) {
  return requestV1.putForm(`${prefix}/accountplanlisteningtask/update/riskTimes`,data)
}

