<template slot-scope="scope">
  <div class="image-main">
    <el-button type="primary"  @click="playVideo()" ref="btn" size="mini">
      播 放
    </el-button>
    <el-dialog title :visible.sync="dialogPlay" width="50%" @close="closeDialog" top="5vh">
      <video :src="imgServer+fileUrl" controls autoplay class="video"
             width="100%"></video>
    </el-dialog>
  </div>
</template>

<script>
  import {imgServer} from '@/api/config'

  export default {
    name: "showVedioInList",
    props: {
      fileUrl: String,
      // srcList: Array
    },
    mounted() {
      this.imgServer = imgServer;
    },
    methods: {
      closeDialog() {
        this.dialogPlay = false;
      },
      playVideo() {
        this.dialogPlay = true;
      },


    },
    watch: {},
    data() {
      return {
        dialogPlay: false,
        imgServer: ''
      }
    }
  }
</script>

<style scoped>
  .image-main {
    width: 35px;
  }
</style>
