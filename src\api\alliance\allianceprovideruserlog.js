import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 服务商与人员业务记录
 */

// 批量发起同步数据-可同时用于人员档案里面的同步
export function sysData(data) {
  return requestV1.postForm(`${prefix}/allianceprovideruserlog/sys/data`, data)
}

// 根据多参数进行列表查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/allianceprovideruserlog/query/page`, data)
}

// 发起h5签约
export function sendH5Sign(data) {
  return requestV1.putForm(`${prefix}/allianceprovideruserlog/send/h5/sign`, data)
}

// 批量发起h5签约
export function batchsendH5Sign(data) {
  return requestV1.putJson(`${prefix}/allianceprovideruserlog/batchsend/h5/sign`, data)
}
