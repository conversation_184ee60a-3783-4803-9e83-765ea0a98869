<!-- 连接 -->
<template>
  <div>
    <el-form-item
      :label="config.label"
      :label-width="config.width"
      class="url-box agreement-box"
      style="width:26%; margin-right: 3px;"
    >
      <el-select v-model="agreement" placeholder="请选择协议" size="mini" style="width:88px;" @change="onChangeAgreement">
        <el-option
          v-for="(item, index) in agreementArr"
          :key="index+'url'"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      :rules="rules"
      :class="itemClass"
      :prop="config.name"
      class="url-box"
      style="width:60%;"
    >
      <el-input
        :disabled="disabled"
        v-model="form.data.input"
        :class="childClass"
        :placeholder="config.placeholder"
        type="text"
        style="width:calc(100% - 100px);max-width:300px;;"
      />
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'Url',
  props: {
    agreementArr: {
      type: Array,
      required: false,
      default: function() {
        return [
          'http://', 'https://'
        ]
      }
    },
    // el-form-item的class值
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    // 是否可编辑
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    // el-input的class值
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    // 组件循环时需要的idx值
    idx: {
      type: Number,
      default: 0
    },
    // 参数配置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '文本输入',
          name: 'text',
          placeholder: '请填写文本输入',
          rules: [
            { required: true, message: '请输入文本输入', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      agreement: 'http://', // 协议
      rules: [],
      form: {
        data: {
          input: ''
        }
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        this.form.data.input = val
      },
      deep: true
    },
    // 监听到form的数据变化则把config.name和form.data.input和idx传到父组件
    form: {
      handler(val) {
        // this.form.data.input = val
        // this.updateVal()
        this.$emit('updateForm', '' + this.config.name, this.form.data.input, this.idx)
        this.updateVal()
      },
      deep: true
    }
  },
  created() {
    this.rules = this.config.rules
  },
  methods: {
    onChangeAgreement(val) {
      this.agreement = val
      this.updateVal()
    },
    updateVal(val) {
      // debugger
      // if (val.indexOf(this.agreement) > -1) {
      const url = this.agreement + this.form.data.input
      this.$emit('updateForm', '' + this.config.key, url, this.idx)
      // }
    }
  }
}
</script>
<style lang="scss">
.url-box{
  .el-form-item__content{
    width:76%;
  }

}
.agreement-box{
  .el-form-item__content{
    width:40%;
  }
}

</style>

