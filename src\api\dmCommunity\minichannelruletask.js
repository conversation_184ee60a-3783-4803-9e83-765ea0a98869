/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * 小程序渠道链管理-马甲规则任务
 */

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/insert`, data)
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/query/page`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/minichannellinktask/update`, data)
}

// 根据主键单一查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/minichannellinktask/query/one`, data)
}

// 更新用户状态 
export function changestatus(data) {
  return requestV1.get(`${prefix}/minichannellinktask/changestatus`, data)
}


// 删除指定项 {id}
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/dm/api/v1/minichannellinktask/delete/one/${data.id}`)
}


// 任务子项列表查询
export function queryChildPage(data) {
  return requestV1.postJson(`${prefix}/minichannellinktaskitem/query/page`, data)
}

// 任务子项列表全部查询
export function queryChildList(data) {
  return requestV1.get(`${prefix}/minichannellinktaskitem/query/list`, data)
}

// 保存数据
export function insertChild(data) {
  return requestV1.postJson(`${prefix}/minichannellinktaskitem/insert`, data)
}

// 更新数据
export function updateChild(data) {
  return requestV1.putJson(`${prefix}/minichannellinktaskitem/update`, data)
}

// 根据主键单一查询
export function queryChildOne(data) {
  return requestV1.get(`${prefix}/minichannellinktaskitem/query/one`, data)
}


// 
// 更新子项用户状态 
export function changeChildStatus(data) {
  return requestV1.get(`${prefix}/minichannellinktaskitem/changestatus`, data)
}

// 批量更新子项状态 /v1/minichannellinktaskitem/changestatus/batch
export function changeChildStatusBatch(data) {
  return requestV1.postForm(`${prefix}/minichannellinktaskitem/changestatus/batch`, data)
}



// account/miniChannelLinkAccount
export function miniChannelLinkAccount(data) {
  return requestV1.get(`${prefix}/account/miniChannelLinkAccount`, data)
}



// 根据主键id指定删除
// export function deleteOne (data) {
//     return requestV1.deleteForm(`${prefix}/minichannellink/delete/one/${data.id}`)
// }

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/minichannellinktask/query/list`, data)
}


// 删除指定项 {id}
export function deleteChildOne(data) {
  return requestV1.deleteForm(`${prefix}/minichannellinktaskitem/delete/one/${data.id}`)
}

// 批量删除指定项目 {ids}
export function deleteChildBatch(data) {
  return requestV1.deleteForm(`${prefix}/minichannellinktaskitem/delete/batch/${data.ids}`)
}

// 修复重复ip /minichannellinktask/ipAmendment
export function switchipAmendment(data) {
  return requestV1.get(`${prefix}/minichannellinktask/ipAmendment`, data)
}

// 设置费用
export function minichannellinktaskupdate(data) {
  return requestV1.putJson(`${prefix}/minichannellinktask/update`, data)
}

// 批量新增任务子项
export function minichannellinktaskbatchitem(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/insert/batch/item`, data)
}

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/minichannellinktask/delete/batch/${data.ids}`)
}

// 批量删除多余流水
export function cleanBatch(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/clean/batch`, data)
}

// 批量导入
export const importBatch = env.ctx + '/dm/api/v1/minichannellinktask/tasks/import'

// 批量候补执行
export function batchAlternate(data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/batch/alternate`, data)
}

// 批量新增马甲管理任务 
export function minichannellinktaskBatchInsert (data) {
  return requestV1.postJson(`${prefix}/minichannellinktask/batch/insert`, data)
}