<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">
    <el-checkbox-group
      :class="childClass"
      v-model="form.data.checkbox">
      <el-checkbox
        v-for="(item,index) in array"
        :key="item.key"
        :checked="index === 0"
        :label="item.key"
        name="checkbox">{{ item.value }}</el-checkbox>
    </el-checkbox-group>
  </el-form-item>
</template>

<script>
// import dic from '@/service/api/dic'
import common from '@/common/utils'
export default {
  name: 'Checkbox',
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '复选框',
          name: 'checkbox',
          rules: [
            { required: true, message: '请选择复选框', trigger: 'blur' }
          ],
          array: [],
          dicKey: ''
        }
      }
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    data: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      form: {
        data: {
          checkbox: []
        }
      },
      array: []
    }
  },
  watch: {
    data: {
      handler(val) {
        if (this.$validate.isNull(val)) {
          this.form.data.checkbox = [this.array[0].key + '']
        } else {
          this.form.data.checkbox = val.length > 0 ? val.split(',') : []
        }
      },
      deep: true
    },
    config: {
      handler(val) {
        if (val.array.length > 0) {
          this.array = val.array
        }
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.checkbox.toString())
      },
      deep: true
    }
  },
  mounted() {
    this.getDic()
  },
  methods: {
    /**
     * 获取数据字典值
     */
    getDic() {
      const ar = this.config.array
      if (ar.length > 0) {
        this.array = ar
        return
      }
      if (!this.$validate.isNull(this.config.dicKey)) {
        this.$api.dic.getDicInfo({ 'dictType': this.config.dicKey }, (res) => {
          this.array = res
        })
      }
    }
  }
}
</script>

<style lang="scss">

</style>
