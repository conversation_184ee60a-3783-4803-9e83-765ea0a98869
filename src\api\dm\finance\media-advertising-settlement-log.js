/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

import env from '@/config/env'


/**
 * 财务管理-媒体广告投放结算申请记录
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/putplansettlelog/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/putplansettlelog/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/putplansettlelog/insert`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/putplansettlelog/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/putplansettlelog/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/putplansettlelog/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
  return requestV1.get(`${prefix}/putplansettlelog/query/param`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/putplansettlelog/update`, data)
}


// 审批驳回 
export function auditVeto(data) {
  return requestV1.get(`${prefix}/putplansettlelog/audit/veto/${data.id}`)
}

// 审批通过
export function auditPass(data) {
  return requestV1.get(`${prefix}/putplansettlelog/audit/pass/${data.id}`)
}

// excel导入数据
const uploadExcel = env.ctx + prefix + '/putplansettlelog/plan/import'
// const uploadRecordDetailExcel = baseURL + '/manage/api/adMediaRecord/uploadRecordDetailExcel'
export { uploadExcel }
// export { uploadRecordDetailExcel }

// 查询所有经销商
export function queryOperatorList(data){
  return requestV1.postJson(`/manage/api/operator/queryList`, data)
}

// 媒体广告结算记录总金额统计
export function totalStatistics(data){
  return requestV1.postJson(`${prefix}/putplansettlelog/total/statistics`, data)
}