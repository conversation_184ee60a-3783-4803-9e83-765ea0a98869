/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/auth/api/v1'

//根据角色id获取小程序菜单
export function appletroleid(data) {
    return requestV1.get(prefix + '/permission/get/applet/permission/by/roleid', data);
}

//根据角色id获取pc菜单
export function pcroleid(data) {
    return requestV1.get(prefix + '/permission/get/pc/permission/by/roleid', data);
}

//获取当前角色PC菜单
export function permission(data) {
    return requestV1.get(prefix + '/permission/get/pc/role/all/permission', data);
}

//获取当前角色资源列表
export function resourceList(data) {
    return requestV1.get(prefix + '/permission/get/role/resource/list', data);
}