/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/auth/api/v1'

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/roleinfo/query/one', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/roleinfo/update', data);
}

//新建角色保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/roleinfo/insert', data);
}

//根据多参数进行列表查询
export function queryList(data) {
    return requestV1.get(prefix + '/roleinfo/query/list', data);
}

//更新角色上的资源
export function permission(data) {
    return requestV1.putJson(prefix + '/roleinfo/update/role/permission', data);
}

//根据主键id指定删除
export function deleteone(data) {
    return requestV1.deleteForm(`${prefix}/roleinfo/delete/one/${data}`);
}

//克隆角色
export function cloneRole(data) {
  return requestV1.postForm(`${prefix}/roleinfo/clone/role`, data);
}