/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 马甲管理-banner曝光量马甲规则
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteJson(`${prefix}/attentionrulerelation/delete/batch/ids`, data)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/attentionrulerelation/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/attentionrulerelation/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/attentionrulerelation/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/attentionrulerelation/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/attentionrulerelation/update`, data)
}

// 修改启动状态
export function switchOpenstatus (data) {
    return requestV1.postForm(`${prefix}/attentionrulerelation/update/open/status`, data)
}
