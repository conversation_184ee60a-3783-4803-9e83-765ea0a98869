<template>
  <div>
    <uploaditem
      ref="uploadRef"
      :picarr="picarr"
      @remove="removeimg"
      :groupId="groupId"
      :onlyshow="true"
      :filesize="filesize"
      @showclick="showclick"
    />
    <uploadDialog
      :visible="uploadVisible"
      @remove="removeimg"
      :list="picarr"
      :appendtobody="true"
      :groupId="groupId"
      :maxcount="maxcount"

      @close="uploadVisible = false"
      @query="queryimg"
    ></uploadDialog>
  </div>
</template>

<script>
import uploadDialog from "@/components/uploadDialog/index.vue";
import uploaditem from "@/components/uploadDialog/uploaditem/index.vue";
export default {
  name: "uploadView",
  components: {
    uploadDialog,
    uploaditem
  },
  props: {
    picarr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    groupId: {
      type: Number,
      default: null,
    },
    filesize:{
      type:Number,
      default:10,
    },
    maxcount:{
      type:Number,
      default:100,
    }
  },
  data() {
    return {
      uploadVisible: false,
     
    };
  },
  methods: {
    showclick(){
        this.uploadVisible = true;
    },
    removeimg(idx) {
      this.$emit("remove", idx);
    },
    queryimg(arr){
        this.$emit('query',arr)
        this.uploadVisible = false
    }
  },
};
</script>

<style lang="scss" scoped>
</style>