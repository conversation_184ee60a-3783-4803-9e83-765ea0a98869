/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const basicsPrefix = '/basics/api/v1'
const dmPrefix = '/dm/api/v1'

// 新增lkl文件
export function merchantfileUploadFile (data) {
    return requestV1.postJson(`${basicsPrefix}/merchantfile/uploadFile`,data)
}
// 平台分账绑定申请
export function ledgerbindBindReceiver (data) {
    return requestV1.postJson(`${basicsPrefix}/ledgerbind/bindPlatformReceiver`,data)
}