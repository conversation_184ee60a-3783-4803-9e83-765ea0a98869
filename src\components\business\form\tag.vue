<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">
    <!--<el-input-->
    <!--v-model="form.data.input"-->
    <!--:class="config.childClass"-->
    <!--:placeholder="config.placeholder"-->
    <!--type="text"-->
    <!--style=""/>-->

    <el-tag
      v-for="tag in form.data.tag"
      :key="tag"
      :disable-transitions="false"
      closable
      @close="handleClose(tag)">
      {{ tag }}
    </el-tag>
    <el-input
      v-if="inputVisible"
      ref="saveTagInput"
      v-model="inputValue"
      class="input-new-tag"
      size="mini"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
    />
    <el-button v-else class="button-new-tag" size="mini" @click="showInput">+ 添加</el-button>

  </el-form-item>
</template>

<script>
import common from '@/common/utils'
export default {
  name: 'Tag',
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '标签',
          name: 'tag',
          rules: [
            { required: true, message: '请输入标签', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: ''
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          tag: []
        }
      },
      inputVisible: false,
      inputValue: ''
    }
  },
  watch: {
    data: {
      handler(val) {
        this.form.data.tag = val.length > 0 ? val.split(',') : []
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.tag.toString())
      },
      deep: true
    }
  },
  methods: {
    handleClose(tag) {
      this.form.data.tag.splice(this.form.data.tag.indexOf(tag), 1)
    },

    showInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm() {
      const inputValue = this.inputValue
      // debugger
      if (!this.$validate.isNull(inputValue) && !this.form.data.tag.includes(inputValue)) {
        this.form.data.tag.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>

<style lang="scss">
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
