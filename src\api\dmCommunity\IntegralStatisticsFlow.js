/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

const prefix = '/dm/api/v1'

/**
 * 积分统计流水
 */


// 积分统计流水 分页
export function pointrecordQueryPage (data) {
    return requestV1.postJson(`${prefix}/pointrecord/query/page`, data)
}
// 获取总积分
export function pointrecordTotal(data){
    return requestV1.postJson(`${prefix}/pointrecord/total`, data)
}
// 积分记录统计分页
export function pointrecordPage(data){
    return requestV1.postJson(`${prefix}/pointrecord/statistic/page`, data)
}
//导出积分流水
export async function pointrecordExport(data){
    return requestV1.download(`${env.ctx}${prefix}/pointrecord/export`, data, `积分统计流水.xlsx`, 'post',{
        'content-type': 'application/json; charset=utf-8'
    })
}

