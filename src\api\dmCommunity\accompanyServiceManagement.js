/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

const prefix = '/dm/api/v1'

/**
 * 陪诊平台--服务管理
 */

// 服务分页列表
export function serviceQueryPage (data) {
  return requestV1.postJson(`${prefix}/accompanyservice/query/page`,data)
}

// 服务保存数据
export function serviceInsert (data) {
  return requestV1.postJson(`${prefix}/accompanyservice/insert`,data)
}

// 服务主键单一查询
export function drugQueryOne (data) {
  return requestV1.get(`${prefix}/brand/query/one`,data)
}

// 服务更新数据
export function updateServiceData (data) {
  return requestV1.putJson(`${prefix}/accompanyservice/update`,data)
}

// 服务批量删除
export function BatchDeleteService (data) {
  return requestV1.deleteForm(`${prefix}/accompanyservice/delete/batch/${data.ids}`)
}

// 服务-单一删除
export function DeleteOneService (data) {
  return requestV1.deleteForm(`${prefix}/accompanyservice/delete/one/${data.id}`)
}

/**
 * 陪诊平台--套餐列表
 */

// 套餐分页列表
export function accompanycomboQueryPage (data) {
  return requestV1.postJson(`${prefix}/accompanycombo/query/page`,data)
}

// 套餐保存数据
export function accompanycomboInsert (data) {
  return requestV1.postJson(`${prefix}/accompanycombo/insert`,data)
}

// 套餐主键单一查询
export function accompanycomboQueryOne (data) {
  return requestV1.get(`${prefix}/accompanycombo/query/one`,data)
}

// 套餐更新数据
export function updateAccompanycomboData (data) {
  return requestV1.putJson(`${prefix}/accompanycombo/update`,data)
}

// 套餐批量删除
export function BatchDeleteAccompanycombo (data) {
  return requestV1.deleteForm(`${prefix}/accompanycombo/delete/batch/${data.ids}`)
}

// 套餐-单一删除
export function DeleteOneAccompanycombo (data) {
  return requestV1.deleteForm(`${prefix}/accompanycombo/delete/one/${data.id}`)
}

/**
 * 陪诊平台--套餐订单管理
 */

// 订单列表分页
export function accompanycombouserQueryPage (data) {
  return requestV1.postJson(`${prefix}/accompanycombouser/query/page`,data)
}

// 套餐使用记录分页
export function accompanycombologQueryPage (data) {
  return requestV1.postJson(`${prefix}/accompanycombolog/query/page`,data)
}
