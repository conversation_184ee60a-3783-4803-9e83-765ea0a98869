<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :width="width"
      center
      class="pub-dialog"
      top="1vh"
      :before-close="cancel"
      v-bind="$attrs"
      :append-to-body="appendtobody" 
      :modal-append-to-body="modalappendtobody"
      
    >
    <!-- :append-to-body="true" :modal-append-to-body="false" -->
      <div class="dialog" :style="{maxHeight:dialogHeight}">
        <slot name="content"/>
      </div>
      <div v-show="!noShowButton" class="footer" style="height: 50px;margin-top: 10px;">
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancel()" size="mini">取 消</el-button>
          <el-button type="primary"  @click="commit()" :loading='confirmLoading' size="mini" :disabled='disabled'>确 定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getTableHeight} from '@/utils/index'
export default {
  components: {},
  props: {
    confirmLoading:{
      type:Boolean,
      default:false,
    },
    dialogVisible: Boolean,
    title: String,
    noShowButton: Boolean,
    width: {
      type: String,
      default: '80%'
    },
    appendtobody:{
      type:Boolean,
      default:false,
    },
    modalappendtobody:{
      type:Boolean,
      default:false,
    },
    disabled:{
      type:Boolean,
      default:false,
    }

  },
  data() {
    return {
      scrollHeight: null,
      dialogHeight: 0
    }
  },
  watch: {
    dialogVisible(value) {
      if (value) {
        this.getHeight()
        console.log('mainDialog==>', this.dialogHeight)
      }
    }
  },
  created() {
  },
  mounted() {
    this.getHeight()
  },
  methods: {
    getHeight() {
      this.dialogHeight = getTableHeight(20) + 'px'
    },
    cancel() {
      this.$emit('cancel', false)
    },
    commit() {
      console.log('===> commitData')
      this.$emit('commitData')
    }
  }
}
</script>
<style lang="scss" scoped>
.footer {
  text-align: center;
}
.dialog {
  overflow-y: auto;
}
</style>
