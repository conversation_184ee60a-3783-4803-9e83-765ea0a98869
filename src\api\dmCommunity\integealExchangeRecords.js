/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 兑换审核
 */

// 礼物兑换 分页
export function pointgiftexchangeQueryPage (data) {
    return requestV1.postJson(`${prefix}/pointgiftexchange/query/auditPage`, data)
}

// 更新物流信息 分页
export function pointgiftexchangeUpdateDelivery (data) {
    return requestV1.postJson(`${prefix}/pointgiftexchange/updateDelivery`, data)
}

// 积分用户地址单一查询
export function pointaddressQueryOne (data) {
    return requestV1.get(`${prefix}/pointaddress/query/one`, data)
}

