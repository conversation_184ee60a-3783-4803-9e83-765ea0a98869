/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 横幅管理
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/useworkconfig/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/useworkconfig/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/useworkconfig/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/useworkconfig/query/one`, data)
}

// 分页列表查询代办模板
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/useworkconfig/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/useworkconfig/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/useworkconfig/update`, data)
}

// 修改状态
export function updateOpenStatus (data) {
    return requestV1.postForm(`${prefix}/useworkconfig/update/open/status`, data)
}

// 平台端分页
export function queryPlatformPage(data) {
  return requestV1.postJson(`${prefix}/useworkconfig/query/platform/page`, data);
}

// 根据当前用工分组查询已有的关联数据  
export function useworkconfigQueryGroupList(data = {}) {
  return requestV1.get(`${prefix}/useworkconfig/query/group/list`, data);
}

// 批量关联 
export function commonbusinessservicereBatchAssociation(data = {}) {
  return requestV1.postJson(`${prefix}/commonbusinessservicere/batch/association`, data);
}

// 解除关联 
export function commonbusinessservicereDeleteOne(data = {}) {
  return requestV1.deleteForm(`${prefix}/commonbusinessservicere/delete/one/${data.ids}`);
}