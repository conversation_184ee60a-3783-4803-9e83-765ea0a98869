/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

import env from '@/config/env'


/**
 * 马甲管理-随机评论列表
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/sockpuppetcommenttemplateitem/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/sockpuppetcommenttemplateitem/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/sockpuppetcommenttemplateitem/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/sockpuppetcommenttemplateitem/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/sockpuppetcommenttemplateitem/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/sockpuppetcommenttemplateitem/update`, data)
}

// 修改启动状态
export function switchOpenstatus (data) {
    return requestV1.get(`${prefix}/sockpuppetcommenttemplateitem/switch/openstatus`, data)
}

// excel导入数据
const uploadExcel = env.ctx + prefix + '/sockpuppetcommenttemplateitem/comment/import'
// const uploadRecordDetailExcel = baseURL + '/manage/api/adMediaRecord/uploadRecordDetailExcel'
export { uploadExcel }
