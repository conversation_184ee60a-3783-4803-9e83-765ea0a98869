/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

// API前缀
const basicsPrefix = '/basics/api/v1'
const dmPrefix = '/dm/api/v1'

// 新增lkl文件
export function merchantfileUploadFile (data) {
    return requestV1.postJson(`${basicsPrefix}/merchantfile/uploadFile`, data)
}

// 创建分账接收方
export function applyLedgerReceiver (data) {
    return requestV1.postJson(`${basicsPrefix}/ledgerreceiver/applyLedgerReceiver`, data)
}

// 分账接收方列表
export function queryLedgerReceiverPage (data) {
    return requestV1.postJson(`${basicsPrefix}/ledgerreceiver/query/page`, data)
}

// 平台分账绑定申请
export function ledgerbindBindReceiver (data) {
    return requestV1.postJson(`${basicsPrefix}/ledgerbind/bindPlatformReceiver`, data)
}

// 对私查询cardbin信息
export function getCardBinInfo (params) {
    return requestV1.get(`${basicsPrefix}/merchantfile/cardBinInfo`, { params })
}

// 平台绑定接收方
export function platformBind (data) {
    return requestV1.postJson(`${basicsPrefix}/lklaccompany/platform/bind`, data)
}

// 商户结算信息变更
export function changeSettle (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/changeSettle`, data)
}

// 电子合同申请
export function ecApply (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/ec/apply`, data)
}

// 电子合同查询
export function ecQuery (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/ec/query`, data)
}

// 电子合同下载
export function ecDownload (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/ec/download`, data)
}

// 拉卡拉进件表分页查询
export function queryContractPage (data) {
    return requestV1.postJson(`${basicsPrefix}/contract/query/page`, data)
}

// 人工复核申请
export function ecManual (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/ec/manual`, data)
}

// 人工复核申请查询
export function ecManualQry (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/ec/manualQry`, data)
}

// 电子合同分页查询
export function queryEcContractPage (data) {
    return requestV1.postJson(`${basicsPrefix}/eccontract/query/page`, data)
}

// 新增商户进件
export function addMer (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/addMer`, data)
}

// 增网增终进件
export function addTerm (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/addTerm`, data)
}

// 进件信息查询
export function contractQry (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/contractQry`, data)
}

// 附件补充上传
export function replenishFile (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/replenishFile`, data)
}

// 进件校验
export function verifyContractInfo (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/verifyContractInfo`, data)
}

// 进件复议提交
export function reconsiderSubmit (data) {
    return requestV1.postJson(`${basicsPrefix}/lkl/open/reconsiderSubmit`, data)
} 