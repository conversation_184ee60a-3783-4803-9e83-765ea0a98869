<template>
  <div class="component-wrapper">
      <el-input
        placeholder="请输入关键字"
        prefix-icon="el-icon-search"
        @keydown.enter.native="search"
        @input="search"
        v-model="value"
        size="mini">
    </el-input>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
export default {
    name: 'SearchBar',
    data () {
        return {
            value: '',
            newRouter: []
        }
    },
    computed: {
        ...mapGetters(['permission_routes'])
    },
    watch: {
        newRouter: {
            handler() {
                this.$emit('change', this.newRouter)
            },
            deep: true
        },
        permission_routes: {
            handler () {
                this.search()
            },
            deep: true
        }
    },
    methods: {
        search () {
            let vm = this

            function filterRouter (routers) {
                return routers.filter(item => {
                    if(item.hidden) return false
                    if (item.children && item.children.length) {
                        let children = filterRouter(item.children)
                        if(!children.length) {
                            return false
                        }else{
                            item.children = children
                            return true
                        }
                    } else {
                        // 关键字过滤
                        if(item.meta.title.indexOf(vm.value) !== -1 || item.path.indexOf(vm.value) !== -1){
                            return true
                        } else {
                            return false
                        }
                    }
                })
            }

            let newRouters = JSON.parse(JSON.stringify(this.permission_routes))
            const newRouter = filterRouter(newRouters)
            this.newRouter = newRouter
        }
    }
}
</script>

<style scoped>
.component-wrapper {
    width: 95%;
    height: 42px;
}

/deep/ .el-input__inner {
    background: #FFFFFF;
    color: #1D2029;
}
</style>
