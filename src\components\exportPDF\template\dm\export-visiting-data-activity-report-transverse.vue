<template>
  <div class="pageContent" v-loading="pageLoading">
    <template v-for="(page, index) in pageContent">
      <div
        class="everyPage"
        :key="index"
        :style="{
          width: pageSize.width + 'px',
          height: pageSize.height + 'px',
        }"
        v-if="!page.authHeight"
      >
        <div class="everyPageContent">
          <!-- 封面 -->
        </div>
      </div>
      <div
        :key="index"
        v-else-if="page.authHeight"
        :style="{
          width: pageSize.width + 'px',
          height: page.targetHeight ? page.targetHeight + 'px' : 'auto',
        }"
        :id="authHeightResult[index]"
      >
        <div class="everyPageContent">
          <!-- 结算报告 -->
          <div
            class="settlement-page"
            v-if="page.type === 'surveyOfQuestionnaireActivities'"
          >
            <div class="templateBox">
              <div class="table-header-title">
                {{ filename }}数据报告（时间{{
                  page.pageContent.planTime
                }}）
              </div>
              <el-table
                :data="page.pageContent.pharmacyTableData"
                header-row-class-name="header-row-class-name"
                border
                style="width: 100%"
              >
                <el-table-column type="index" width="50px" label="序号">
                </el-table-column>
                <template v-for="item in page.pageContent.pharmacyHearders">
                  <el-table-column
                    :key="item.key"
                    :prop="item.key"
                    :label="item.title"
                    :width="item.width"
                  >
                    <template slot-scope="scope">
                      <template v-if="item.key === 'siteImagePathsText'">
                        <template
                          v-for="(url, urlIdx) in scope.row.siteImagePathsText"
                        >
                          <img
                            :key="urlIdx"
                            :src="url"
                            class="siteImagePathsIco"
                            alt=""
                          />
                        </template>
                        <span style="color: #fff; font-size: 0">1</span>
                      </template>
                      <div
                        class="exporttxt"
                        v-else
                        :style="scope.row[item.key + 'Style'] || ''"
                      >
                        {{ scope.row[item.key] }}
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import {
  getVisitingplan as queryOne,
  insert,
  update,
} from "@/api/dm/visiting/visitingplan";

import { queryList } from "@/api/dm/visiting/visitingplanobjectlist.js";

import { getIOSTime,format, domainURL  } from "@/utils/index";

import { getdrugstoreFeedbackList } from "@/utils/enumeration.js";



export default {
  props: {
    taskId: {
      type: [Number, String],
      default: null,
    },
    preview: {
      type: Boolean,
      default: true,
    },
    // 拼接域名 lvbao不支持本地调式图片
    domainUrl: {
      type: String,
      default: "https://lvbao-saas.oss-cn-shenzhen.aliyuncs.com/",
    },
    domainResult: {
      type: Array,
      default: function () {
        return ["https://file.greenboniot.cn/"];
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      filename:"",
      // 横向就变成 宽 1123 高 794
      pageSize: {
        width: 1123,
        height: 794,
      },
      // 导出数据的标题
      pageContent: [
        // 问卷活动调研
        {
          text: "问卷活动调研",
          type: "surveyOfQuestionnaireActivities",
          authHeight: true,
          pageContent: {
            // 提交详情
            // 默认
            defaultTableData: [],

            // 人物
            characterTableData: [],
            // 药店
            pharmacyHearders: [
              {
                key: "drugstoreName",
                title: "药店名",
                width: 80,
              },
              {
                key: "drugstoreUserName",
                title: "店员",
                width: 80,
              },
              {
                key: "drugstoreDuration",
                title: "拜访时长(分钟)",
                width: 130,
              },
              {
                key: "drugstoreTransmit",
                title: "传递关键信息",
                width: 120,
              },
              {
                key: "drugstoreFeedbackText",
                title: "客户反馈",
                width: 80,
              },
              {
                key: "terminalDesc",
                title: "终端位置",
                width: 180,
              },
              {
                key: "writeTimeText",
                title: "拜访日期",
                width: 120,
              },
              // {
              //   key: "siteImagePathsText",
              //   title: "现场图片",
              //   width: 120,
              // },
              {
                key: "userInfoName",
                title: "提交人",
                width: 120,
              },
              // {
              //   key: "createTimeText",
              //   title: "提交时间",
              //   width: 120,
              // },
              {
                key: "userInfoPhone",
                title: "手机号码",
                // width: 120,
              },
              // {
              //   key: "oneLevelAuditStatus",
              //   title: "一级审核状态",
              //   width: 100,
              // },
              // {
              //   key: "oneLevelAuditTimeText",
              //   title: "审核时间",
              //   width: 80,
              // },
              // {
              //   key: "oneLevelAuditUserName",
              //   title: "一级审核人",
              //   width: 100,
              // },
              // { key: "oneLevelAuditDesc", title: "备注", width: 80 },

              // {
              //   key: "twoLevelAuditStatus",
              //   title: "二级审核状态",
              //   width: 100,
              // },
              // {
              //   key: "twoLevelAuditTimeText",
              //   title: "审核时间",
              //   width: 80,
              // },
              // {
              //   key: "twoLevelAuditUserName",
              //   title: "二级审核人",
              //   width: 100,
              // },
              // { key: "twoLevelAuditDesc", title: "备注", width: 80 },

              // {
              //   key: "threeLevelAuditStatus",
              //   title: "三级审核状态",
              //   // width: "100px",
              //   width:100,
              // },
              // {
              //   key: "threeLevelAuditTimeText",
              //   title: "审核时间",
              //   width: 80,
              // },
              // {
              //   key: "threeLevelAuditUserName",
              //   title: "三级审核人",
              //   width: 100,
              // },
            ],
            pharmacyTableData: [],
          },
        },
      ],
      pageLoading: false,
      authHeightResult: [],
      initsuccesscount: 0,
      targetCount: 2,
      // 导出类型
      exportVisitType: 3,
    };
  },
  mounted() {
    let number = 0;
    let idx = this.pageContent.findIndex(
      (item) => item.type === "surveyOfQuestionnaireActivities"
    );
    let hearders = this.pageContent[idx].pageContent.pharmacyHearders;
    for (let i = 0; i < hearders.length; i++) {
      number += hearders[i].width;
    }
    console.log("number", number);

    this.hearderWidth = number + 50 + 1;

    if (this.pageSize.width < this.hearderWidth) {
      this.$emit("updateWidth", this.hearderWidth);
    }
  },
  watch: {
    updatecount(n) {
      this.pageLoading = true;
      this.initsuccesscount = 0;

      this.initpageData();
    },
  },
  methods: {
    addComputeTag() {
      // class=“pdf_finish”
      const dom = document.createElement("div");
      dom.classList = "pdf_finish";
      document.body.append(dom);
    },
    getBase64Sync(imgUrl) {
      if (imgUrl.startsWith("https://") || imgUrl.startsWith("http://")) {
      } else {
        imgUrl = this.domainUrl + imgUrl;
      }
      for (let i = 0; i < this.domainResult.length; i++) {
        if (imgUrl.indexOf(this.domainResult[i]) != -1) {
          let tempArr = imgUrl.split(this.domainResult[i])[1];
          // console.log("tempArr", tempArr);
          imgUrl = this.domainUrl + tempArr;
          break;
        }
      }
      // this.getBase64Sync(
      //   "https://cdn2.weimob.com/saas/@assets/saas-fe-website-web-stc/_next/static/media/floor2.d79c3038.png"
      // ).then((url) => {
      //   this.url = url;
      // });
      return new Promise((resolve, reject) => {
        var xhr = new XMLHttpRequest();
        xhr.open("get", imgUrl, true);
        // 至关重要
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (this.status == 200) {
            //得到一个blob对象
            var blob = this.response;
            console.log("blob", blob);
            // 至关重要
            let oFileReader = new FileReader();
            oFileReader.onloadend = function (e) {
              // 此处拿到的已经是 base64的图片了
              let base64 = e.target.result;
              let image = new Image();
              image.src = base64;
              let canvas = document.createElement("canvas");
              canvas.width = image.width;
              canvas.height = image.height;
              let context = canvas.getContext("2d");
              context.drawImage(image, 0, 0, image.width, image.height);
              //返回
              resolve(base64);
            };
            oFileReader.readAsDataURL(blob);
          } else {
            console.log("imgUrl123", imgUrl);
          }
        };
        xhr.send();
      });
    },
    // 获取拜访详情
    async queryOne() {
      const res = await queryOne({ id: this.taskId, isCurrentUser: 2 });

      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (idx !== -1) {
        console.log("kkk");
        this.pageContent[idx].pageContent.articleContent = res.data.content;
        this.exportVisitType = res.data.type - 0;

        //  item.value = [res.data.startTime, res.data.endTime];
        // let startTime = format(new Date(res.data.startTime),'YYYY-MM-DD');
        // let endTime = format(new Date(res.data.endTime),'YYYY-MM-DD');

        this.pageContent[idx].pageContent.planTime =
          res.data.startTimeStr + " 至 " + res.data.endTimeStr;
        this.filename = res.data.title

        this.$forceUpdate();
      }

      this.$nextTick(() => {
        setTimeout(() => {
          this.updateSuccess();
        }, 2000);
      });

      // this.updateSuccess();

      console.log("res", res);
    },

    // 获取拜访列表
    async getVisitCordList(type) {
      const res = await queryList({
        type: type,
        planId: this.taskId,
      });


      const data = res.data;
      
      let idx = this.pageContent.findIndex(
        (item) => item.type === "surveyOfQuestionnaireActivities"
      );

      if (type === 1) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.defaultTableData = data;
        }
      } else if (type === 2) {
        if (idx !== -1) {
          this.pageContent[idx].pageContent.characterTableData = data;
        }
      } else if (type === 3) {
        if (idx !== -1) {
          const drugstoreFeedbackArr = getdrugstoreFeedbackList();
          for (let i = 0; i < data.length; i++) {
            let item = data[i];
            // if(item.writeTime && item.writeTime!=''){
            //   item.writeTimeText = format(item.writeTime,'YYYY-MM-DD');
            // }else{
            //   item.writeTimeText = ''
            // }
            item.writeTimeText = item.writeTimeStr;
            item.drugstoreFeedbackText = this.getEnumText(
              item.drugstoreFeedback,
              drugstoreFeedbackArr
            );
            let siteImagePathsText =
              item.siteImagePaths && item.siteImagePaths !== ""
                ? item.siteImagePaths.split(",")
                : [];
            // siteImagePathsText = ['https://file.greenboniot.cn/1/dm-service/926814004694011906.jpg','1/dm-service/926814004694011906.jpg']
            for (let j = 0; j < siteImagePathsText.length; j++) {
              siteImagePathsText[j] = domainURL(siteImagePathsText[j])
            //   siteImagePathsText[j] = await this.getBase64Sync(
            //     siteImagePathsText[j]
            //   );
            }
            item.siteImagePathsText = siteImagePathsText;
            // item.createTimeText = getIOSTime(item.createTime)
            data[i] = item;
          }
          console.log("data", data);

          this.pageContent[idx].pageContent.pharmacyTableData = data;
        }
      }

      setTimeout(() => {
        this.$nextTick(() => {
          this.updateSuccess();
        });
      },1000);

      // return data;
    },
    updateSuccess() {
      this.initsuccesscount += 1;

      console.log(
        "this.initsuccesscount",
        this.initsuccesscount,
        this.targetCount
      );

      if (this.initsuccesscount === this.targetCount) {
        this.pageLoading = false;
        this.$nextTick(() => {
          setTimeout(() => {
            this.addComputeTag()
            this.$emit("compute", {});
          }, 2000);
        });
      }
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    initpageData() {
      // 获取详情
      this.queryOne().then((res) => {
        if (this.exportVisitType === 1) {
          // 获取拜访记录- 默认
          this.getVisitCordList(1);
        } else if (this.exportVisitType === 2) {
          // 获取拜访记录- 人物
          this.getVisitCordList(2);
        } else if (this.exportVisitType === 3) {
          // 获取拜访记录- 药店
          this.getVisitCordList(3);
        }
      });
    },

    initpage() {
      this.authHeightResult = {};
      for (let i = 0; i < this.pageContent.length; i++) {
        if (this.pageContent[i].authHeight) {
          let id = "authHeight" + i;
          this.authHeightResult[i] = id;
        }
      }

      this.$nextTick(() => {
        this.save();
      });
    },
    // 页面的倍数
    initHeight(height) {
      let pagecount = 1;
      while (height > this.pageSize.height) {
        pagecount += 1;
        height -= this.pageSize.height;
      }

      return pagecount * this.pageSize.height;
    },
    // 保存前初始化页面
    save() {
      for (let key in this.authHeightResult) {
        let id = this.authHeightResult[key];

        let dom = document.getElementById(id);
        console.log(dom.clientHeight, this.pageContent[key]);
        this.pageContent[key].targetHeight = this.initHeight(dom.clientHeight);
      }
      this.$forceUpdate();
    },
  },
};
</script>




<style lang="scss" scoped>
.siteImagePathsIco {
  width: 60px;
  height: 60px;
}
.templateBox {
  width: 100%;

  .table-header-title {
    height: 40px;
    display: flex;
    align-items: center;
    font-weight: 550;
    justify-content: center;
    line-height: 1.5;
    font-size: 14px;
    background: #dfe6ec;
    border-bottom: 1px solid #dbdbdb;
  }
}
::v-deep .header-row-class-name th {
  background: #dfe6ec;
}

.mgt30 {
  margin-top: 30px;
}
.pageContent {
  // width: 100%;
  margin: 0 auto;
}
.everyPage {
  overflow: hidden;
  padding: 0 0px 5px;
  box-sizing: border-box;
  background: #fff;
}
.everyPageContent {
  width: 100%;
  height: 100%;
  background: #fff;
}
// 封面
.cover-page {
  padding-top: 100px;
  padding-left: 50px;
  height: 100%;
  overflow: hidden;
  background: #fff;
  box-sizing: border-box;

  .cover-page-bottom-txt {
    // display:flex;
    margin-top: 50px;
    line-height: 2;
    font-size: 18px;
    text-align: center;
  }
  .cover-text-box-info {
    font-size: 16px;
    color: #7c7c7c;
  }

  .cover-text-box-bottom {
    position: absolute;
    bottom: 100px;
    left: 50px;
    color: #fff;
  }

  .cover-text-box-title {
    font-size: 40px;
    color: #fff;
  }

  .cover-page-icon {
    margin-bottom: 30px;
  }

  .cover-page-img-box {
    position: relative;
  }

  .cover-text-box {
    position: absolute;
    top: 100px;
    left: 30px;
  }

  .cover-page-img {
    width: 100%;
  }

  .cover-text-box-info {
    color: #fff;
    font-size: 24px;
  }
}
// 目录
.directory-page {
  padding: 20px;
  .directory-title {
    font-size: 28px;
    line-height: 2;
    font-weight: 550;
    margin-bottom: 30px;
  }
}

// 问卷活动调研
.serve-page {
  padding: 20px;

  .serve-page-title {
    height: 50px;
    // background: #3a78f1;
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 550;
    // color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0e0800;
  }

  .serve-page-execute-time {
    text-align: center;
    font-weight: 550;
    margin-bottom: 10px;
  }

  .serve-page-execute-sub1 {
    font-weight: 550;
    margin-bottom: 20px;
  }

  .serve-page-execute-center-t1 {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
    font-weight: 550;
  }

  .serve-page-execute-introduction {
    text-indent: 2em;
    color: #7c7c7c;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 40px;
  }

  .serve-cols {
    display: flex;
    align-items: center;
    height: 40px;
    // border-bottom: 1px solid #000;
    border-top: none;
  }
  .serve-col {
  }
  .serve-cols-title {
    padding: 20px;
    background: #ffefef;
    // border: 2px solid #de4040;
    line-height: 1.5;
    color: #de4040;
  }
  .border-bottom {
    border-bottom: 1px solid #000;
  }
  .serve-col-span {
    width: 130px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .serve-col-label {
    padding-left: 30px;
  }
  .serve-cols-info {
    line-height: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #edf3ff;
    color: blue;
  }
}

.person-serve-detail {
  padding: 20px;

  .person-serve-detail-title {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 550;
    font-size: 18px;
  }
  .person-serve-detail-until {
    display: flex;
    justify-content: flex-end;
  }
}

.one {
  background: yellow;
}
.two {
  background: blue;
}
</style>
