/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'
const prefix2 = '/dm/api/v2'

/**
 * 定制推广任务
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/seotask/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/seotask/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/seotask/insert`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/seotask/query/one`, data)
}

// 多参数列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/seotask/query/list`, data);
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/seotask/query/page`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/seotask/update`, data)
}
// 切换状态
export function updateExecuteStatus(data) {
  return requestV1.postForm(`${prefix}/seotask/update/execute/status`, data)
}

// 获取集合根据事物类型
export function getListAffairtype(data) {
  return requestV1.get(`${prefix}/seotask/get/list/affairtype`, data)
}


// 获取提现每个任务的汇总 
export function seotaskexecutorplangetseotask(data) {
  return requestV1.get(`${prefix2}/seotaskexecutorplan/get/seotask/applet/detail`, data)
}


// 获取任务个人服务事项明细
export function seotaskpersonaffairtype(data) {
  return requestV1.get(`${prefix}/seotask/get/person/affairtype/detail`, data)
}


// 结算明细报告导出 v1/
export function seotaskexecutorplangetlist(data) {
  return requestV1.get(`${prefix}/seotaskexecutorplan/get/seotask/applet/detail/list`, data)
}
export function seotaskexecutorplangetlistv2(data) {
  return requestV1.get(`${prefix2}/seotaskexecutorplan/get/seotask/applet/detail`, data)
}





