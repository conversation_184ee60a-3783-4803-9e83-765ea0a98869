<template>
  <div>
    <el-upload
      ref="uploadCom"
      :action="doUpload"
      list-type="picture-card"
      :before-upload="beforeAvatarUpload"
      :on-preview="handlePictureCardPreview"
      :before-remove="handleRemove"
      :on-success="handleSuccess"
      :on-error="imageError"
      :on-exceed="handleExceed"
      :headers="uploadHeaders"
      :multiple="multiple"
      :disabled="disabled"
      name="files"
      :limit="limit"
      :data="{ groupId, relId }"
      :file-list="imgArr"
      accept=".jpg,.jpeg,.png,.webp,.JPG,.JPEG"
    >
      <i class="el-icon-plus" />
      <div class="el-upload__tip" slot="tip" v-if="tip">{{ tip }}</div>
    </el-upload>
    <el-image-viewer
      v-if="dialogVisible"
      :on-close="() => {dialogVisible = false}"
      :url-list="imgArr.map(item => item.url)"
      :initialIndex="imgIndex"
    ></el-image-viewer>
  </div>
</template>

<script>
import { doUpload, deleteByPath } from '@/api/index'
import { getToken } from '@/utils/auth'
import { imgServer } from '@/api/config'
import ElImageViewer from "element-ui/packages/image/src/image-viewer";

export default {
  components: {
    ElImageViewer
  },
  props: {
    groupId: {
      type: Number,
      default: 1000
    },
    relId: {
      type: String,
      default: function () {
        return ''
      }
    },
    img: {
      type: [String, Array],
      default: null
    },
    memberData: {
      type: Object,
      default: null
    },
    // 1 营业执照 2组织机构代码 3税务登记证 4银行开户证明 5机构信用代码 6icp备案许可 7行业许可证 8身份证正面 9身份证反面
    type: {
      type: Number,
      default: 1
    },
    // 是否提示删除成功
    isNotMsg: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 1
    },
    multiple: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: null
    },
    size: {
      type: Number,
      default: 100
    },
    tip: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      doUpload,
      uploadHeaders: {
        Authorization: getToken()
      },
      picture: null,
      dialogVisible: false,
      imgServer,
      imgArr: [],
      memberId: null,
      pictureList: [],
      imgIndex: 0
    }
  },
  watch: {
    formData(n, o) {

    },
    img(n) {
      if (n) {
        this.picture = n
        this.initFileList()
      } else {
        this.imgArr = []
      }
    },
    memberData(n) {
      if (n) {
        this.memberId = n.id
      }
    }
  },
  mounted() {
    if (this.formData) {
      this.picture = this.formData.value
      this.initFileList()
    }
    if (this.memberData) {
      this.memberId = this.memberData.id
    }
  },
  methods: {
    beforeAvatarUpload(file) {
      let testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension =
        testmsg === 'jpg' ||
        testmsg === 'JPG' ||
        testmsg === 'png' ||
        testmsg === 'PNG' ||
        testmsg === 'jpeg' ||
        testmsg === 'JPEG' ||
        testmsg === 'gif' ||
        testmsg === 'webp' ||
        testmsg === 'GIF'
      const isLt50M = file.size / 1024 / 1024 < this.size
      if (!extension) {
        this.$message({
          message: '上传图片只能是jpg / png / jpeg / webp格式!',
          type: 'error'
        })
        return false // 必须加上return false; 才能阻止
      }
      if (!isLt50M) {
        this.$message({
          message: `上传文件大小不能超过 ${this.size}MB!`,
          type: 'error'
        })
        return false
      }
      return extension || isLt50M
    },


    initFileList() {
      if (this.img) {
        if (this.multiple || Array.isArray(this.img)) {
          let arr = []
          let list = []
          this.img.map(i => {
            if (i.indexOf('http') === -1) {
              arr.push({ url: this.$env.file_ctx + i })
              list.push(i)
            } else {
              const reg = new RegExp(this.$env.file_ctx)
              list.push(i.replace(reg, ''))
              arr.push({ url: i })
            }
          })
          this.picture = arr
          this.imgArr = arr
          this.pictureList = list
        } else {
          if (this.img.indexOf('http') === -1) {
            this.picture = this.$env.file_ctx + this.img
            this.imgArr = [{ url: this.$env.file_ctx + this.img }]
            this.pictureList = [this.img]
          } else {
            this.picture = this.img
            this.imgArr = [{ url: this.img }]
            const reg = new RegExp(this.$env.file_ctx)
            this.pictureList = [this.img.replace(reg, '')]
          }
        }
      }
    },
    handlePictureCardPreview(val) {
      this.picture = val.url
      this.imgIndex = this.imgArr.findIndex(item => item.url === val.url)
      this.dialogVisible = true
    },
    async handleRemove(file) {
      if (file.status === 'success') {
        return this.$confirm(`是否删除该文件?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(res => {
          let path = null
          if (this.multiple) {
            try {
              path = file.response.data[0].fileUrl
            } catch {
              path = file.url
            }
          } else {
            path = this.img
          }
          this.picture = null
          if (this.multiple) {
            const list = JSON.parse(JSON.stringify(this.pictureList))
            console.log('list-------------------', this.pictureList, list, path)
            let index = 0
            for (let i = 0; i < list.length; i++) {
              let item = list[i]
              if(item.indexOf(this.$env.file_ctx) === -1) {
                item = this.$env.file_ctx + item
              }
              if(path.indexOf(this.$env.file_ctx) === -1) {
                path = this.$env.file_ctx + path
              }
              if (path === item) {
                this.pictureList.splice(index, 1)
              } else {
                index ++
              }
            }
            if (this.formData) {
              this.$emit('getUrlObj', { url: this.pictureList, formData: this.formData })
            } else {
              this.$emit('getUrl', this.pictureList)
            }
          } else {
            if (this.formData) {
              this.$emit('getUrlObj', { url: this.picture, formData: this.formData })
            } else {
              this.$emit('getUrl', this.picture)
            }
          }
          // deleteByPath({ path }).then(res => {
          //   if (!this.isNotMsg) {
          //     this.$message({
          //       message: res.msg,
          //       type: 'success'
          //     })
          //   }
          //   this.picture = null
          //   if (this.multiple) {
          //     for (let i = 0; i < this.pictureList.length; i++) {
          //       const item = this.pictureList[i]
          //       if (path === item) {
          //         this.pictureList.splice(i, 1)
          //       }
          //     }
          //     if (this.formData) {
          //       this.$emit('getUrlObj', { url: this.pictureList, formData: this.formData })
          //     } else {
          //       this.$emit('getUrl', this.pictureList)
          //     }
          //   } else {
          //     if (this.formData) {
          //       this.$emit('getUrlObj', { url: this.picture, formData: this.formData })
          //     } else {
          //       this.$emit('getUrl', this.picture)
          //     }
          //   }
          // })
        })
      }
    },
    handleSuccess(response, file, fileList) {
      if(response.code !== 0) {
        this.$eltool.errorMsg(response.msg)
        this.$refs.uploadCom.uploadFiles.splice(this.$refs.uploadCom.uploadFiles.indexOf(response), 1)
        return
      }

      if (this.multiple) {
        this.pictureList.push(response.data[0].dir)
        if (this.pictureList.length === fileList.length) {
          this.$emit('getUrl', this.pictureList)
          if (this.formData) {
            this.$emit('getUrlObj', { url: this.pictureList, formData: this.formData,file })
          }
        }
      } else {
        this.picture = response.data[0].dir
        this.$emit('getUrl', this.picture)
        const memberId = this.memberId
        const type = this.type
        const picture = this.picture
        this.$emit('memberData', { picture, memberId, type })
        if (this.formData) {
          this.$emit('getUrlObj', { url: this.picture, formData: this.formData,file })
        }
      }
    },
    handleBeforeUpload() {
    },
    /**
     * 上传失败
     * @param err
     * @param file
     * @param fileList
     */
    imageError(err, file, fileList) {
      this.$eltool.errorMsg('上传失败，请重新操作！')
      return false
    },

    /**
     * 处理超出上传限制
     */
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传${this.limit}张图片`);
    }

  }

}
</script>

<style lang="scss" scoped>
</style>

