<template>
  <el-drawer
    :title="'积分规则' + paramsData ? '编辑' : '新增'"
    :visible.sync="dialogVisible"
    size="1000px"
    :before-close="handleClose"
    center
  >
    <searchList
      :from-data="formList.filter(e => !e.hidden)"
      :config="{ size: 24, labelWidth: '150px' }"
      @selectVal="selectVal"
      @getImgUrlObj="getImgUrl"
    >
    <div class="agreementButton" slot='agreementButton' slot-scope="{ row }">
      <div>
        <el-switch
          v-model="row.value"
          :active-value="1"
          :inactive-value="0"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </div>
      <div v-if="row.value" class="agreement-boxFather">
        <div class="agreement-box" v-for="(item, index) in agreementList2D">
          {{(()=>{item[2].indexKey = index})()}}
          <div class="agreement-title">
            <div>序号{{index + 1}}</div>
            <el-button size="mini" type="danger" @click="deleteAgreement(index)">删除</el-button>
          </div>
          <searchList
            @switchChange="({ val, item })=>agreementSwitchChange({ val, item },index)"
            :from-data="item"
            :config="{ size: 24, labelWidth: '150px' }"
            @getImgUrlObj="getImgUrl"
            @getFileDetail="getFileDetail"
          >
          </searchList>
        </div>
        <el-button size="mini" icon="el-icon-search" type="primary" @click="agreementList2D.push(createNewAgreement())">新增协议</el-button>
      </div>
    </div>
    </searchList>
    <el-button
          size="mini"
          icon="el-icon-search"
          type="primary"
          @click="confirm()"
    >确定</el-button>
  </el-drawer>
</template>

<script>
import imageView from '@/components/imageview/index.vue';
import uploadFile from '@/components/uploadFile.vue';
import { Message } from 'element-ui'
import { getFromData } from "@/utils/index";
import formMixin from "@/mixin/formMixin";
import {getAccompanyproviderInsert,getAccompanyproviderUpdate,minichannellinkQueryOne,agreementAddOrUpdate,agreementAddOrUpdateDeleteOne} from "@/api/dmCommunity/accompany-doctor.js";
import { insert } from "@/api/dmCommunity/minichannellink.js";
import provinces from '@/utils/area.js'
let provincesTwo = provinces.map(e=>({
    ...e,
    children:e.children.map(({label,value})=>({label,value}))
}))
provincesTwo.map(fatherE=>{
  fatherE.children.map(e=>{
    if(e.label === '市辖区'){
      e.label = fatherE.value + '市辖区';
      e.value = fatherE.value + '市辖区';
    }
    if(e.label === '省直辖县级行政区划'){
      e.label = fatherE.value + '省直辖县级行政区划';
      e.value = fatherE.value + '省直辖县级行政区划';
    }
  })
})
console.log('provincesTwo',JSON.parse(JSON.stringify(provincesTwo)));
import { queryPage } from '@/api/allinPayMember'
// 协议入口类型集合
let entryTypeMap = [
  { label: '下单页', value: 1},
  { label: '单个订单支付页', value: 2},
  { label: '联合订单支付页', value: 3},
  { label: '申请分销页', value: 4},
]
// 输出单个协议
let createNewAgreement = ({entryType = [], name, url, lklButton = 0} = {})=>[
  {  title: "入口类型", id: "entryType",width: '60%', value: entryType.map(e=>+e), type: 2, option: entryTypeMap, must: true ,multiple: true},
  {  title: "协议名称", id: "name", width: '60%',value: name, type: 1, must: true },
  {  title: '上传文件', id: 'url',type: 11, value: url, showFileMapFlag:true, accept: '.rar,.zip,.doc,.docx,.pdf,.xls,.xlsx', limit: 1, must: true },
  {  title: "拉卡拉分销协议", id: "lklButton", value: lklButton, type: 8, activeValue: 1, inactiveValue: 0 ,hidden:true},
]
export default {
  mixins: [formMixin],
  components: {
    imageView,
    uploadFile,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    paramsData: {
      type: Object,
      default: () => {
        return null;
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      createNewAgreement,
      agreementList2D:[createNewAgreement()],
      file_ctx:this.$env.file_ctx,
      initPostParams: null,
      initliveBroadCastParams: null,
      updateCountPost: 0,
      updateCountLiveBroadcast: 0,
      dialogVisible: false,
      showOptions:false,
      formList: [
        {  title: "服务商logo",  id: "logo", value: '', default: '', type: 10},
        {  title: "服务商名称",  id: "providerName", value: null,  type: 1,must: true},
        {  title: "分账比例", id: "rate",  value: null, type: 12,step:0.01,precision:2,min:0,max:1,must: true},
        { title: "服务城市",id: "provinces", value: [], type: 14,option:provincesTwo ,props: {multiple: true},must: true},
        {  title: "负责人姓名", id: "username",  value: null, type: 1,must: true},
        {  title: "负责人手机", id: "phone",  value: null, type: 1,must: true},
        {  title: "客服手机", id: "contactPhone",  value: null, type: 1,must: true},
        {  title: "简介",id: 'introduction', value: null, option: [], type: 1, inputType:'textarea'},
        {  title: "小程序名称",  id: "appName", value: null,  type: 1,must: true},
        {  title: "云陪诊开关", id: "storeButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        {  title: "云陪诊小程序appid",  id: "cAppid", value: null,  type: 1,must: true,hidden: true},
        {  title: "门店码", id: "storeCode", value: '', type: 10,default: '',hidden: true},
        {  title: "独立小程序开关", id: "appidButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        {  title: "独立小程序appid",  id: "appid", value: null,  type: 1,must: true,hidden: true},
        {  title: "联系客服设置", id: "contactButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        {  title: "使用位置", id: "contactPosition", value: [], type: 2, option: [
          { label: '首页', value: '1'},
          { label: '下单页', value: '2'}
        ], multiple: true, hidden: true, must: true },
        {  title: "客服二维码开关", id: "codeButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        {  title: "客服二维码", id: "codeUrl", value: '', type: 10,default: '',hidden: true, },
        {  title: "小程序在线客服开关", id: "onlineCustomerButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        {  title: '人工导诊设置', type: 15,  value: null,id:'manualButton', option: [{ label: '用户自主选择', value: 2},{ label: '开启人工导诊', value: 1},{ label: '关闭人工导诊', value: 0},], must: true },
        {  title: "保险业务", id: "insureButton", value: 0, type: 15, option: [
          { label: '关闭', value: 0},
          { label: '开启保险（身份证必填)', value: 1},
          { label: '开启保险（身份证选填，填时显示保险)', value: 2}
        ] },
        {  title: "创建联合订单设置", id: "combineButton", value: null, type: 8,activeValue:1,inactiveValue:0},
        {  title: "在线问诊开关", id: "onlineConsultButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        {  title: '在线问诊跳转类型', type: 15,  value: null,id:'linkType', option: [{ label: '内部页面', value: 1},{ label: 'web地址', value: 2},{ label: '外部小程序', value: 3},],hidden: true },
        {  title: '在线问诊跳转链接', type: 1,  value: null,id:'link',hidden: true },
        {  title: "跳转小程序appid", id: "linkAppid",  value: null, type: 1,hidden: true},
        {  title: "线下收款码开关", id: "offlineButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        { title: "", id: "transferOrderTip", type: 24, tipContent: "备注:开启时【陪诊订单-待支付、待派单、待接单、待服务、服务中状态】显示绑定拉卡拉订单操作"},
        {  title: "帖子开关设置", id: "postButton", value: null, type: 8,activeValue:1,inactiveValue:0},
        {  title: "协议设置", id: "agreementButton", value: null, type: 20,activeValue:1,inactiveValue:0},
        {  title: "服务模块", id: "serviceButton", value: 1, type: 8, activeValue: 1, inactiveValue: 0 },
        { title: "", id: "serviceButtonTip", type: 24, tipContent: "备注: 开启或关闭将控制首页【服务模块】显示或隐藏"},
        {  title: "分类模块", id: "classifyButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        { title: "", id: "classifyButtonTip", type: 24, tipContent: "备注: 开启或关闭将控制首页【分类模块】显示或隐藏"},
        {  title: "本地诊所师状态", id: "localDoctorButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        { title: "", id: "localDoctorTip", type: 24, tipContent: "备注: 开启或关闭将控制首页【本地诊师】显示或隐藏"},
        {  title: "本地名医状态", id: "localFamousDoctorButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        { title: "", id: "localFamousDoctorTip", type: 24, tipContent: "备注: 开启或关闭将控制首页【本地名医】显示或隐藏"},
        {  title: "热门医院状态", id: "hotHospitalButton", value: 0, type: 8, activeValue: 1, inactiveValue: 0 },
        {  title: "门店logo", id: "storeLogo", value: null, type: 10, default: '',hidden: true, tip:'建议尺寸 210px * 50px' },
        {  title: "订单二维码海报logo", id: "posterLogo", value: null, type: 10, default: '', tip:'建议尺寸 444px * 731px;建议上传二倍图' },
        {  title: "订单二维码海报", id: "poster", value: null, type: 10, default: '', tip:'建议尺寸 210px * 50px' },
        {  title: "后台菜单logo", id: "backLogo", value: null, type: 10, default: '',tip:'建议尺寸 210px * 50px' },
      ],
      showTreeValueMap:{},
      loadText:true
    };
  },
  async created() {
    let {data:{records}} = await queryPage({size:1000000,pageNum:1});
    records.map((item)=>{
      item.value = item.id
      item.label = item.name
    })
    this.setFormData('tlAccount','option',records)
  },
  inject:['getOptions'],
  computed: {
    storeButtonVal(){
      return this.getFormData('storeButton','value');
    },
    appidButtonVal(){
      return this.getFormData('appidButton','value');
    },
    onlineConsultButtonVal(){
      return this.getFormData('onlineConsultButton','value');
    },
    linkTypeVal(){
      return this.getFormData('linkType','value');
    },
    contactButtonVal(){
      return this.getFormData('contactButton','value');
    },
  },
  watch: {
    agreementList2D:{
      deep:true,
      handler(){
        let agreementList = this.agreementList2D.map(e=>{
          return {entryType:e[0].value,e:e};
        })
        agreementList.forEach(agreement => {
          let currentEmitOptions = agreement.e[3]
          // 判断当前协议是否选择了入口类型为4的申请分销页面
          if(agreement.entryType.includes(4)) return this.$set(currentEmitOptions,'hidden',false)
          this.$set(currentEmitOptions,'hidden',true)
          this.$set(currentEmitOptions,'value',0)
        });
      }

    },
    storeButtonVal(n){
      this.setFormData('storeCode', 'hidden', !n);
      this.setFormData('storeLogo', 'hidden', !n);
      this.setFormData('cAppid', 'hidden',!n);
      n && this.setFormData('cAppid', 'value','wx436b8e65632f880f');
    },
    appidButtonVal(n){
      this.setFormData('appid', 'hidden', !n);
    },
    onlineConsultButtonVal(n){
      console.log('n',n);
      this.setFormData('linkType', 'hidden', !n);
      this.setFormData('link', 'hidden', !n);
    },
    contactButtonVal(n){
      console.log('n',n);
      this.setFormData('contactPosition', 'hidden', !n);
    },
    linkTypeVal(n){
      console.log('n',n);

      this.setFormData('linkAppid', 'hidden', n != 3);
    },
    visible(n) {
      this.dialogVisible = n;
      this.loadText = true
      if (!n) {
        this.formList.map((item) => {
          this.setFormData(item.id,'value', this.getFormData(item.id,'default') || null)
          item.disable = false;
        });
        this.agreementList2D = [createNewAgreement()];
      }
    },
    async paramsData(n){
      if(this.paramsData){
        console.log('this.paramsData',this.paramsData);
        console.log('this.formList',this.formList);
        this.formList.map((item) => {
          let newValue = (this.paramsData[item.id] === undefined) ? this.getFormData(item.id,'default') : this.paramsData[item.id]
          this.setFormData(item.id,'value', newValue)
        });
        // 初始化协议
        let agreementList = this.paramsData.agreementList || [];
        console.log('agreementList',agreementList);

        this.agreementList2D = agreementList.map(e=>{
          e.entryType = e.entryType.split(',');
          return createNewAgreement(e)
        })
        // 初始化时根据codeButton显示二维码上传
        const codeButtonVal = this.paramsData.codeButton || 0;
        this.setFormData('codeUrl', 'hidden', codeButtonVal !== 1);
        // 初始化时根据contactButton显示使用类型选择
        const contactButtonVal = this.paramsData.contactButton || 0;
        this.setFormData('contactPosition', 'hidden', contactButtonVal !== 1);
        // 处理联系客服位置字符串转数组
        if (this.paramsData.contactPosition && typeof this.paramsData.contactPosition === 'string') {
          const positions = this.paramsData.contactPosition.split(',');
          this.setFormData('contactPosition', 'value', positions);
        }
        // 初始化时根据codeButton显示二维码上传
        const storeCodeVal = this.paramsData.storeButton || 0;
        this.setFormData('storeCode', 'hidden', storeCodeVal !== 1);
        this.setFormData('storeLogo', 'hidden', storeCodeVal !== 1);
        if(storeCodeVal === 1){
          let {data:codeData} = await minichannellinkQueryOne({
            businessId:this.paramsData.id,
            businessType:4
          })
          // 如果有则直接返回
          if(codeData.qrcodePath){
            this.codeImg = codeData.qrcodePath;
            this.setFormData('storeCode', 'value', codeData.qrcodePath);
            return;
          }
          let {data} = await insert({
            path:'modules/accompany-doctor/home/<USER>'+this.paramsData.id,
            appid:'wx436b8e65632f880f',
            name:'订单二维码',
            businessType:4,
            businessId:this.paramsData.id,
          })
          this.setFormData('storeCode', 'value', data.qrcodePath);
        }
      }
    }
  },

  methods: {
    // 控制分销协议开关取反
    agreementSwitchChange({val,item},index){
      if(val){
        this.agreementList2D.forEach((agreement,i)=>{
          i!== index && this.$set(agreement[3],'value',0)
        })
      }
    },
    deleteAgreement(index){
      let agreementList2D = JSON.parse(JSON.stringify(this.agreementList2D))
      this.agreementList2D = [];
      this.$nextTick(()=>{
        this.agreementList2D = agreementList2D;
        this.agreementList2D.splice(index,1)
      })
    },
    setFormData(id, key, value) {
      for (let i = 0; i < this.formList.length; i++) {
        if (this.formList[i].id === id) {
          this.$set(this.formList[i],key,value)
          return;
        }
      }
    },

    getFormData(id,key){
      if(key) return JSON.parse(JSON.stringify(this.formList.filter(e=>e.id === id)[0]))[key]
      return JSON.parse(JSON.stringify(this.formList.filter(e=>e.id === id)[0]))
    },

    selectVal(val) {
      if (val.item.id === 'codeButton') {
        const isVisible = val.item.value === 1;
        this.setFormData('codeUrl', 'hidden', !isVisible);
        this.$forceUpdate();  // 新增强制刷新
      }
      if (val.item.id === 'contactButton') {
        const isVisible = val.item.value === 1;
        this.setFormData('contactPosition', 'hidden', !isVisible);
        this.$forceUpdate();  // 强制刷新
      }
    },
    getImgUrl({ url, formData }) {
      console.log('formData',formData);
      this.setFormData(formData.id, 'value',url)
    },
    getFileDetail(item, e) {
      console.log('item',item);
      console.log('e',e);
      this.$set(this.agreementList2D[item.indexKey][2],'value',e ? e.dir : null)
    },
    // 确认
    async confirm() {
      console.log('JSON.parse(JSON.stringify(this.formList))',JSON.parse(JSON.stringify(this.formList)));
      let formData = getFromData(this.formList.filter(e=>!e.hidden))
      if(!formData) return
      // 检测协议
      let agreementList = this.agreementList2D.map(e=>getFromData(e))
       if(!this.checkAgreement(agreementList)) return
      // 开启开关时必须上传二维码
      if (formData.codeButton === 1 && !formData.codeUrl) {
        Message.error('开启客服二维码开关后必须上传二维码图片');
        return;
      }
      // 开启联系客服设置时必须选择使用位置
      if (formData.contactButton === 1 && (!formData.contactPosition || formData.contactPosition.length === 0)) {
        Message.error('开启联系客服设置后必须选择使用位置');
        return;
      }
      // 处理联系客服位置数组转字符串
      if (formData.contactPosition && Array.isArray(formData.contactPosition)) {
        formData.contactPosition = formData.contactPosition.join(',');
      } else if (formData.contactPosition) {
        // 如果是字符串，确保格式正确
        formData.contactPosition = String(formData.contactPosition);
      }
      delete formData.timeMap
      // 处理省市区
      let currentProvince;
      let provinces = formData.provinces.reduce((e,next)=>{
        next.map((nextItem,index)=>{
          if(index === 1){
            if(!currentProvince) currentProvince = next[0];
            if(currentProvince !== next[0]){
              e[index].push('$');
              currentProvince = next[0];
            }
          }
          e[index].push(nextItem)
        })
        return e
      },[[],[]]).map((e,index)=>{
        if(index === 0){
          return [...(new Set(e))].join(',')
        }else{
          return e.join(',')
        }
      })
      formData.province = provinces[0]
      formData.city = provinces[1]
      formData.source = 1;
      formData.postButton = formData.postButton ? 1 : 0;
      formData.serviceButton = formData.serviceButton ? 1 : 0;
      formData.classifyButton = formData.classifyButton ? 1 : 0;
      formData.localDoctorButton = formData.localDoctorButton ? 1 : 0;
      formData.localFamousDoctorButton = formData.localFamousDoctorButton ? 1 : 0;
      formData.hotHospitalButton = formData.hotHospitalButton ? 1 : 0;
      console.log('formData',formData);
      let apiFunc = this.paramsData ? (()=>{formData.id = this.paramsData.id;return getAccompanyproviderUpdate})() : getAccompanyproviderInsert
      formData.manualButton = +formData.manualButton;
      let data = await apiFunc({...formData});
      if(formData.agreementButton) await this.upDateAgreement(data,agreementList)
      Message.success('插入成功')
      this.close(true);
    },
    async upDateAgreement(data,agreementList){
      let currentProviderId = this?.paramsData?.id || data.data
      agreementList = agreementList.map(agreement=>{
        agreement.entryType = agreement.entryType.join(',')
        if(agreement) agreement.providerId = currentProviderId
        return agreement
      })
      let options = {
        agreementList,
        providerId: currentProviderId,
      }
      await agreementAddOrUpdate(options)
    },
    checkAgreement(agreementList){
      console.log('agreementList',agreementList);

      if(agreementList.length === 0) return true
      for (let index = 0; index < agreementList.length; index++) {
        if(!agreementList[index]){
          const currentBox = document.querySelectorAll('.agreement-box')[index];
          currentBox.style.backgroundColor = 'red';
          setTimeout(()=>{
            currentBox.style.backgroundColor = '';
          },500)
          currentBox.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          })
          return false
        }
      }
      return true
    },
    handleClose() {
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          this.close();
        })
        .catch((_) => {});
    },
    close(type) {
      this.$emit("close", type);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content {
  width: 100% !important;
}
.agreementButton{
  display: flex;
  align-items: center;
  column-gap: 40px;
}
.agreement-boxFather{
  background: #F3F4F8;
  padding: 10px;
  border-radius: 10px;
}
.agreement-box{
  margin-bottom: 10px;
  width: 533px;
  transition: background-color 0.5s;
  border-radius: 20px;
  padding: 10px;
  overflow: hidden;
  border: 1px solid #00B484;
  .agreement-title{
    display: flex;
    justify-content: space-between;
  }
}
.HeadPathBox{
  position: relative;
  .HeadPathBoxLoading{
    position: absolute;
    top: 2px;
    left: 26px;
    height: 25px;
    background: white;
    width: 162px;
    text-align: center;
    color: #888;
    font-size: 13px;
  }
}
.botPrompt{
  font-size: 12px;
  color: #606266;
  margin-left: 150px;
}
.form-box {
  display: flex;
  align-items: center;
  font-size: 14px;

  .form-label {
    width: 100px;
  }


}
.d-flex{
  display: flex;
  align-items: center;
}
.load-more {
  text-align: center;
  color: #888;
  font-size: 13px;
  line-height: 34px;
  cursor: pointer;
}
.empty-text {
  padding: 10px 0;
  margin: 0;
  text-align: center;
  color: #999;
  font-size: 14px;
}nmh
.text {
  font-size: 14px;
}
.item {
  padding: 18px 0;
}
.box-card {
  width: 480px;
}

.form-title {
  color: #000;
  font-size: 16px;
  word-break: break-all;
  margin-bottom: 12px;
}

.line {
  padding-top: 24px;
  width: 100%;
  border-top: 1px solid #ccc;
  margin-top: 12px;
}

::v-deep .el-drawer__body {
  padding: 0 24px 24px;
}
</style>
