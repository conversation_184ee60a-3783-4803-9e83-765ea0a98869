/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 * 投放计划管理-订阅消息投放计划
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/wxmintemplateputtasks/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/wxmintemplateputtasks/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/wxmintemplateputtasks/insert`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/wxmintemplateputtasks/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/wxmintemplateputtasks/query/page`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/wxmintemplateputtasks/update`, data)
}

// 列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/wxmintemplateputtasks/query/list`, data);
}



// 查看记录接口api/v1/{{taskId}}
export function getwxmintemplateputtaskslog(data) {
  return requestV1.get(`${prefix}/wxmintemplateputtaskslog/query/one/${data.taskId}`);
}

// 查看记录接口分页api/v1/{{taskId}}
export function getwxmintemplateputtaskslogpage(data) {
  return requestV1.postJson(`${prefix}/wxmintemplateputtaskslog/query/page`,data);
}

// 启动或者取消任务 wxmintemplateputtasks/launch
export function wxmintemplateputtaskslaunch(data) {
  return requestV1.postForm(`${prefix}/wxmintemplateputtasks/launch`, data);
}

// 根据手机号查询openId
export function wxmintemplateputtasksQueryOpenIdByPhone(data) {
  return requestV1.postForm(`${prefix}/wxmintemplateputtasks/queryOpenIdByPhone`, data);
}