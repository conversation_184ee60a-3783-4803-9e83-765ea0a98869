<template>
  <div class="container">


    <el-select v-model="selectVal" filterable placeholder="请选择" class="select" clearable @change="change">
      <el-option
        v-for="item in list"
        :key="item.id"
        :label="item.codeName"
        :value="item.id"
      />
    </el-select>
  </div>
</template>

<script>
import {findByParentId} from '@/api/index'
export default {
  name: "showSysCode",
  props: {
    parentId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      selectVal: null,
      list: []
    }
  },
  created() {
    this.initData();
  },
  methods: {
    change(val) {
      console.log(val);
      this.selectVal = val;
      this.$emit('changeCodeParentId', val)
    },
    initData() {
      findByParentId({parentId:this.parentId}).then((res) => {
        this.list = res.data;
      }).catch((res) => {

      })
    }
  },

}
</script>

<style scoped lang="scss">
.container {

  display: flex;
  width: 387px;
  margin-right: 20px;


  span {
    min-width: 125px;
    line-height: 40px;
    text-align: center;
  }

}
</style>
