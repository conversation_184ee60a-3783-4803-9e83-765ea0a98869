<template>
  <div>
    <el-dialog
      custom-class="nopDialog"
      lock-scroll
      :close-on-click-modal="false"
      :visible.sync="visible"
      title="选择图片"
      width="900px"
      :append-to-body="appendtobody"
      :modal-append-to-body="modalappendtobody"
      :before-close="close"
    >
      <div class="uploadform">
        <!-- <div class="uploadform-left">
          <div class="uploadform-left-c">

            <el-tree
              :data="options"
              node-key="id"
              @node-click="handleNodeClick"
              :expand-on-click-node="false"
            >
              <template #default="{ node, data }">
                <span
                  class="custom-tree-node"
                  style="display: flex; width: 100%"
                >
                  <span style="flex: 1">{{ node.label }}</span>
                  <span style="display: flex; align-items: center">
                    <i
                      class="icon el-icon-edit-outline"
                      @click="showGroup('edit', node)"
                    ></i>
                    <i class="icon el-icon-delete" @click="delNode(node)"></i>
                  </span>
                </span>
              </template>
            </el-tree>
          </div>
          <div class="subup" @click="imgVisible = true">上传</div>
        </div> -->
        <div class="uploadform-right">
          <div class="btns">
            <el-button type="danger" size="mini" @click="moredel"
              >批量移除</el-button
            >
          </div>

          <uploaditem
            ref="uploadRef"
            :picarr="list"
            @query="successFn"
            @remove="removeImageItem"
            :ischecked="true"
            :groupId="groupId"
            :filesize="filesize"
            :maxcount="maxcount"
          />

          <!-- <el-pagination
            :currentPage="currentpage"
            :page-size="pagesize"
            background
            layout="total,prev, pager, next"
            :total="total"
            @current-change="handleCurrentChange"
          ></el-pagination> -->
        </div>
      </div>

      <!-- <template #footer> -->
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="close">取消</el-button> -->
        <el-button type="primary" @click="query" :disabled="disabled">

            <template v-if="!disabled">确定</template>
            <template v-else>
                文件上传中，请稍等
            </template>
        </el-button>
      </span>
      <!-- </template> -->
    </el-dialog>
  </div>
</template>


<script>
import uploaditem from "./uploaditem/index.vue";
export default {
  name: "uploadDialog",
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    // 文件大小上传限制
    filesize: {
      type: Number,
      required: false,
      default: 10, // 10M
    },
    maxcount:{
      type:Number,
      default:100,
    },
    list: {
      type: Array,
      default: function () {
        return [];
      },
    },
    groupId: {
      type: Number,
    },
    appendtobody:{
        type:Boolean,
    },
    modalappendtobody:{
        type:Boolean,
    }
  },
  data() {
    return {
        disabled:false,
      //   avatorArr: [
      //     {
      //       url: "https://poster-1252497236.file.myqcloud.com/1077195279746203648/icon_emotion_del%281202691676879634955%29.png",
      //     },
      //   ],
      //   selectArr:[],
    };
  },
  methods: {
    moredel() {
      this.$refs.uploadRef.moredelete();
    },
    close() {
      if(this.disabled){
        return this.$message({
          type:"error",
          message:"正在上传文件，请稍后"
        })
      }
      this.$emit("close", {});
    },
    query() {
      //   if (this.selectArr.length == 0) {
      //     this.$message({
      //       type: "error",
      //       message: "请选择上传文件",
      //     });
      //   }
        this.disabled = true;
      this.$refs.uploadRef.submit();
    },
    clear() {
      //   this.avatorArr = [];
    },
    successFn(arr) {
      console.log(arr);
      console.log("上传成功");
    
      this.$emit("query", arr);
      this.disabled = false;

      // successInfo({
      //     msg: "上传成功",
      // });
      // that.imgVisible = false;
    },
    removeImageItem(idx) {
      //   this.avatorArr.splice(idx, 1);
      this.$emit("remove", idx);
    },
  },
  watch: {
    visible(n) {

    },
  },
  components: {
    uploaditem,
  },
};
</script>


<style lang="scss" scope>
.btns {
  margin-bottom: 10px;
  display: flex;
}
.subup {
  background: var(--bgcolor, "#17d56b");
  width: 100px;
  height: 30px;
  border-radius: 5px;
  margin: 20px auto 0;
  color: #fff;
  font-size: 16px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 1px 1px 3px rgb(0 0 0 / 20%), -1px -1px 3px rgb(0 0 0 / 30%),
    2px 2px 10px rgb(0 0 0 / 20%) inset;
  cursor: pointer;
}
.uploadform {
  display: flex;
  min-height: 350px;
  .uploadform-left {
    width: 200px;
    border-right: 1px solid #dbdbdb;
    display: flex;
    flex-direction: column;
  }
  .uploadform-left-c {
    padding: 20px 0;
  }
  .uploadform-left-c {
    flex: 1;
  }
  .uploadform-item {
    border: 1px solid #dbdbdb;
    margin-right: 10px;
    display: inline-block;
    padding: 10px;
  }
  .avatorimg {
    width: 100px;
    height: 100px;
  }
  .uploadform-right {
    padding: 10px;
    flex: 1;
  }
  .info {
    line-height: 30px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
  }
}
</style>

<style>
.nopDialog .el-dialog__body {
  padding: 0 20px;
}
</style>