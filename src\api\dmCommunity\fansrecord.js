/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 帖子管理
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/fansrecord/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/fansrecord/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/fansrecord/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/fansrecord/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/fansrecord/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/fansrecord/update`, data)
}

// 修改加v状态
export function updateAddv (data) {
    return requestV1.postForm(`${prefix}/fansrecord/update/addv`, data)
}

// 修改用户状态
export function updateUserStatus (data) {
    return requestV1.postForm(`${prefix}/fansrecord/update/user/status`, data)
}

// 随机粉丝列表
export function getRandomFansList (data) {
    return requestV1.get(`${prefix}/fansrecord/get/random/fans/list`, data)
}

// 根据accountId获取粉丝
export function getFansrecordByAccountid(data) {
    return requestV1.get(`${prefix}/fansrecord/get/fansrecord/by/accountid`, data)
}

// 分页列表 - 用药说明书商户端-新增的分页列表
export function getFansrecordMerchantsPage(data) {
  return requestV1.postJson(`${prefix}/fansrecord/merchants/page`, data);
}

// 分页列表 - 陪诊师商户端-新增的分页列表
export function getAccompanyfansrecordQueryPage(data) {
  return requestV1.postJson(`${prefix}/accompanyfansrecord/query/page`, data);
}
// 分页列表 - 陪诊师商户端-帖子服务商列表
export function getFansrecordProviderPage(data) {
  return requestV1.postJson(`${prefix}/fansrecord/provider/page`, data);
}


