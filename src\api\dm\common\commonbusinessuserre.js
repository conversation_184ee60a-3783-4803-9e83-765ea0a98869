/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 通用业务关联用户表
 */

// 批量删除数据
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/commonbusinessuserre/delete/batch/${data.ids}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/commonbusinessuserre/insert`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/commonbusinessuserre/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/commonbusinessuserre/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/commonbusinessuserre/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
  return requestV1.get(`${prefix}/commonbusinessuserre/query/param`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/commonbusinessuserre/update`, data)
}

// 批量绑定
export function batchUserids(data) {
  return requestV1.postForm(`${prefix}/commonbusinessuserre/batch/userids`, data)
}