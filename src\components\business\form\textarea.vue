<!--form表单的文本框组件-->
<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">
    <el-input
      v-model="form.data.textarea"
      :class="childClass"
      :placeholder="config.placeholder"
      type="textarea"
      maxlength="30"
      show-word-limit
      style=""/>
  </el-form-item>
</template>

<script>
export default {
  name: 'Textarea',
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '文本框',
          name: 'textarea',
          maxlength: '256',
          placeholder: '请填写文本框输入',
          rules: [
            { required: true, message: '请输入文本框输入', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: ''
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    childClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      form: {
        data: {
          textarea: ''
        }
      }
    }
  },

  watch: {
    data: {
      handler(val) {
        this.form.data.textarea = val
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.textarea)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">

</style>
