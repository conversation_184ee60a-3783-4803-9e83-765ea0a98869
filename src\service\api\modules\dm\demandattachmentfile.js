/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 文件报告
 */

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/demandattachmentfile/insert`, data)
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/demandattachmentfile/query/page`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/demandattachmentfile/update`, data)
}

// 根据主键单一查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/demandattachmentfile/query/one`, data)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/demandattachmentfile/delete/one/${data.id}`)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/demandattachmentfile/query/list`, data)
}

// 修改启动状态
export function updateOpenStatus(data) {
  return requestV1.postForm(`${prefix}/demandattachmentfile/update/open/status`, data)
}

// 文件审核报告链接回显的文件表格
export function queryReportPage(data) {
  return requestV1.postJson(`${prefix}/demandattachmentfile/query/report/page`, data)
}

// 获取权限文件集合
export function getAuthFileList(data) {
  return requestV1.get(`${prefix}/demandattachmentfile/get/auth/file/list`, data)
}
