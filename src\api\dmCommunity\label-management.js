/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 药品说明书--品牌方管理
 */

// 标签分页列表
export function getProducttagQueryPage (data) {
  return requestV1.postJson(`${prefix}/producttag/query/page`,data)
}

// 标签新增数据
export function producttagUpdate (data) {
  return requestV1.putJson(`${prefix}/producttag/update`,data)
}
// 标签新增数据
export function producttagInsert (data) {
  return requestV1.postJson(`${prefix}/producttag/insert`,data)
}

// 删除标签
export function producttagDelete (data) {
  return requestV1.deleteForm(`${prefix}/producttag/delete/one/${data}`)
}

// 批量删除标签
export function producttagDeleteBatch (data) {
  return requestV1.deleteForm(`${prefix}/producttag/delete/batch/${data}`)
}
// 根据条件查询标签
export function producttagQueryList (data) {
  return requestV1.get(`${prefix}/producttag/query/list`,data)
}
// 根据单一主键查询
export function producttagQueryOne (data) {
  return requestV1.get(`${prefix}/producttag/query/one`,data)
}