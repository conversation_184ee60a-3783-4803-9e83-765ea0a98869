import requestV1 from '@/common/utils/modules/request'

const prefix = '/sop/api'

/**
 * 物料配置
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/material/add`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/material/queryPage`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/material/query/list`, data);
}

// 根据id查询
export function queryOne (data) {
  return requestV1.get(prefix + '/material/findById', data)
}

// 更新数据
export function update (data) {
    return requestV1.postJson(`${prefix}/material/edit`, data)
}

