<!--省市区选择器-->
<template>
  <el-form-item
    :label="config.label"
    :rules="rules"
    :class="itemClass"
    :prop="config.name">
    <el-cascader
      :options="options"
      v-model="form.data.input"
      size="mini"
    />
  </el-form-item>
</template>

<script>
/**
 *  provinceAndCityData: 省市二级联动
 *  provinceAndCityDataPlus: 省市二级联动(带“全部”选项)
 *  regionData: 省市区三级联动数据（不带“全部”选项）
 *  regionDataPlus: 省市区三级联动数据（带“全部”选项）
 */
// eslint-disable-next-line
import { provinceAndCityData, regionData, provinceAndCityDataPlus, regionDataPlus, CodeToText, TextToCode } from 'element-china-area-data'
// import { oneOf } from '@/common/utils'

export default {
  props: {
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '省市区',
          name: 'address',
          rules: [
            { required: true, message: '省市区地址不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    // type: 1.省 2.省市二级联动 3.省市区三级联动
    type: {
      validator(value) {
        return this.$common.oneOf(value, [1, 2, 3])
      },
      default: 2
    },
    // 组件循环时需要的idx值
    idx: {
      type: Number,
      default: 0
    },
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    data: {
      type: Array,
      required: false,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      selectedOptions: [],
      form: {
        data: {
          input: []
        }
      }
    }
  },
  computed: {
    // 根据type展示相对应的数据
    options() {
      let arr = []
      if (this.config.type === 1) {
        for (const item of provinceAndCityData) {
          if (item.children) {
            const obj = this.$common.deepClone(item)
            obj.children = null
            arr.push(obj)
          }
        }
      } else if (this.config.type === 2) {
        arr = provinceAndCityData
      } else {
        arr = regionData
      }
      return arr
    },
    // 校验规则(如果规则为空则用默认的规则)
    rules() {
      let rule = []
      if (this.config.rules.length === 0) {
        rule = [
          { required: true, message: '省市区地址不能为空', trigger: 'blur' }
        ]
      } else {
        rule = this.config.rules
      }
      return rule
    }
  },
  watch: {
    data: {
      handler(val) {
        this.form.data.input = val
      },
      deep: true
    },
    // 监听到form的数据变化则把config.name和form.data.input和idx传到父组件
    form: {
      handler(val) {
        this.$emit('updateForm', this.config.name, this.form.data.input, this.idx)
      },
      deep: true
    }
  },
  methods: {

  }
}
</script>

<style scoped>

</style>
