/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 文件报告
 */

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/demandattachmentfilereport/insert`, data)
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/demandattachmentfilereport/query/page`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/demandattachmentfilereport/update`, data)
}

// 根据主键单一查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/demandattachmentfilereport/query/one`, data)
}

// 根据主键id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/demandattachmentfilereport/delete/one/${data.id}`)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/demandattachmentfilereport/query/list`, data)
}

// 修改启动状态
export function updateEnableStatus(data) {
  return requestV1.postForm(`${prefix}/demandattachmentfilereport/update/enable/status`, data)
}
