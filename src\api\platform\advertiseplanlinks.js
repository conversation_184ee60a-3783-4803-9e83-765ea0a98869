/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

/**
 *  广告页投放计划链接表
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/advertiseplanlinks/delete/batch/${data.ids}`)
}

// 广告页投放计划-保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/advertiseplanlinks/insert`, data)
}

// 广告页投放计划-根据主键单一查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/advertiseplanlinks/query/one`, data)
}

// 广告页投放计划-更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/advertiseplanlinks/update`, data)
}

// 广告页投放计划-分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/advertiseplanlinks/query/page`,data)
}



