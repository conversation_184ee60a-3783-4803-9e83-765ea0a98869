/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

const prefix = '/dm/api/v1'
// const prefix = '/dm2/api/v1'
const basicFix = '/basics/api/v1'
const manageFix = '/manage/api'
/**
 * 小葫芦陪诊--陪诊订单
 */

// 陪诊订单分页
export function getAccompanybookPage (data) {
    return requestV1.postJson(`${prefix}/accompanybook/query/page`,data)
  }
  // 服务分页列表
  export function serviceQueryPage (data) {
    return requestV1.postJson(`${prefix}/accompanyservice/query/page`,data)
  }
  // 服务批量删除
  export function BatchDeleteService (data) {
    return requestV1.deleteForm(`${prefix}/accompanyservice/delete/batch/${data.ids}`)
  }
  // 服务-单一删除
  export function DeleteOneService (data) {
    return requestV1.deleteForm(`${prefix}/accompanyservice/delete/one/${data.id}`)
  }
  // 套餐保存数据
  export function accompanycomboInsert (data) {
    return requestV1.postJson(`${prefix}/accompanycombo/insert`,data)
  }

  // 套餐更新数据
  export function updateAccompanycomboData (data) {
    return requestV1.putJson(`${prefix}/accompanycombo/update`,data)
  }

  // 套餐主键单一查询
  export function accompanycomboQueryOne (data) {
    return requestV1.get(`${prefix}/accompanycombo/query/one`,data)
  }
  // 套餐主键单一查询
  export function accompanycombouserQueryOne (data) {
    return requestV1.get(`${prefix}/accompanycombouser/query/one`,data)
  }

  // 套餐分页列表
  export function accompanycomboQueryPage (data) {
    return requestV1.postJson(`${prefix}/accompanycombo/query/page`,data)
  }

  // 套餐批量删除
  export function BatchDeleteAccompanycombo (data) {
    return requestV1.deleteForm(`${prefix}/accompanycombo/delete/batch/${data.ids}`)
  }
  // 套餐-单一删除
  export function DeleteOneAccompanycombo (data) {
    return requestV1.deleteForm(`${prefix}/accompanycombo/delete/one/${data.id}`)
  }


  // 根据id查询订单
  export function getAccompanybookOne (data) {
    return requestV1.get(`${prefix}/accompanybook/query/one`,data)
  }
  // 取消订单
  export function accompanybookCancel (data) {
    return requestV1.postJson(`${prefix}/accompanybook/cancel`,data)
  }
  // 完成类型订单退款
  export function accompanybookFinishOrderRefund (data) {
    return requestV1.postJson(`${prefix}/accompanybook/finishOrderRefund`,data)
  }
  // 更新订单
  export function accompanybookUpdateOrder (data) {
    return requestV1.postJson(`${prefix}/accompanybook/updateOrder`,data)
  }
  // 创建订单
  export function createAccompanybook (data) {
    return requestV1.postJson(`${prefix}/accompanybook/book`,data)
  }
  // 创建联合订单
  export function accompanycombineorderCreate (data) {
    return requestV1.postJson(`${prefix}/accompanycombineorder/create`,data)
  }
  // 查询联合订单
  export function accompanycombineorderQueryCombineOrder (data) {
    return requestV1.get(`${prefix}/accompanycombineorder/queryCombineOrder`,data)
  }
  // 创建虚机订单
  export function virtualOrder (data) {
    return requestV1.postJson(`${prefix}/accompanybook/virtualOrder`, data)
  }
  // 派单
  export function accompanybookDispatch (data) {
    return requestV1.postJson(`${prefix}/accompanybook/dispatch`,data)
  }
  // 派单-服务商
  export function accompanybookDispatchProvider (data) {
    return requestV1.postJson(`${prefix}/accompanybook/dispatchProvider`,data)
  }
  // 转单
  export function accompanybookTransfer (data) {
    return requestV1.get(`${prefix}/accompanybook/transfer`,data)
  }
  // 获取订单二维码
  export function accompanybookOrderCode (data) {
    return requestV1.postJson(`${prefix}/accompanybook/orderCode`,data)
  }
  // 完成订单
  export function accompanybookFinish (data) {
    return requestV1.get(`${prefix}/accompanybook/finish`,data)
  }
  // 获取打卡记录
  export function accompanyGetLogList (data) {
    return requestV1.get(`${prefix}/signinlog/get/logList`,data)
  }
  // 更新通用打卡签到记录数据
  export function signinlogUpdate (data) {
    return requestV1.putJson(`${prefix}/signinlog/update`,data)
  }

  /**
   * 小葫芦陪诊--服务管理
   */
    // 根据id查询套餐
  export function getAccompanyserviceOne (data) {
      return requestV1.get(`${prefix}/accompanyservice/query/one`,data)
  }
    // 陪诊服务分页
  export function getAccompanyServicePage(data){
    return requestV1.postJson(`${prefix}/accompanyservice/query/page`,data)
  }

  /**
   * 小葫芦陪诊--陪诊师管理
   */
      // 陪诊师表分页
      export function getAccompanyemployeePage(data){
        return requestV1.postJson(`${prefix}/accompanyemployee/query/page`,data)
      }
      // 陪诊师新增
      export function getAccompanyemployeeInsert(data){
        return requestV1.postJson(`${prefix}/accompanyemployee/insert`,data)
      }
      // 陪诊师更新
      export function getAccompanyemployeeUpdate(data){
          return requestV1.putJson(`${prefix}/accompanyemployee/update`,data)
      }
      // 删除陪诊师
      export function getAccompanyemployeeDelete(data){
          return requestV1.deleteForm(`${prefix}/accompanyemployee/delete/batch/${data}`,)
      }
      // 更换陪诊师
      export function accompanyemployeeChangeEmployee(data){
          return requestV1.postJson(`${prefix}/accompanybook/changeEmployee`,data)
      }

  /**
   * 小葫芦陪诊--陪诊服务商
   */
      // 陪诊服务商分页
      export function getAccompanyproviderPage(data){
        return requestV1.postJson(`${prefix}/accompanyprovider/query/page`,data)
      }
      // 新增服务商
      export function getAccompanyproviderInsert(data){
        return requestV1.postJson(`${prefix}/accompanyprovider/insert`,data)
      }
      // 服务商 指定删除
      export function accompanyproviderDeleteOne (id) {
        return requestV1.deleteJson(`${prefix}/accompanyprovider/delete/one/${id}`)
      }
      // 积分规则 批量删除
      export function accompanyproviderDeleteBatch (ids) {
        return requestV1.deleteJson(`${prefix}/accompanyprovider/delete/batch/${ids}`)
      }
      // 更新服务商
      export function getAccompanyproviderUpdate(data){
        return requestV1.putJson(`${prefix}/accompanyprovider/update`,data)
      }
      // 绑定员工
      export function accompanyproviderBind(data){
        return requestV1.postJson(`${prefix}/accompanyprovideruser/bind`,data)
      }
      // 获取服务商绑定员工
      export function getAccompanyprovideruserUserList (data) {
        return requestV1.get(`${prefix}/accompanyprovideruser/query/userList`,data)
      }
      // 获取员工服务商id
      export function getAccompanyprovideruserProvider (data) {
        return requestV1.get(`${prefix}/accompanyprovideruser/query/provider`,data)
      }
      // 根据id查询服务商
      export function getAccompanyprovideruserQueryOne (data) {
        return requestV1.get(`${prefix}/accompanyprovider/query/one`,data)
      }
      // 财务明细统计
      export function getAccompanyfinanceQueryProvider (data) {
        return requestV1.get(`${prefix}/accompanyfinance/query/provider`,data)
      }
      // 交易流水
      export function getSeparateResultListQuery (data) {
        return requestV1.get(`${prefix}/lkl/open/separate/separateResultListQuery`,data)
      }
  /**
   * 小葫芦陪诊--提现管理
   */
      // 提现分页查询
      export function getAccompanypayoutQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanypayout/query/page`,data)
      }
      // 提现分页查询-服务商平台
      export function getAccompanypayoutQueryProviderPage(data){
        return requestV1.postJson(`${prefix}/accompanypayout/query/providerPage`,data)
      }
      // 财务明细分页-服务商平台
      export function getAccompanyfinanceQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanyfinance/query/page`,data)
      }
      // 交易流水-流水列表
      export function getWorkerNotRunning(data){
        return requestV1.postJson(`${prefix}/accompanybook/query/statisticLog`,data)
      }
      // 交易流水-流水统计
      export function getCodeCoverageStatistics(data){
        return requestV1.postJson(`${prefix}/accompanybook/query/statistic`,data)
      }
      // 交易流水-导出订单
      export async function getStatisticExport(data){
        return requestV1.download(`${prefix}/accompanybook/query/statisticExport`, data, `交易流水订单.xlsx`, 'post',true)
      }
      // 保险订单分页查询
      export function queryInvoicePage(data){
        return requestV1.postJson(`${basicFix}/invoice/query/invoicePage`, data)
      }
      // 提现审核
      export function getAccompanypayoutAudit(data){
        return requestV1.postJson(`${prefix}/accompanypayout/audit`,data)
      }
      // 发起提现
      export function getAccompanypayoutInsert(data){
        return requestV1.postJson(`${prefix}/accompanypayout/insert`,data)
      }
  /**
   * 小葫芦陪诊--数据面板统计
   */
      // 数据统计-陪诊订单统计
      export function getOrderSourceStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/orderSourceStatistic`,data)
      }
      // 数据统计-陪诊师排名Top10
      export function getEmployeeRankStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/employeeRankStatistic`,data)
      }

      // 数据统计-陪诊师性别统计
      export function getEmployeeSexStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/employeeSexStatistic`,data)
      }

      // 数据统计-套餐订单统计
      export function getComboStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/comboStatistic`,data)
      }

      // 数据统计-服务商统计Top10
      export function getProviderRankStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/providerRankStatistic`,data)
      }

      // 数据统计-订单统计
      export function getOrderStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/orderStatistic`,data)
      }

      // 数据统计-帖子阅读数统计
      export function getPostReadCountStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/postReadCountStatistic`,data)
      }

      // 数据统计-收益统计
      export function getIncomeStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/incomeStatistic`,data)
      }

      // 数据统计-服务商统计
      export function getProviderStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/providerStatistic`,data)
      }

      // 数据统计-陪诊师统计
      export function getEmployeeStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/employeeStatistic`,data)
      }
      // 数据统计-帖子统计
      export function getAccompanybookPostStatistic(data){
        return requestV1.postJson(`${prefix}/accompanybook/postStatistic`,data)
      }
      // 返回所有服务商
      export function getAccompanyproviderQueryAll(data){
        return requestV1.get(`${prefix}/accompanyprovider/query/all`,data)
      }
      // 配置信息查询
      export function accompanyconfigQueryConfig(data){
        return requestV1.get(`${prefix}/accompanyconfig/queryConfig`,data)
      }
      // 陪诊课程-观看配置
      export function accompanycourseconfigUpdate(data){
        return requestV1.putJson(`${prefix}/accompanycourseconfig/update`,data)
      }
      // 陪诊课程-观看配置
      export function accompanycourseQueryConfig(data){
        return requestV1.get(`${prefix}/accompanycourseconfig/query/config`,data)
      }
      // 课程分类 ----------
      // 陪诊课程-新增分类
      export function accompanycourseclassifyInsert(data){
        return requestV1.postJson(`${prefix}/accompanycourseclassify/insert`,data)
      }
      // 陪诊课程分类表分页
      export function accompanycourseclassifyQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanycourseclassify/query/page`,data)
      }
      // 更新陪诊课程分类
      export function accompanycourseclassifyUpdate(data){
        return requestV1.putJson(`${prefix}/accompanycourseclassify/update`,data)
      }
      // 根据id查询陪诊课程分类
      export function accompanycourseclassifyQueryOne(data){
        return requestV1.get(`${prefix}/accompanycourseclassify/query/one`,data)
      }
      // 陪诊课程指定id删除课程分类
      export function accompanycourseclassifyDeleteOne(data){
        return requestV1.deleteForm(`${prefix}/accompanycourseclassify/delete/one/${data.id}`)
      }
      // 陪诊课程批量删除课程分类
      export function accompanycourseclassifyDeleteBatch(data){
        return requestV1.deleteForm(`${prefix}/accompanycourseclassify/delete/batch/${data}`)
      }
      // 课程列表 ----------
      // 陪诊课程-课程分页
      export function accompanycourseQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanycourse/query/page`,data)
      }
      // 陪诊课程-新增课程
      export function accompanycourseInsert(data){
        return requestV1.postJson(`${prefix}/accompanycourse/insert`,data)
      }
      // 陪诊课程-编辑课程
      export function accompanycourseUpdate(data){
        return requestV1.putJson(`${prefix}/accompanycourse/update`,data)
      }
      // 陪诊课程-根据id查询课程
      export function accompanycourseQueryOne(data){
        return requestV1.get(`${prefix}/accompanycourse/query/one`,data)
      }
      // 陪诊课程-修改上下架
      export function accompanycourseUpdateState(data){
        return requestV1.postJson(`${prefix}/accompanycourse/updateState`,data)
      }
      // 陪诊课程指定id删除课程列表
      export function accompanycourseDeleteOne(data){
        return requestV1.deleteForm(`${prefix}/accompanycourse/delete/one/${data.id}`)
      }
      // 陪诊课程批量删除课程列表
      export function accompanycourseDeleteBatch(data){
        return requestV1.deleteForm(`${prefix}/accompanycourse/delete/batch/${data}`)
      }
      // 陪诊课程-目录管理列表
      export function accompanycourseDirectoryList(data){
        return requestV1.get(`${prefix}/accompanycourse/directoryList`,data)
      }
      // 陪诊课程-目录管理新增/编辑
      export function accompanycourseDirectoryManage(data){
        return requestV1.postJson(`${prefix}/accompanycourse/directoryManage`,data)
      }
      // 陪诊课程-学习进度分页
      export function accompanycoursestudyQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanycoursestudy/query/page`,data)
      }
      // 陪诊课程-时长分页
      export function accompanycoursestudyLogQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanycoursestudylog/query/page`,data)
      }
      // 陪诊课程-学习统计
      export function accompanycoursestudyLogStudyStatistic(data){
        return requestV1.get(`${prefix}/accompanycoursestudylog/studyStatistic`,data)
      }
      // 陪诊课程-学习统计
      export function accompanycoursestudyLogStudyInsert(data){
        return requestV1.postJson(`${prefix}/accompanycoursestudylog/insert`,data)
      }
      // 城市定价分页查询
      export function accompanycitypriceQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanycityprice/query/page`,data)
      }
      // 城市定价 - 新增城市定价
      export function accompanycitypriceInsert(data){
        return requestV1.postJson(`${prefix}/accompanycityprice/insert`,data)
      }
      // 城市定价 - 根据id查询城市定价
      export function accompanycitypriceQueryOne(data){
        return requestV1.get(`${prefix}/accompanycityprice/query/one`,data)
      }
      // 城市定价 - 更新城市定价
      export function accompanycitypriceUpdate(data){
        return requestV1.putJson(`${prefix}/accompanycityprice/update`,data)
      }
      // 城市定价 - 根据id删除城市定价
      export function accompanycitypriceDeleteOne(data){
        return requestV1.deleteForm(`${prefix}/accompanycityprice/delete/one/${data.id}`)
      }
      // 城市定价 - 批量删除城市定价
      export function accompanycitypriceDeleteBatch(data){
        return requestV1.deleteForm(`${prefix}/accompanycityprice/delete/batch/${data}`)
      }
      // 拉卡拉查询账户余额
      export function lklaccompanyBalanceQuery(data){
        return requestV1.postJson(`${prefix}/lklaccompany/balance/query`,data)
      }
      // 服务商基础配置修改
      export function accompanyproviderUpdateV2(data){
        return requestV1.putJson(`${prefix}/accompanyprovider/updateV2`,data)
      }
      // 导出订单
      export async function accompanybookQqueryExport(data,tabName){
        return requestV1.download(`${prefix}/accompanybook/query/export`, data, `陪诊商户端订单【${tabName}】.xlsx`, 'post',true)
      }
      // 批量删除签到打卡记录
      export function accompanybookDeleteBatch(data){
        return requestV1.deleteForm(`${prefix}/signinlog/delete/batch/${data}`)
      }
      // 批量导入
      export const importAccompanyemployee = env.ctx + '/dm/api/v1/accompanyemployee/import'
      // 克隆订单
      export function accompanybookClone(data){
        return requestV1.get(`${prefix}/accompanybook/clone`,data)
      }
      // 克隆订单2
      export function accompanybookCloneOrder(data){
        return requestV1.postJson(`${prefix}/accompanybook/cloneOrder`,data)
      }
      // 陪诊课程-新增课程资料
      export function accompanycourseinfoInsert(data){
        return requestV1.postJson(`${prefix}/accompanycourseinfo/insert`,data)
      }
      // 陪诊课程-批量新增课程资料
      export function accompanycourseinfoAdd(data){
        return requestV1.postJson(`${prefix}/accompanycourseinfo/add`,data)
      }
      // 陪诊课程-更新课程资料
      export function accompanycourseinfoUpdate(data){
        return requestV1.putJson(`${prefix}/accompanycourseinfo/update`,data)
      }
      // 陪诊课程-批量更新课程资料
      export function accompanycourseinfoBatchUpdate(data){
        return requestV1.postJson(`${prefix}/accompanycourseinfo/batchUpdate`,data)
      }
      // 陪诊课程-获取课程的资料
      export function accompanycourseinfoQueryInfo(data){
        return requestV1.get(`${prefix}/accompanycourseinfo/query/info`,data)
      }
      // 陪诊课程-根据资料id删除
      export function accompanycourseinfoDeleteOne(data){
        return requestV1.deleteForm(`${prefix}/accompanycourseinfo/delete/one/${data.id}`)
      }
      // 陪诊课程-批量删除资料
      export function accompanycourseinfoDeleteBatch(data){
        return requestV1.deleteForm(`${prefix}/accompanycourseinfo/delete/batch/${data}`)
      }

      /**
       * 小葫芦陪诊--患者档案管理
       */
      // 陪诊患者档案分页
      export function accompanypatientQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanypatient/query/page`,data)
      }

      // 新增患者档案
      export function accompanypatientInsert(data){
        return requestV1.postJson(`${prefix}/accompanypatient/insert`,data)
      }

      // 更新患者档案
      export function accompanypatientUpdate(data){
        return requestV1.putJson(`${prefix}/accompanypatient/update`,data)
      }

      // 删除患者档案
      export function accompanypatientDeleteOne(data){
        return requestV1.deleteForm(`${prefix}/accompanypatient/delete/one/${data.id}`)
      }

      // 批量删除患者档案
      export function accompanypatientDeleteBatch(data){
        return requestV1.deleteForm(`${prefix}/accompanypatient/delete/batch/${data}`)
      }

      // 就诊人陪诊记录
      export function accompanyBookQueryRecordPage(data){
        return requestV1.postJson(`${prefix}/accompanybook/query/recordPage`,data)
      }

      // 获取可见陪诊记录ID集合
      export function accompanypatientrecordQuerySeeList(data){
        return requestV1.get(`${prefix}/accompanypatientrecord/query/seeList`,data)
      }

      /**
       * 小葫芦陪诊--陪诊师审核管理
       */
      // 陪诊师表分页查询
      export function getAccompanyemployeeQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanyemployee/query/page`,data)
      }

      // 审核陪诊师
      export function accompanyemployeeAudit(data){
        return requestV1.postJson(`${prefix}/accompanyemployee/audit`,data)
      }

      // 导出陪诊师审核记录
      export async function exportAccompanyEmployee (data) {
        return requestV1.download(`${prefix}/accompanyemployee/export`, data, `陪诊师审核记录.xlsx`, 'post', true)
      }

      // 根据业务id和业务类型查询订单详情
      export function minichannellinkQueryOne(data){
        return requestV1.get(`${prefix}/minichannellink/query/one/business`,data)
      }
      // 分销模板新增
      export function distributorteTemplateInsert(data){
        return requestV1.postJson(`${prefix}/accompanydistributortemplate/insert`,data)
      }
      // 分销模板编辑
      export function distributorteTemplateUpdate(data){
        return requestV1.putJson(`${prefix}/accompanydistributortemplate/update`,data)
      }
      // 分账模板分页
      export function distributorteTemplateQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanydistributortemplate/query/page`,data)
      }
      //  查询当前银行卡号的开户行信息
      export function cardBinInfo(data){
        return requestV1.get(`${basicFix}/merchantfile/cardBinInfo`,data)
      }
      // 分销员（接收方）创建
      export function accompanydistributorInsert(data){
        return requestV1.postJson(`${prefix}/accompanydistributor/insert`,data)
      }
      // 分销员（接收方）编辑
      export function accompanydistributorUpdate(data){
        return requestV1.putJson(`${prefix}/accompanydistributor/update`,data)
      }
      // 分销员分页
      export function accompanydistributorPage(data){
        return requestV1.postJson(`${prefix}/accompanydistributor/query/page`,data)
      }
      // 分销员查询
      export function accompanydistributorQueryOne(data){
        return requestV1.get(`${prefix}/accompanydistributor/query/one`,data)
      }
      // 分销客户分页
      export function accompanydistributorQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanycustomer/query/page`,data)
      }
      // 分销流水分页
      export function accompanydistriburecordQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanydistriburecord/query/page`,data)
      }
      // 审核分销员
      export function accompanydistributorAudit(data){
        return requestV1.postJson(`${prefix}/accompanydistributor/audit`,data)
      }
      // 分销客户分页
      export function accompanycustomerQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanycustomer/query/page`,data)
      }
      // 解绑客户
      export function accompanydistributorcustomerUnbind(data){
        return requestV1.get(`${prefix}/accompanydistributorcustomer/bind`,data)
      }
      // 客户绑定记录分页
      export function accompanydistributorcustomerQueryPage(data){
        return requestV1.postJson(`${prefix}/accompanydistributorcustomer/query/page`,data)
      }
      // 分销员根据id指定删除
      export function DeleteOneAccompanydistributor (data) {
        return requestV1.deleteForm(`${prefix}/accompanydistributor/delete/one/${data.id}`)
      }
      // 服务批量删除
      export function BatchDeleteAccompanydistributor (data) {
        return requestV1.deleteForm(`${prefix}/accompanydistributor/delete/batch/${data.ids}`)
      }
      // 通过电话号码查询就诊人
      export function accompanypatientQueryListByPhone (data) {
        return requestV1.get(`${prefix}/accompanypatient/query/listByPhone`,data)
      }
      // 分销流水金额
      export function accompanydistriburecordAmount (data) {
        return requestV1.postJson(`${prefix}/accompanydistriburecord/query/amount`,data)
      }
      // 绑定协议
      export function bindAgreement (data) {
        return requestV1.get(`${prefix}/accompanydistributor/bindAgreement`, data)
      }
      // 线下单列表
      export function queryOfflinePage (data) {
        return requestV1.postJson(`${prefix}/accompanybook/query/offlinePage`,data)
      }
      // 线下单绑定
      export function bindOfflineOrder (data) {
        return requestV1.get(`${prefix}/accompanybook/bindOfflineOrder`,data)
      }
      // 线下单绑定情况查询
      export function queryHasOrder (data) {
        return requestV1.get(`${prefix}/accompanybook/query/hasOrder`,data)
      }
      // 服务初始化导入
      export function importAccompanyservice(data) {
        return requestV1.uploadFileAxios({
          url: env.ctx + `${prefix}/accompanyservice/import`,
          method: 'post',
          data: data
        })
      }
      // 陪诊横幅初始化
      export function importAccompanybanner(data) {
        return requestV1.uploadFileAxios({
          url: env.ctx + `${prefix}/banner/import`,
          method: 'post',
          data: data
        })
      }
      // 服务分类初始化
      export function importAccompanyserviceclassify(data) {
        return requestV1.uploadFileAxios({
          url: env.ctx + `${prefix}/accompanyserviceclassify/import`,
          method: 'post',
          data: data
        })
      }
      //保险订单退保
      export function accompanyinsureCancel(data) {
        return requestV1.get(`${prefix}/accompanyinsure/cancel`, data)
      }
      // 根据id查询陪诊保险记录
      export function accompanyinsureQueryOne(data) {
        return requestV1.get(`${prefix}/accompanyinsure/query/one`, data)
      }
      // 获取微信授权token
      export function getAuthorizerAccessToken (data) {
        return requestV1.get(`${manageFix}/wx/getAuthorizerAccessToken`,data)
      }
      // 获取微信类目名称信息
      export function getAllCategoryName (data) {
        return requestV1.get(`${manageFix}/wx/getAllCategoryName`,data)
      }
      // 提交代码审核
      export function submitAuditWX (data) {
        return requestV1.postJson(`${manageFix}/wx/wxa/submitAudit`,data)
      }

      /**
       * 服务分类相关接口
       */
      // 服务分类分页查询
      export function accompanyserviceclassifyQueryPage (data) {
        return requestV1.postJson(`${prefix}/accompanyserviceclassify/query/page`,data)
      }

      // 新增服务分类
      export function accompanyserviceclassifyInsert (data) {
        return requestV1.postJson(`${prefix}/accompanyserviceclassify/insert`,data)
      }

      // 编辑服务分类
      export function accompanyserviceclassifyUpdate (data) {
        return requestV1.putJson(`${prefix}/accompanyserviceclassify/update`,data)
      }

      // 批量删除服务分类
      export function accompanyserviceclassifyDeleteBatch (ids) {
        return requestV1.deleteForm(`${prefix}/accompanyserviceclassify/delete/batch/${ids}`)
      }

      // 根据id删除服务分类
      export function accompanyserviceclassifyDeleteOne (id) {
        return requestV1.deleteForm(`${prefix}/accompanyserviceclassify/delete/one/${id}`)
      }
      // 新增或编辑协议
      export function agreementAddOrUpdate (data) {
        return requestV1.postJson(`${prefix}/accompanyprovideragreement/addOrUpdate`,data)
      }
      // 根据id删除协议
      export function agreementAddOrUpdateDeleteOne (data) {
        return requestV1.deleteForm(`${prefix}/accompanyprovideragreement/delete/one/${data.id}`)
      }
      // 套餐订单列表分页
      export function accompanycombouserQueryPage (data) {
        return requestV1.postJson(`${prefix}/accompanycombouser/query/page`,data)
      }
      // 分页查询-陪诊
      export function queryAccompanyLogPage (data) {
          return requestV1.postJson(`${prefix}/minichannellinklog/query/accompanyPage`, data)
      }
      // 数据统计-陪诊
      export function queryStatisticsMerchantAccompanyTotal (data) {
        // return requestV1.postJson(`${prefix}/minichannellinklog/query/statistics/total`, data)
        return requestV1.postJson(`${prefix}/minichannellinklog/query/statistics/accompanyTotal`, data)
      }
      // 根据陪诊id查询医嘱
      export function accompanyadviceQueryAccompany(data) {
        return requestV1.get(`${prefix}/accompanyadvice/query/accompany`, data)
      }

      // 新增医嘱
      export function accompanyadviceInsert(data) {
        return requestV1.postJson(`${prefix}/accompanyadvice/insert`, data)
      }

      // 更新医嘱
      export function accompanyadviceUpdate(data) {
        return requestV1.putJson(`${prefix}/accompanyadvice/update`, data)
      }
      // 更改服务商
      export function changeProvider(data) {
        return requestV1.get(`${prefix}/accompanyprovideruser/changeProvider`, data)
      }
      // 判断用户是否配置了多服务商
      export function accompanymultprovideruserJudgeUser(data) {
        return requestV1.get(`${prefix}/accompanymultprovideruser/judgeUser`, data)
      }