/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 横幅管理
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/outboundcallrecorddetail/delete/batch/${data.ids}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/outboundcallrecorddetail/insert`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/outboundcallrecorddetail/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/outboundcallrecorddetail/query/one`, data)
}

// 分页列表查询代办模板
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/outboundcallrecorddetail/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
  return requestV1.get(`${prefix}/outboundcallrecorddetail/query/param`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/outboundcallrecorddetail/update`, data)
}