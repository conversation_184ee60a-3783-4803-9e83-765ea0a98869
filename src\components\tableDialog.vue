<template>
  <div>
    <template>
      <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="80%"
        center
        top="0.5vh"
        :before-close="handleClose"
      >
        <div class="content">
          <div class="search" ref="search" v-show="noSearch">
            <slot name="search"></slot>
            <div class="main" style="width: 0px;">
              <el-button size="mini" type="primary"  style="margin-left: 30px" @click="initData('search')">查询</el-button>
              <slot name="buttons"/>
            </div>
          </div>

          <div class="table">
            <el-table
              :data="tableData"
              border
              v-loading="loading"
              style="width: 100%"
              
              :max-height="tableHeight"
              @selection-change="handleSelectionChange"
            >
              <slot/>
            </el-table>
          </div>

          <div class="page">
            <el-pagination
              :current-page="data.current"
              :page-sizes="[5,10, 20, 30, 50, 100, 500, 1000]"
              :page-size="data.size"
              layout="sizes, prev, pager, next"
              :total="data.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>

        <div class="footer" style="height: 50px;" v-show="!noShowButton">
          <span slot="footer" class="dialog-footer">
          <el-button size="mini" @click="cancel()">取 消</el-button>
          <el-button size="mini" type="primary"  @click="commit()" v-loading.fullscreen.lock="loading">确 定</el-button>
        </span>
        </div>
      </el-dialog>

    </template>
  </div>
</template>

<script>
import {getTableHeight} from '@/utils'
export default {
  components: {},
  props: {
    dialogVisible: Boolean,
    search: Object,
    tableUrl: String,
    title: String,
    noShowButton: Boolean,
    noSearch: Boolean,
    isInitData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableHeight: null,
      tableData: [],
      loading: false,
      list: [],
      data: {
        size: 10
      },
      selectList: [],
    }
  },
  watch: {
    dialogVisible(value) {
      if (value) {
        if (!this.isInitData) {
          this.initData('search');
        }
        this.$nextTick(() => {
          console.log(this.$refs.search)
          const searchHeight = this.$refs.search.offsetHeight;
          this.tableHeight = getTableHeight(searchHeight) - 120
        });
      }
    },

  },

  created() {

  },
  mounted() {
    console.log('======', !this.isInitData)
    if (!this.isInitData) {
      this.initData();
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.selectList = val;
      this.$emit('handleSelectionChange', val)
    },
    handleClose() {
      this.$emit('cancel', false)
    },
    cancel() {
      // this.dialogVisible=false;
      this.$emit('cancel', false)
    },
    commit() {
      this.$emit('commitData', this.selectList)
    },
    initData(val) {
      if (val) {
        this.data.current = 1;
      }
      this.loading = true;
      const data = this.data;
      data.condition = this.search;
      this.$requestV1.postJson(this.tableUrl, data).then((res) => {
        this.tableData = res.data.records

        const temp = res.data
        this.data.total = temp.total
        this.data.size = temp.size
        this.data.page = temp.page;
        this.loading = false;
      }).catch((res) => {

      })
    },
    handleSizeChange(val) {
      this.data.size = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.data.current = val
      this.initData()
    },
  }
}
</script>

<style lang="scss" scoped>
.content {
  min-height: 50px;
  margin-left: 20px;
  margin-bottom: 19px;
  display: flex;
  flex-direction: column;
  padding: 0px;
}

.search {
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;

}

.footer {
  text-align: center;
}

.page {
  margin-top: 7px;
}

.main-left {
  width: 93px;
  display: inline-block;
}


</style>
