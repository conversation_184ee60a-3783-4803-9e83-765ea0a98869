/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 在线会议-在线会议主体管理
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/meeting/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/meeting/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/meeting/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/meeting/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/meeting/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/meeting/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/meeting/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/meeting/update`, data)
}

// 更新直播状态
export function activityStatusUpdate (data) {
    return requestV1.putForm(`${prefix}/meeting/activity/status/update`, data)
}

// 运营配置保存
export function operateConfigUpdate (data) {
    return requestV1.putForm(`${prefix}/meeting/operate/config/update`, data)
}


// 获取添加事件记录列表
export function querycommoncollectlikesPage (data) {
    return requestV1.postJson(`${prefix}/meeting/query/event/page`, data)
}



// 马甲任务列表查询
export function queryChildPage(data) {
    return requestV1.postJson(`${prefix}/meetingruletask/query/page`, data)
}

// 马甲任务保存数据
export function insertChild (data) {
    return requestV1.postJson(`${prefix}/meetingruletask/insert`, data)
}

// 马甲任务更新数据
export function updateChild (data) {
    return requestV1.putJson(`${prefix}/meetingruletask/update`, data)
}

// 马甲任务根据主键单一查询
export function queryChildOne (data) {
    return requestV1.get(`${prefix}/meetingruletask/query/one`, data)
}


// 
// 马甲任务更新子项用户状态 
export function changeChildStatus (data) {
    return requestV1.get(`${prefix}/meetingruletask/changestatus`, data)
}

// 马甲任务批量更新子项状态 /v1/minichannellinktaskitem/changestatus/batch
export function changeChildStatusBatch (data) {
    return requestV1.postForm(`${prefix}/meetingruletask/changestatus/batch`, data)
}

// 删除指定项 {id}
export function deleteChildOne (data) {
    return requestV1.deleteForm(`${prefix}/meetingruletask/delete/one/${data.id}`)
}

// 用药说明书 - 名医直播历史数据关联
export function postmessageBatchBindingProductIds (data) {
  return requestV1.postForm(`${prefix}/meeting/doc/live/binding`,data)
}

// 科普帖子 根据用户id和任务id查询科普帖子记录
export function postairecordQueryOneByUserId (data) {
  return requestV1.get(`${prefix}/postairecord/query/oneByUserId`,data)
}

// 科普帖子 根据taskId查询科普帖子记录表
export function postairecordQueryListTaskId (data) {
  return requestV1.postForm(`${prefix}/postairecord/query/list/taskId`,data)
}

// 科普帖子 根据taskId查询科普帖子记录表分页接口
export function postairecordQueryListTaskIdPage (data, expandHeaders = {}) {
  return requestV1.postJson(`${prefix}/postairecord/query/list/taskId/page`,data, null, expandHeaders)
}

// 科普任务统计数据
export function postairecordQueryListTaskIdStatistic (data) {
  return requestV1.postJson(`${prefix}/postairecord/query/list/taskId/statistic`,data)
}

export function meetingEventExport(data, fileName = '事件记录.xlsx'){
  return requestV1.download(`${prefix}/meeting/event/export`, data, fileName, 'post',true,{},true)
}

// 科普帖子-批量删除
export function postairecordDeleteBatch (data) {
  return requestV1.deleteForm(`${prefix}/postairecord/delete/batch/${data.ids}`)
}

// 科普帖子-新增数据
export function postairecordInsert (data) {
  return requestV1.postJson(prefix + '/postairecord/insert', data)
}

// 科普帖子-单一查询
export function postairecordQueryOne (data) {
  return requestV1.get(prefix + '/postairecord/query/one', data)
}
// 科普帖子更新
export function postairecordUpdate (data) {
  return requestV1.putJson(prefix + '/postairecord/update', data)
}
// 科普生成接口
export function postairecordGenerate (data) {
  return requestV1.postJson(prefix + '/postairecord/generate', data)
}

// 科普笔记默认回填品种
export function publicmedicinepoolRandomOne(data){
  return requestV1.get(prefix + '/publicmedicinepool/randomOne',data)
}
// 科普笔记选择模板
export function publicmedicinepoolQueryPage(data){
  return requestV1.postJson(prefix + '/publicmedicinepool/query/page',data)
}
// 批量删除
export function meetingruletaskDeleteBatch(data){
  return requestV1.deleteForm(`${prefix}/meetingruletask/delete/batch/${data.ids}`)
}
// 克隆马甲任务配置 
export function meetingruletaskClone(data){
  return requestV1.postForm(`${prefix}/meetingruletask/clone`,data)
}