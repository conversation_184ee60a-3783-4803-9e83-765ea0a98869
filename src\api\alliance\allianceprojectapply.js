import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * 项目申请记录
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/allianceprojectapply/delete/batch/${data.ids}`)
}

// 根据id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/allianceprojectapply/delete/one/${data.id}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/allianceprojectapply/insert`, data)
}

// 分页查询
export function queryList(data) {
  return requestV1.get(`${prefix}/allianceprojectapply/query/list`, data);
}

// 分页查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/allianceprojectapply/query/page`, data);
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/allianceprojectapply/query/one`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/allianceprojectapply/update`, data)
}

// 审核通过
export function auditPass(data) {
  return requestV1.get(`${prefix}/allianceprojectapply/audit/pass/${data.id}`, data)
}

// 审核驳回
export function auditVeto(data) {
  return requestV1.get(`${prefix}/allianceprojectapply/audit/veto/${data.id}`, data)
}

// 导入项目申请
export const applyImport = `${env.ctx}${prefix}/allianceprojectapply/apply/import`

// 获取奔奔申请
export function allianceprovideruserlogBenTaskList(data) {
  return requestV1.get(`${prefix}/allianceprovideruserlog/ben/task/list`, data)
}

