<!--form表单的打分组件-->
<template>
  <el-form-item
    :label="config.label"
    :rules="config.rules"
    :class="itemClass"
    :prop="config.name">
    <el-rate
      v-model="form.data.rate"
      :class="childClass"/>
  </el-form-item>
</template>

<script>
import common from '@/common/utils'
export default {
  name: 'Rate',
  props: {
    // el-form-itemd的class名
    itemClass: {
      type: String,
      required: false,
      default: ''
    },
    // el-rate的class名
    childClass: {
      type: String,
      required: false,
      default: ''
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {
          label: '评分',
          name: 'rate',
          rules: [
            { required: true, message: '请点击评分', trigger: 'blur' }
          ]
        }
      }
    },
    data: {
      type: String,
      required: false,
      default: '0'
    }
  },
  data() {
    return {
      form: {
        data: {
          rate: 0
        }
      }
    }
  },

  watch: {
    data: {
      handler(val) {
        // debugger
        if (this.$validate.isNull(val)) {
          this.form.data.rate = 0
        } else {
          this.form.data.rate = Number(val)
        }
      },
      deep: true
    },
    form: {
      handler(val) {
        this.$emit('updateForm', '' + this.config.name, this.form.data.rate + '')
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">

</style>
