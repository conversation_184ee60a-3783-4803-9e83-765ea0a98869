/* eslint-disable eol-last */
// 表单页面通用mixin
import store from '@/store'
import { Message } from 'element-ui'

export default {
    data() {
        return {
            size: 10,
            current: 1

        }
    },
    watch: {

    },
    beforeMount() {},
    beforeDestroy() {},
    mounted() {

    },
    methods: {
        /**
         * 用于回显表单数据
         * @param {Array} targetArr
         * @param {Object} data
         * @param {Object} config
         * **/
        setFromData(
            data,
            targetArr = this.selectFrom.item,
            config = {
                startTime: 'startTime',
                endTime: 'endTime',
                installType: 'installType',
                installId: 'installId'
            }) {
            for (const i in data) {
                for (const j in targetArr) {
                    if (i === j.id) {
                        targetArr[j].value = data[i]
                    }
                    if (targetArr[j].cName === 'installSelect') {
                        targetArr[j].value = { installType: data[config.installType], installId: data[config.installId] }
                    }
                    if (targetArr[j].cName === 'time') {
                        targetArr[j].value = [data[config.startTime], data[config.endTime]]
                    }
                    if (targetArr[j].cName === 'address') {
                        targetArr[j].value = [data.province, data.city, data.district]
                    }
                }
            }
        },
        /**
         * 获取表单数据组成{key:value}返回
         * @param {Array} data
         * @param {String} key
         * @param {String} vlaue
         * @param {String} must
         * @param {String} title
         * @param {String} sTime
         * @param {String} eTime
         * @returns {Object}
         * **/
        getFromData({
            data,
            key = 'id',
            value = 'value',
            must = 'must',
            title = 'title',
            sTime = 'startTime',
            eTime = 'endTime'
        }) {
            if (!data) {
                Message.error('请传入请求数据')
                return false
            } else {
                const obj = {}
                for (const i in data) {
                    if (data[i][must] && !data[i][value]) {
                        Message.error(`${data[i][title]}不能为空`)
                        return false
                    }
                    obj[data[i][key]] = data[i][value]

                    // 时间选择类型
                    if (data[i].cName === 'datePicker') {
                        if (this.$u.checkType(data[i][value], 'Array')) {
                            obj[sTime] = data[i][value][0]
                            obj[eTime] = data[i][value][1]
                        } else {
                            obj[sTime] = null
                            obj[eTime] = null
                        }
                        delete obj[data[i].id]
                    }
                    // 地址选择类型
                    if (data[i].cName === 'addressSelect') {
                        if (this.$u.checkType(data[i][value], 'Array')) {
                            obj.province = data[i][value][0] ? data[i][value][0] : null
                            obj.city = data[i][value][1] ? data[i][value][1] : null
                            obj.district = data[i][value][2] ? data[i][value][2] : null
                        } else {
                            obj.province = null
                            obj.city = null
                            obj.district = null
                        }
                        delete obj[data[i].id]
                    }
                    // 安装单位选择类型
                    if (data[i].cName === 'installSelect') {
                        if (this.$u.checkType(data[i][value], 'Object')) {
                            obj.installType = data[i].value.installType
                            obj.installId = data[i].value.installId
                        }
                        delete obj[data[i].id]
                    }
                    // if (!data[i].id) break
                }
                for (const i in obj) {
                    if (!obj[i] ||
                        obj[i] === null ||
                        obj[i] === undefined ||
                        obj[i] === 'null' ||
                        obj[i] === 'undefined' ||
                        obj[i] === '') {
                        delete obj[i]
                    }
                }
                return obj
            }
        },
        /**
         * 分页数据匹配数据获取对应值展示在表格中，用于处理分页接口只返回对应id没有返回值，匹配当前已有数组获取对应值
         * @param {String} key
         * @param {Array} arr
         * @param {String} value
         * @param {String} label
         * @return {String | Number}
         * **/
        getText(key, arr, value = 'value', label = 'label') {
            if (!arr) {
                Message.error('请传入对照数组')
                return
            }
            for (const i in arr) {
                if (arr[i][value] === key) {
                    return arr[i][label]
                }
            }
        },
        /**
         * 分页切换方法
         * @param {Number} size
         * @param {Number} current
         * **/
        paginationUpdate({ size, current }) {
            this.size = size
            this.current = current
                // 默认查询方法
            this.search()
        },
        /**
         * 设置数组内指定的值
         * @param {Array} targetArr 目标数组默认this.selectFrom.item
         * @param {any} value
         * @param {String} key
         * **/
        setValue({ targetArr = this.selectFrom.item, value, key = 'value', id }) {
            if (!targetArr) return
            targetArr.map(i => {
                if (i.id === id) {
                    i[key] = value
                }
            })
        },
        /**
         * 获取数组内指定的值
         * @param {Array} targetArr 目标数组默认this.selectFrom.item
         * @param {String} id
         * @param {String} key
         * @returns {any}
         * **/
        getValue({ targetArr = this.selectFrom.item, id, key = 'value' }) {
            if (!targetArr) return
            for (const i in targetArr) {
                if (targetArr[i].id === id) {
                    return targetArr[i][key]
                }
            }
        }

    }
}