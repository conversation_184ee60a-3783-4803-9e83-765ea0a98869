<template>
  <section class="app-main" :class="{ fullScreen: fullScreen }">
    <transition name="fade-transform" mode="out-in">
      <keep-alive>
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'AppMain',
  computed: {
    excludeList() {
      return [
        'rechargeRecord',
        'plan',
        'AddPlan',
        'deviceEDList',
        'bindDeviceInfo',
        'editCommodity',
        'addInstall',
        'editPlan',
        'installationUnit',
        'setAccount',
        'bindDevice',
        'cockpit',
        'groupConfig',
        'setWelcome',
        'pushList',
        'addPushNews',
        'editPushNews',
        'channelBusiness',
        'maintenanceStaff',
        'news',
        'platformAdvertiserRechargeRecord',
        'advertiser',
        'minConfig',
        'widthdrawConfig',
        'customerEdit',
        'editDemandOrder',
        'editAdditionalCharges',
        'customerCenter',
        'customerAdditionalCharges',
        'customerData',
        'autoReply',
        'customerBagWarehouse',
        'customerSharedWarehouse',
        'customerBagWarehouseDetail',
        'customerBagWarehouseOut',
        'customerBagWarehouseIn',
        'customerSharedWarehouseDetail',
        'employee',
        'department',
        'workItem',
        'workOrder',
        'customerList',
        'h5Config',
        'wxDelayAdd',
        'wxDelay',
        'outPacketTypeTotal',
        'addPackageApply',
        'syntaskDetail',
        'syntask',
        'promoteCustomerContract',
        'promoteContract',
        'promotePayInfo',
        'promoteCustomerPayInfo',
        'fansLabel',
        'fanProfile',
        'evaluatePacketConfig',
        'editContractInfo',
        'separateaccountTemUpdateRecord'
        ]
    },
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    },
    ...mapState({
      fullScreen: state => state.app.fullScreen
    })
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 10px 0 0 0;
  background: white;
}

.fixed-header + .app-main {
  // padding-top: 50px;


}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    // padding-top: 84px;
  }
}

@media screen and (max-width: 1505px) and (min-width: 1000px) {
  .hasTagsView {
    .app-main {
      /* 84 = navbar + tags-view = 50 + 34 */
      min-height: calc(100vh - 140px);
    }

    .fixed-header + .app-main {
      // padding-top: 140px;
    }
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
.fullScreen {
  padding-top: 0 !important;
}
</style>
