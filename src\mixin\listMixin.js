/* eslint-disable eol-last */
// 列表页面通用mixin

export default {
  data() {
    return {
      tabHeight: 0,
      tableData: {}, // 分页数据
      loading: false, // 表格加载loading
      selectFrom: [],
      current: 1,
      size: 10,
      selectArr: [] // 选择项
    }
  },
  watch: {

  },
  methods: {
    /**
     * 表格统一选择方法
     * @param {object[]} val 选中项列表 
     */
    select(val) {
      this.selectArr = val
    },
    setFormData(id, key, value, list = this.selectFrom) {
      list.forEach((item, index) => {
        if (item.id === id) {
          list[index][key] = value
        }
      })
    },
    getFormData(id, key, list = this.selectFrom) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].id === id) {
          return list[i][key];
        }
      }
    },
    handleSizeChange(val) {
      this.size = val
      this.search(true)
    },
    handleCurrentChange(val) {
      this.current = val
      this.search(true)
    },
    getEnumText(value, list) {
      const itemType = list.find(item => item.value === value)
      return (itemType && Object.keys(itemType).length) ? itemType.label : ''
    },
    getTabHeight(height) {
      this.tabHeight = height
    },
    /**
     * 列表搜索功能 统一重写search
     * @param {boolean} noreset 是否不重置页码 true不重置 false重置
     */
    search(noreset) {
      // if (!noreset) {
      //   this.size = 10
      //   this.current = 1
      // }
      // this.loading = true
      // const size = this.size
      // const current = this.current

      // const condition = getFromData(this.selectFrom)
      // queryPage({ size, current, condition }).then(res => {
      //   res.data.records = res.data.records.map(item => {
      //     return {
      //       ...item,
      //     }
      //   })
      //   this.tableData = res.data
      // }).then(res => {
      //   this.loading = false
      // }).catch(res => {
      //   this.loading = false
      // })
    }
  }
}