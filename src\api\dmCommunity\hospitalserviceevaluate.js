/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 帖子管理
 */

// 批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/hospitalserviceevaluate/delete/batch/${data.ids}`)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/hospitalserviceevaluate/insert`, data)
}

// 根据多参数进行列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/hospitalserviceevaluate/query/list`, data)
}

// 根据id查询
export function queryOne(data) {
  return requestV1.get(`${prefix}/hospitalserviceevaluate/query/one`, data)
}

// 分页列表
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/hospitalserviceevaluate/query/page`, data);
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/hospitalserviceevaluate/update`, data)
}

// 绑定模板
export function bindTemplate(data) {
  return requestV1.postForm(`${prefix}/hospitalserviceevaluate/bind/template`, data)
}


// 更新状态 /v1/
export function hospitalserviceevaluateopenstatus(data) {
  return requestV1.postForm(`${prefix}/hospitalserviceevaluate/update/open/status`, data)
}


// 获取定评记录 /dm/api/v1/hospitalserviceevaluatelog/query/log/page
export function hospitalserviceevaluatelogpage(data) {
  return requestV1.postJson(`${prefix}/hospitalserviceevaluatelog/query/log/page`, data)
}

// // 字典 /manage/api/hospital/listAllHospital
// export function hospitallistAllHospital(param) {
//   // const url = env.ctx + 'manage/api/hospital/queryList'
//   const url = env.ctx + 'manage/api/installUnit/listAllByInstallType'

//   return request.postJson(url, param)
// }