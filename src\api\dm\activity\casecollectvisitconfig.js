/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 病例征集活动-征集流程配置
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/casecollectvisitconfig/delete/batch/${data.ids}`)
}

// 根据主键id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/casecollectvisitconfig/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/casecollectvisitconfig/add`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`/dm/api/v2/casecollectvisitconfig/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/casecollectvisitconfig/query/page`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/casecollectvisitconfig/update`, data)
}

// 根据病例征集获取节点list 不过滤不生效状态
export function getListBymMainid (data) {
  return requestV1.get(`/dm/api/v1/casecollectvisitconfig/get/list/by/mainid`, data)
}

// 根据病例征集获取节点list 过滤生效状态
export function getListBymMainidV2 (data) {
    return requestV1.get(`/dm/api/v2/casecollectvisitconfig/get/list/by/mainid`, data)
}

// 修改启动状态
export function updateOpenStatus(data) {
    return requestV1.postForm(`${prefix}/casecollectvisitconfig/update/open/status`, data)
}

//获取用户提交详情
export function getCommitDetail(data) {
    return requestV1.get('/dm/api/v1/casecollectvisitconfig/get/commit/detail', data)
}
