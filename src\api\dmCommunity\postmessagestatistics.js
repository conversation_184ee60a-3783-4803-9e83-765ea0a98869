/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 帖子管理-日统计
 */

// 分页列表
// export function queryPage (data) {
//     return requestV1.postJson(`${prefix}/postmessagestatistics/query/page`, data);
// }
export function queryOldPage (data) {
    return requestV1.postJson(`${prefix}/postmessagestatistics/query/page`, data);
}
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/postmessagestatistics/query/page/merchant`, data);
}

// 帖子日统计数查询
// export function queryDailyStatTotal (data) {
//     return requestV1.postJson(`${prefix}/postmessagestatistics/query/daily/stat/total`, data);
// }
export function queryDailyStatOldTotal (data) {
    return requestV1.postJson(`${prefix}/postmessagestatistics/query/daily/stat/total`, data);
}
export function queryDailyStatTotal (data) {
    return requestV1.postJson(`${prefix}/postmessagestatistics/query/daily/stat/merchant/total`, data);
}
