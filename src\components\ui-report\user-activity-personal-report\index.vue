<template>
  <div class="pageContent" v-loading="pageLoading">
    <template v-for="(page, index) in pageContent">
      <div
        class="everyPage"
        :key="index"
        :style="{
          width: pageSize.width + 'px',
          height: pageSize.height + 'px',
        }"
        v-if="!page.authHeight"
      >
        <template v-if="page.type === 'thumb'">
          <thumb
            :pageObject="page.pageObject"
            :updatecount="thumbUpdateCount"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
          >
          </thumb>
        </template>
        <template v-else-if="page.type === 'directory'">
          <directory 
            :pageObject="page.pageObject"
            @updatePageing='updatePageing'
            @success="updateSuccess"
            :pageNumObject='pageNumObject'
            :updatecount="directoryUpdateCount"
          ></directory>
        </template>
      </div>
      <template v-else-if="page.authHeight">
        <information
          :key="index"
          v-if="page.type === 'information'"
          :updatecount="informationUpdateCount"
          :pageObject="page.pageObject"
          @updatePageing='updatePageing'
          :pageNumObject='pageNumObject'
          @success="updateSuccess"
        ></information>
        <invitationRegistration
          :key="index"
          v-else-if="page.type === 'pullNewDetail'"
          :updatecount="pullNewDetailsUpdateCount"
          :pageObject="page.pageObject"
          renderObject="log"
          @success="updateSuccess"
          @updatePageing='updatePageing'
          :pageNumObject='pageNumObject'
        ></invitationRegistration>
        <invitationRegistration
          :key="index"
          v-else-if="page.type === 'kpiPostLikeDetails'"
          :updatecount="kpiPostLikeDetailsUpdateCount"
          :pageObject="page.pageObject"
          renderObject="dianzan"
          @success="updateSuccess"
          @updatePageing='updatePageing'
          :pageNumObject='pageNumObject'
        ></invitationRegistration>
        <invitationRegistration
          :key="index"
          v-else-if="page.type === 'kpiPostCommentDetails'"
          :updatecount="kpiPostCommentDetailsUpdateCount"
          :pageObject="page.pageObject"
          renderObject="common"
          @success="updateSuccess"
          @updatePageing='updatePageing'
          :pageNumObject='pageNumObject'
        ></invitationRegistration>
        <template v-else-if="page.type === 'liveExecution'">
          <liveExecutionTwo
            :key="index"
            :updatecount="liveExecutionUpdateCount"
            :pageObject="page.pageObject"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
            inType='personal-push-report'
            v-if="pagetype === 16"
          ></liveExecutionTwo>
          <liveExecution
            :key="index"
            :updatecount="liveExecutionUpdateCount"
            :pageObject="page.pageObject"
            @success="updateSuccess"
            @updatePageing='updatePageing'
            :pageNumObject='pageNumObject'
            v-else-if="pagetype === 17"
          ></liveExecution>
        </template>
      </template>
    </template>
  </div>
</template>

<script>
import thumb from "@/components/ui-report/personal-push-report/components/thumb.vue";
import directory from "@/components/ui-report/personal-push-report/components/directory.vue";
import information from "@/components/ui-report/personal-push-report/components/information.vue";
import invitationRegistration from "@/components/ui-report/components/invitation-registration.vue";
import liveExecution from "@/components/ui-report/personal-push-report/components/live-execution.vue";
import liveExecutionTwo from "@/components/ui-report/online-promotion-of-personal-reports/components/execution-image.vue";

import commonMixin from "@/components/ui-report/mixins/common.js";
import { imgServer } from "@/api/config";
import { domainURL } from "@/utils/index.js";
import { todotasksGetReportPersonal } from "@/api/todotasks.js";
import { queryOne as queryTodoTaskOne } from "@/api/todotasks";
import { maxSizeReportImageList, noVerticalImageToVisible } from "@/utils/report-error-image.js";
import { getIdentity } from '@/utils/auth'

export default {
  mixins: [commonMixin],
  props: {
    pagetype: {
      type: Number,
      default: 12,
    },
    levelParams: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  components: {
    thumb,
    directory,
    information,
    invitationRegistration,
    liveExecution,
    liveExecutionTwo
  },
  provide() {
    return {
      pageSize: this.pageSize,
      domainUrl: this.domainUrl,
      pageTopBgUrl: "",
      pagetype: this.pagetype,
    };
  },
  data() {
    return {
      maxImageCount: 40,
      tenantId: '-1',
      landscapeToVerticalList: [],
      fixedFieldObject: {
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己
      },
      domainUrl: imgServer + "static/",
      pageContent: [
        {
          text: "封面",
          type: "thumb",
          pageObject: {},
        },
        {
          text: "目录",
          type: "directory",
          pageObject: {},
        },
        {
          text: "基础信息",
          type: "information",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "小葫芦平台邀请注册明细",
          type: "pullNewDetail",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "KPI 帖子点赞明细",
          type: "kpiPostLikeDetails",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "KPI 帖子评论明细",
          type: "kpiPostCommentDetails",
          pageObject: {},
          authHeight: true,
        },
        //
        {
          text: "现场执行",
          type: "liveExecution",
          pageObject: {},
          authHeight: true,
        },
      ],
      pullNewDetailsUpdateCount: 0,
      liveExecutionUpdateCount: 0,
      // targetCount: 5,
      targetCount: 8,
      tenantName: "广州绿葆网络发展有限公司",
      thumbUpdateCount: 0,
      informationUpdateCount: 0,
      kpiPostLikeDetailsUpdateCount: 0,
      kpiPostCommentDetailsUpdateCount: 0,
      userActivityItemId: null,
      userActivityId: null,
      userActivityUserId: null,
      directoryUpdateCount: 0,
    };
  },
  watch: {
    updatecount(n) {
      this.pageLoading = true;
      this.initMethod();
    },
  },
  methods: {
    async initMethod() {
      const res = await todotasksGetReportPersonal(
        {
          id: this.taskId,
        },
        {
          "no-time-manage": 1,
        }
      );
      const taskRes = await queryTodoTaskOne(
        {
          id: this.taskId,
        },
        {
          "no-time-manage": 1,
        }
      );
      const taskData = taskRes.data;
      const data = res.data;
      if(data.todoTasks instanceof Object) {
        let todoTasks = data.todoTasks;
        this.tenantId = todoTasks.tenantId;
        if(noVerticalImageToVisible[this.tenantId] && Array.isArray(noVerticalImageToVisible[this.tenantId])) {
          this.landscapeToVerticalList = noVerticalImageToVisible[this.tenantId];
        } else {
          this.landscapeToVerticalList = [];
        }
      }
      console.log('this.tenantId===',this.tenantId,this.landscapeToVerticalList)
      if (
        Array.isArray(taskData.todoTasksItems) &&
        taskData.todoTasksItems.length !== 0
      ) {
        this.userActivityUserId = taskData.todoTasksItems[0].disposeUserId;
        this.userActivityItemId = taskData.todoTasksItems[0].id;
      }
      this.userActivityId = taskData.businessId;
      let commonObect = {
        tenantName: data.tenantName,
        projectName: this.fixedFieldObject.projectParty,
        startTime: data.startTime,
        endTime: data.endTime,
        userActivityUserId: this.userActivityUserId,
        userActivityItemId: this.userActivityItemId,
        userActivityId: this.userActivityId,
        levelParams: this.levelParams
      };
      console.log("commonObect", commonObect);
      let todoTaskExecutorReportVos = [];
      if (data.todoTaskExecutorReportVos instanceof Object) {
        todoTaskExecutorReportVos = data.todoTaskExecutorReportVos.map(
          (item) => {
            item.unitPrice = this.getPrice(item.unitPrice) + "元";
            item.bonus = this.getPrice(item.bonus) + "元";
            item.totalAmount = this.getPrice(taskData.incentiveFeePrice) + "元";
            item.settlementNum = taskData.performanceCount;
            return item;
          }
        );
      }
      let imageResult = [];
      if (data.todoTaskExecutorLocationReportVos instanceof Object) {
        oneRoot: for (
          let i = 0;
          i < data.todoTaskExecutorLocationReportVos.length;
          i++
        ) {
          let imagePath = data.todoTaskExecutorLocationReportVos[i].imageIds;
          if (imagePath !== "" && imagePath) {
            let arr = imagePath.split(",");
            for (let k = 0; k < arr.length; k++) {
              if (imageResult.length === this.maxImageCount) {
                break oneRoot;
              }
              let url = arr[k];
              if (arr[k] === "") {
                continue;
              }
              let landscapeToVertical = false;
              if(this.landscapeToVerticalList.includes(url)) {
                landscapeToVertical = true;
              }
              imageResult.push({
                url: domainURL(arr[k]) + "?x-oss-process=image/auto-orient,1",
                landscapeToVertical
              });
            }
          }
        }
      }
      // this.signInLogListImage = imageArr;

      let thumbObject = {
        ...commonObect,
      };
      let directoryObject = {};
      let informationObject = {
        ...commonObect,
        userTable: todoTaskExecutorReportVos,
      };
      let pullNewDetailObject = {
        ...commonObect,
      };
      this.pageContent.forEach((item) => {
        switch (item.type) {
          case "thumb":
            thumbObject.parentUuid = item.type;
            item.pageObject = thumbObject;
            break;
          case "directory":
            directoryObject.parentUuid = item.type;
            item.pageObject = directoryObject;
            break;
          case "information":
            informationObject.parentUuid = item.type;
            item.pageObject = informationObject;
            break;
          case "pullNewDetail":
            item.pageObject = {
              ...pullNewDetailObject,
              parentUuid: item.type,
              uuidKey: "pullNewDetail",
            };
            break;
          case "kpiPostLikeDetails":
            item.pageObject = {
              ...pullNewDetailObject,
              parentUuid: item.type,
              uuidKey: "kpiPostLikeDetails",
            };
            break;
          case "kpiPostCommentDetails":
            item.pageObject = {
              ...pullNewDetailObject,
              parentUuid: item.type,
              uuidKey: "kpiPostCommentDetails",
            };
            break;
          case "liveExecution":
            item.pageObject = {
              imageResult: imageResult,
              parentUuid: item.type,
            };
            break;
        }
      });
      await this.$nextTick();
      this.thumbUpdateCount += 1;
      this.informationUpdateCount += 1;
      this.pullNewDetailsUpdateCount += 1;
      this.kpiPostLikeDetailsUpdateCount += 1;
      this.kpiPostCommentDetailsUpdateCount += 1;
      this.liveExecutionUpdateCount += 1;
      this.directoryUpdateCount += 1;
      this.updateSuccess();
    },
  },
};
</script>

<style lang="scss" scoped>
.everyPage {
  overflow: hidden;
  background: #fff;
}
</style>
<style lang="scss">
@import "../css/personal-push-report.scss";
</style>