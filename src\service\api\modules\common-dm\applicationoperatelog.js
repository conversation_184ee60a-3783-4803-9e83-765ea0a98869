/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 用户活动帖子操作流水
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/applicationoperatelogv2/delete/batch/${data.ids}`)
}

// 保存数据-添加
export function insert (data) {
    return requestV1.postJson(`${prefix}/applicationoperatelogv2/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/applicationoperatelogv2/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/applicationoperatelogv2/query/one`, data)
}

// 分页列表
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/applicationoperatelogv2/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/applicationoperatelogv2/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/applicationoperatelogv2/update`, data)
}

// 审核记录
export function audit (data) {
  return requestV1.postForm(`${prefix}/applicationoperatelogv2/auditLog`, data)
}

// 获取明细 
export function useractivityinvitelogQueryPage(data, expandHeaders = {}) {
  return requestV1.postJson(`${prefix}/useractivityinvitelog/query/Log/detail/page`, data, null, expandHeaders);
}

// 获取明细v2
export function useractivityinvitelogQueryPageV2(data, expandHeaders = {}) {
  return requestV1.postJson(`/dm/api/v2/useractivityinvitelog/query/Log/detail/page`, data, null, expandHeaders);
}
