/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 分销记录
 */

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/distributionrecord/delete/batch/${data.ids}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/distributionrecord/insert`, data)
}

// 根据多参数进行列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/distributionrecord/query/list`, data)
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/distributionrecord/query/one`, data)
}

// 分页列表查询代办模板
export function queryPage(data) {
    return requestV1.postJson(`${prefix}/distributionrecord/query/page`, data);
}

// 根据多参数进行单一查询
export function queryParam(data) {
    return requestV1.get(`${prefix}/distributionrecord/query/param`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/distributionrecord/update`, data)
}

// 审核记录
export function audit (data) {
  return requestV1.postForm(`${prefix}/distributionrecord/auditLog`, data)
}