/* eslint-disable eol-last */
import sysInput from '@/components/system/form/input'
import sysTable from '@/components/system/form/table'
import sysSelect from '@/components/system/form/select'
import sysUpload from '@/components/system/form/upload'
import sysPagination from '@/components/system/form/pagination'
import sysCascader from '@/components/system/form/cascader'
import sysDatePicker from '@/components/system/form/datePicker'

import sysAddressSelect from '@/components/system/search/addressSelect'
import sysInstallSelect from '@/components/system/search/installSelect'

import search from '@/components/system/search'
import charts from '@/components/system/charts'


//新增框架
// business --form
import formCheckbox from './business/form/checkbox'
import formColor from './business/form/color'
import formCounter from './business/form/counter'
import formDate from './business/form/date'
import formDatetime from './business/form/datetime'
import formFile from './business/form/file'
// import formHeader from './business/form/header'
// import formImageCrop from './business/form/image-crop'
import formImage from './business/form/image'
import formInput from './business/form/input'
import formNumber from './business/form/number'
import formRadio from './business/form/radio'
import formSelect from './business/form/select'
// import formSort from './business/form/sort'
import formSwitch from './business/form/switch'
import formTag from './business/form/tag'
import formTextarea from './business/form/textarea'
import formRate from './business/form/rate'

// editor
import formEditorImage from './business/form/editor/components/editorImage'
import formEditor from './business/form/editor'

// newly
import formChinaAreaPicker from './business/form/newly/chinaAreaPicker'
import formIdentityCard from './business/form/newly/identityCard'
import formIp from './business/form/newly/ip'
import formMailbox from './business/form/newly/mailbox'
import formPassword from './business/form/newly/password'
import formPhone from './business/form/newly/phone'
import formPostCode from './business/form/newly/postCode'
import formQq from './business/form/newly/qq'
import formTelPhone from './business/form/newly/telPhone'
import formUrl from './business/form/newly/url'

// list
import carousel from './business/list/carousel'
import pagination from './business/list/pagination'

// sys
import sysInfoSelect from './business/sys/sys-select'
import unitEdit from './business/sys/unit-edit'
import unitSearch from './business/sys/unit-search'

// 重写el-Table
import ElTable from './el-table'


export default {
    ElTable,
    sysInput,
    search,
    sysTable,
    sysPagination,
    sysSelect,
    sysInstallSelect,
    sysUpload,
    sysAddressSelect,
    sysCascader,
    sysDatePicker,
    charts,
    //新增框架
    formCheckbox,
    formColor,
    formCounter,
    formDate,
    formDatetime,
    formFile,
    // formHeader,
    // formImageCrop,
    formImage,
    formInput,
    formNumber,
    formRadio,
    formSelect,
    // formSort,
    formSwitch,
    formTag,
    formTextarea,
    formRate,
    formEditorImage,
    formEditor,
    formChinaAreaPicker,
    formIdentityCard,
    formIp,
    formMailbox,
    formPassword,
    formPhone,
    formPostCode,
    formQq,
    formTelPhone,
    formUrl,
    pagination,
    carousel,
    sysInfoSelect,
    unitEdit,
    unitSearch
}
